package com.radiant.urfs.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "BUSINESS_OPERATION_TYPE_TBL")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessOperationTypeTbl {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "BUSINESS_OPERATION_ID")
    private Integer businessOperationId;

    @Column(name = "BUSINESS_OPERATION_TYPE_NAME", nullable = false, length = 255)
    private String businessOperationTypeName;

    @Column(name = "ACTIVE_FLAG", nullable = false, length = 1)
    private String activeFlag;

    @Column(name = "LAST_MODIFIED_DATETIME", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastModifiedDatetime;

    @ManyToOne
    @JoinColumn(name = "LAST_MODIFIED_BY", nullable = false)
    private UserSBTbl lastModifiedBy;

	public Integer getBusinessOperationId() {
		return businessOperationId;
	}

	public void setBusinessOperationId(Integer businessOperationId) {
		this.businessOperationId = businessOperationId;
	}

	public String getBusinessOperationTypeName() {
		return businessOperationTypeName;
	}

	public void setBusinessOperationTypeName(String businessOperationTypeName) {
		this.businessOperationTypeName = businessOperationTypeName;
	}

	public String getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(String activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public UserSBTbl getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(UserSBTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}

}