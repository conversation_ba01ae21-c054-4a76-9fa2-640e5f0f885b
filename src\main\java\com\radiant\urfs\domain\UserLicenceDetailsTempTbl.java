/**
* <AUTHOR> KN394
* <AUTHOR> E-mail:<EMAIL>
* Class Description: UserLicenceDetailsTempTbl.java
*/package com.radiant.urfs.domain;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity

@Table(name = "USER_LICENCE_DETAILS_TEMP_TBL", catalog = "dbo")
public class UserLicenceDetailsTempTbl {

	private int userLicenceDetailsTempId;
	private UserLicenceDetailsTbl userLicenceDetailsId;
	private UserTbl userTbl;
	private String licenceName;
	private String licenceNumber;
	private String registeredAgentName;
	private String companyNumber;
	private LicenseeRelationTbl licenseeRelationId;
	private char activeFlag;
	private Date lastModifiedDatetime;
	private UserTbl lastModifiedBy;
	private String comments;
	private String registeredOfficeAddress;
	private String otherDescription;
	private char authInd;
	private UserTbl authBy;
	private Date authDateTime;
	private String reRegistrationReason;

	public UserLicenceDetailsTempTbl() {

	}

	public UserLicenceDetailsTempTbl(int userLicenceDetailsTempId, UserTbl userTbl, String licenceName,
			 String licenceNumber, String registeredAgentName, String companyNumber,
			LicenseeRelationTbl licenseeRelationId, char activeFlag, Date lastModifiedDatetime, UserTbl lastModifiedBy,
			String comments, String registeredOfficeAddress, String otherDescription) {
		super();
		this.userLicenceDetailsTempId = userLicenceDetailsTempId;
		this.userTbl = userTbl;
		this.licenceName = licenceName;
		this.licenceNumber = licenceNumber;
		this.registeredAgentName = registeredAgentName;
		this.companyNumber = companyNumber;
		this.licenseeRelationId = licenseeRelationId;
		this.activeFlag = activeFlag;
		this.lastModifiedDatetime = lastModifiedDatetime;
		this.lastModifiedBy = lastModifiedBy;
		this.comments = comments;
		this.registeredOfficeAddress = registeredOfficeAddress;
		this.otherDescription = otherDescription;
//		this.relationToLicenseeName=relationToLicenseeName;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "USER_LICENCE_DETAILS_TEMP_ID", unique = true, nullable = false)
	public int getUserLicenceDetailsTempId() {
		return userLicenceDetailsTempId;
	}

	public void setUserLicenceDetailsTempId(int userLicenceDetailsTempId) {
		this.userLicenceDetailsTempId = userLicenceDetailsTempId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "USER_LICENCE_DETAILS_ID")
	public UserLicenceDetailsTbl getUserLicenceDetailsId() {
		return userLicenceDetailsId;
	}

	public void setUserLicenceDetailsId(UserLicenceDetailsTbl userLicenceDetailsId) {
		this.userLicenceDetailsId = userLicenceDetailsId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "USER_ID", nullable = false)
	public UserTbl getUserTbl() {
		return userTbl;
	}

	public void setUserTbl(UserTbl userTbl) {
		this.userTbl = userTbl;
	}

	@Column(name = "LICENCE_NAME", length = 100)
	public String getLicenceName() {
		return licenceName;
	}

	public void setLicenceName(String licenceName) {
		this.licenceName = licenceName;
	}

	

	@Column(name = "LICENCE_NUMBER", length = 50)
	public String getLicenceNumber() {
		return licenceNumber;
	}

	public void setLicenceNumber(String licenceNumber) {
		this.licenceNumber = licenceNumber;
	}

	@Column(name = "REGISTERED_AGENT_NAME", length = 100)
	public String getRegisteredAgentName() {
		return registeredAgentName;
	}

	public void setRegisteredAgentName(String registeredAgentName) {
		this.registeredAgentName = registeredAgentName;
	}

	@Column(name = "COMPANY_NUMBER", length = 100)
	public String getCompanyNumber() {
		return companyNumber;
	}

	public void setCompanyNumber(String companyNumber) {
		this.companyNumber = companyNumber;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LICENSEE_RELATION_ID", nullable = false)
	public LicenseeRelationTbl getLicenseeRelationId() {
		return licenseeRelationId;
	}

	public void setLicenseeRelationId(LicenseeRelationTbl licenseeRelationId) {
		this.licenseeRelationId = licenseeRelationId;
	}

	@Column(name = "ACTIVE_FLAG", length = 1)
	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_MODIFIED_DATETIME", nullable = false, length = 23)
	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LAST_MODIFIED_BY", nullable = false)
	public UserTbl getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(UserTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}

	@Column(name = "COMMENTS", length = 600)
	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	@Column(name = "REGISTERED_OFFICE_ADDRESS", length = 1000)
	public String getRegisteredOfficeAddress() {
		return registeredOfficeAddress;
	}

	public void setRegisteredOfficeAddress(String registeredOfficeAddress) {
		this.registeredOfficeAddress = registeredOfficeAddress;
	}

	@Column(name = "OTHER_DESCRIPTION", length = 100)
	public String getOtherDescription() {
		return otherDescription;
	}

	public void setOtherDescription(String otherDescription) {
		this.otherDescription = otherDescription;
	}

	@Column(name = "AUTH_IND")
	public char getAuthInd() {
		return authInd;
	}

	public void setAuthInd(char authInd) {
		this.authInd = authInd;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "AUTH_BY")
	public UserTbl getAuthBy() {
		return authBy;
	}

	public void setAuthBy(UserTbl authBy) {
		this.authBy = authBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "AUTH_DATE_TIME", nullable = false)
	public Date getAuthDateTime() {
		return authDateTime;
	}

	public void setAuthDateTime(Date authDateTime) {
		this.authDateTime = authDateTime;
	}

	@Column(name = "REREGISTRATION_REASON")
	public String getReRegistrationReason() {
		return reRegistrationReason;
	}

	public void setReRegistrationReason(String reRegistrationReason) {
		this.reRegistrationReason = reRegistrationReason;
	}

}
