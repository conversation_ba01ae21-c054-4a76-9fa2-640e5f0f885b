package com.radiant.urfs.util;

import java.io.InputStream;
import java.util.Properties;

import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 *
 */
public class ResourceUtil {
	private static final Logger log = Logger.getLogger(ResourceUtil.class);

	/**
	 *
	 * @return
	 */
	public static Properties getEnvSpecificProperties() {
		Properties props = new Properties();
		try {
			InputStream input = null;
			String filename = "application.properties";
			input = ResourceUtil.class.getClassLoader().getResourceAsStream(filename);
			props.load(input);
			String envPropsFileName = "application" + "-" + props.getProperty("spring.profiles.active") + ".properties";
			log.info("Environment Props fileName :" + envPropsFileName);
			input = ResourceUtil.class.getClassLoader().getResourceAsStream(envPropsFileName);
			props.load(input);
		} catch (Exception e) {
			log.error(e);
		}
		return props;
	}

	/**
	 *
	 * @param propName
	 * @return
	 */
	public static String[] getCSPropertyAsArray(String propName) {
		Properties props = getEnvSpecificProperties();
		String propertyCSString = props.getProperty(propName);

		if (propertyCSString == null)
			return new String[] {};

		String[] propertyArray = propertyCSString.split(",");

		return propertyArray;
	}

}
