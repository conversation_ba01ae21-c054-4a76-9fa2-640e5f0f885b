package com.radiant.urfs.domain;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "PAYPAL_TRANSACTIONS_TBL")
public class PaypalTransaction {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PAYPAL_TRANSACTIONS_ID", nullable = false)
    private Integer paypalTransactionsId;

    @Column(name = "PAYPAL_CONFIRMATION_NUMBER", nullable = false, length = 50)
    private String paypalConfirmationNumber;

    @Column(name = "PAYPAL_TRANSACTION_AMT", nullable = false, precision = 18, scale = 0)
    private BigDecimal paypalTransactionAmt;
    
  
    @Column(name = "PAYPAL_TRANSACTION_DATETIME", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date paypalTransactionDatetime;

    @Column(name = "PAYPAL_MESSAGE", length = 255)
    private String paypalMessage;

    
    @Column(name = "COMPANY_NAME", length = 255)
    private String companyName;

    @Column(name = "LAST_MODIFIED_DATETIME", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastModifiedDatetime;

    @ManyToOne
    @JoinColumn(name = "LAST_MODIFIED_BY", nullable = false)
    private UserSBTbl lastModifiedBy;


    @Column(name = "USER_ID")
    private Integer userId;

    // Getters and Setters
    public Integer getPaypalTransactionsId() {
        return paypalTransactionsId;
    }

    public void setPaypalTransactionsId(Integer paypalTransactionsId) {
        this.paypalTransactionsId = paypalTransactionsId;
    }

    public String getPaypalConfirmationNumber() {
        return paypalConfirmationNumber;
    }

    public void setPaypalConfirmationNumber(String paypalConfirmationNumber) {
        this.paypalConfirmationNumber = paypalConfirmationNumber;
    }

    public BigDecimal getPaypalTransactionAmt() {
        return paypalTransactionAmt;
    }

    public void setPaypalTransactionAmt(BigDecimal paypalTransactionAmt) {
        this.paypalTransactionAmt = paypalTransactionAmt;
    }

  

    public String getPaypalMessage() {
        return paypalMessage;
    }

    public void setPaypalMessage(String paypalMessage) {
        this.paypalMessage = paypalMessage;
    }

  

  
    public Date getPaypalTransactionDatetime() {
		return paypalTransactionDatetime;
	}

	public void setPaypalTransactionDatetime(Date paypalTransactionDatetime) {
		this.paypalTransactionDatetime = paypalTransactionDatetime;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public UserSBTbl getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(UserSBTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}

	public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
    
    
}
