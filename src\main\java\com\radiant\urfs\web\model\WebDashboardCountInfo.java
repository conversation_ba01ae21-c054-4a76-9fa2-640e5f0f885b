package com.radiant.urfs.web.model;

public class WebDashboardCountInfo {

	private int noOfUsersRegistered;
	private int noOfCompaniesPending;
	private int noOfCompaniesApproved;
	private int noOfCompaniesRejected;
	
	public int getNoOfUsersRegistered() {
		return noOfUsersRegistered;
	}
	public void setNoOfUsersRegistered(int noOfUsersRegistered) {
		this.noOfUsersRegistered = noOfUsersRegistered;
	}
	public int getNoOfCompaniesPending() {
		return noOfCompaniesPending;
	}
	public void setNoOfCompaniesPending(int noOfCompaniesPending) {
		this.noOfCompaniesPending = noOfCompaniesPending;
	}
	public int getNoOfCompaniesApproved() {
		return noOfCompaniesApproved;
	}
	public void setNoOfCompaniesApproved(int noOfCompaniesApproved) {
		this.noOfCompaniesApproved = noOfCompaniesApproved;
	}
	public int getNoOfCompaniesRejected() {
		return noOfCompaniesRejected;
	}
	public void setNoOfCompaniesRejected(int noOfCompaniesRejected) {
		this.noOfCompaniesRejected = noOfCompaniesRejected;
	}
	
}
