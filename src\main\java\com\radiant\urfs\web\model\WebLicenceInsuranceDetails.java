package com.radiant.urfs.web.model;

import java.util.Date;

public class WebLicenceInsuranceDetails {

	private int licenceInsuranceDetailsId;
	private int licenceDetailsId;
	private String nameInsurer;
	private String levelCover;
	private String levelExcessPayable;
	private String territorialLimits;
	private String reinstatementDetails;
	private char activeFlag;
	private Date lastModifiedDatetime;
	private int lastModifiedBy;
	
	public int getLicenceInsuranceDetailsId() {
		return licenceInsuranceDetailsId;
	}
	public void setLicenceInsuranceDetailsId(int licenceInsuranceDetailsId) {
		this.licenceInsuranceDetailsId = licenceInsuranceDetailsId;
	}
	public int getLicenceDetailsId() {
		return licenceDetailsId;
	}
	public void setLicenceDetailsId(int licenceDetailsId) {
		this.licenceDetailsId = licenceDetailsId;
	}
	public String getNameInsurer() {
		return nameInsurer;
	}
	public void setNameInsurer(String nameInsurer) {
		this.nameInsurer = nameInsurer;
	}
	public String getLevelCover() {
		return levelCover;
	}
	public void setLevelCover(String levelCover) {
		this.levelCover = levelCover;
	}
	public String getLevelExcessPayable() {
		return levelExcessPayable;
	}
	public void setLevelExcessPayable(String levelExcessPayable) {
		this.levelExcessPayable = levelExcessPayable;
	}
	public String getTerritorialLimits() {
		return territorialLimits;
	}
	public void setTerritorialLimits(String territorialLimits) {
		this.territorialLimits = territorialLimits;
	}
	public String getReinstatementDetails() {
		return reinstatementDetails;
	}
	public void setReinstatementDetails(String reinstatementDetails) {
		this.reinstatementDetails = reinstatementDetails;
	}
	public char getActiveFlag() {
		return activeFlag;
	}
	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}
	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}
	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}
	public int getLastModifiedBy() {
		return lastModifiedBy;
	}
	public void setLastModifiedBy(int lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}
	
	
}
