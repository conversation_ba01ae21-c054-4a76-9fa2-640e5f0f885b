/*
 * package com.radiant.urfs.exception;
 * 
 * public class FiduciaryException extends Exception {
 * 
 * private static final long serialVersionUID = 6057638889793569346L;
 * 
 * public FiduciaryException() { super(); }
 * 
 * public FiduciaryException(String message, Throwable cause, boolean
 * enableSuppression, boolean writableStackTrace) { super(message, cause,
 * enableSuppression, writableStackTrace); }
 * 
 * public FiduciaryException(String message, Throwable cause) { super(message,
 * cause); }
 * 
 * public FiduciaryException(String message) { super(message); }
 * 
 * public FiduciaryException(Throwable cause) { super(cause); }
 * 
 * 
 * 
 * }
 */