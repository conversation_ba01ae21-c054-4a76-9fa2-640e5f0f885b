package com.radiant.urfs.common;

public class Constants {

	public static final char ACTIVE = 'Y';
	public static final char ACTIVE_Flag = 'Y';
    public static final char UNREGISTERED = 'U';
	public static final char INACTIVE = 'N';
	public static final String ADD = "Add";
	public static final String EDIT = "Edit";
	public static final String VIEW = "View";
	public static final String APPROVAL = "Approval";
	public static final String DELETE = "Delete";
	public static final String OK = "OK";
	public static final String BAD_REQUEST = "Bad Request";
	public static final String SUCCESS = "SUCCESS";
	public static final String FAIL = "FAIL";
	public static final String DATE_FORMAT = "dd/MM/yyyy";
	public static final String AML_CFT = "AMLCFT";
	public static final String REGISTRATION = "Registration";
	public static final String FIDUCIARY = "Fiduciary";
	public static final String PROCESS_DEFINITION_KEY = "radiantAppProcess";
	public static final String USER_REGISTRATION_PROCESS = "userRegistration";
	public static final String USER_REGISTRATION_PARTIAL_APPROVAL = "P-Approved";
	//public static final String USER_REGISTRATION_PARTIAL_REJECT = "P-Rejected";
	public static final String USER_REGISTRATION_PARTIAL_REJECT = "Pending";
	public static final String ADMIN = "Admin";
	public static final String USER = "User";
	public static final String STATUS_SUBMIT = "Submitted";
	public static final String STATUS_RESUBMIT = "Re-Submitted";
	public static final String STATUS_DRAFT = "Draft";
	public static final String APPROVED = "Approved";
	public static final String REJECTED = "Rejected";
	public static final char APPROVE = 'A';
	public static final char REJECT = 'R';
	public static final char PENDING = 'P';
	public static final char LOGIN = 'l';
	public static int value;
	public static long JWT_EXPIRATION;
	public static final String NA = "N/A";
	public static final String OTHER = "Other";
	public static final String PENALITY_TYPE_A = "A";
	public static final String PENALITY_TYPE_B = "B";
	public static final String PENALITY_TYPE_C = "C";
	//public static final String NONE="NONE";
	
	public static class User {
		public static final String USER_NAME_NOT_EXSISTS = "Username does not exist";
		public static final String EMAIL_NOT_EXSISTS = "EmailAddress does not exist";
		public static final String USER_NOT_EXSISTS = "User does not exist";
		public static final String USER_NAME_EXISTS = "Username exist";
		public static final String COMPANY_NUMBER_EXISTS = "Company Number already exist";
		public static final String COMPANY_NUMBER_NOT_EXISTS = "Company Number is empty";
		public static final String REGISTERED_OFFICE_ADDRESS_NOT_EXISTS = "Registered Office Address is empty";
		public static final String LICENCE_TYPE_NOT_EXISTS = "Licence type is empty or invalid";
		public static final String LOGIN_SUCCESSFUL = "Login Successfull";
		public static final String LOGIN_FAILED = "Login Failed";
		public static final String PAYMENT_DETAILS = "Payment Details";
		public static final String REGISTRATION_SUCCESS = "User registration submitted successfully and to be approved by BVI-FSC";
		public static final String REGISTRATION_DONE = "User registration submitted successfully";
		public static final String RE_REGISTRATION_SUCCESS = "User re-registration submitted successfully and to be approved by BVI-FSC";
		public static final String PENDING_COMPANY = "An application for registration of this company is currently pending for another user. Your application for this company/licensee cannot be processed.  If you wish to register this company, <NAME_EMAIL> for assistance.";
		public static final String REGISTRATION_SUCCESS_MAIL_FAILURE = "User registration submitted successfully and to be approved by BVI-FSC. Failed to send the Email.";
		public static final String REGISTRATION_APPROVE_FAIL = "User registration Approval failed. Please contact administrator";
		public static final String REGISTRATION_PENDING = "User registration Pending Waiting for Approval";
		public static final String AMMENDMENTS_PENDING = "Your changes have been submitted and are pending approval. ";
		public static final String AMMENDMENTS_NOT_FOUND = "The details you are trying to update are pending for approval.admin has approve.";
		public static final String USER_REGISTRATION_APPROVE_MAIL_SUCCESS = "User Registration approved and Email Sent Successfully.";
		public static final String USER_REGISTRATION_APPROVE_MAIL_FAIL = "User Registration approved and failed to send the Email.";
		public static final String USER_REGISTRATION_REJECT_MAIL_SUCCESS = "User Registration rejected and Email Sent Successfully.";
		public static final String USER_REGISTRATION_REJECT = "User Registration rejected Successfully.";
		public static final String USER_PENDING_SATUS = "Registration for this company is currently pending. Please choose another company to proceed.";
		public static final String NO_PENDING_SATUS = "No pending registration for this company";
		public static final String USER_REGISTRATION_REJECT_MAIL_FAIL = "User Registration rejected and failed to send the Email.";
		public static final String REGISTRATION_REJECTED = "User Registration is rejected";
		public static final String CHANGE_PASSWORD_FAILURE = "Error Changing Password. Please Contact Administrator";
		public static final String CHANGE_PASSWORD_SUCCESS = "Password changed successfully. Details sent to your registered E-mail address.";
		public static final String CHANGE_PASSWORD_SUCCESS_MAIL_FAIL = "Password changed successfully. Error in sending the details to E-mail.";
		public static final String INVALID_PASSWORD = "Both passwords are equal. Please change new password";
		public static final String FORGOT_PASSWORD_SUCCESS = "Email Sent. Please check your Email for password.";
		public static final String REGISTRATION_ALREADY_EXISTS = "Registration has already been submitted for this company/license and is awaiting approval by FSC. Please reach out to AMLCFT in case of any issues.";
		public static final String FORGOT_PASSWORD_FAILURE = "Failed to send the Email. Please contact Administrator";
		public static final String LICENSEE_NAME_EXISTS = "Licensee Name already exist";
		public static final String LICENSEE_NAME_NOT_EXISTS = "Licensee Name does not exist";
		public static final String OLD_PASSWORD_MATCH = "Old password matched";
		public static final String OLD_PASSWORD_NOT_MATCH = "Old password  not matched";
		public static final String FORGOT_PASSWORD_LINK_MAIL_SUCCESS  ="We have sent a reset password link to your registered E-mail. Please check.";
		public static final String ADD_NEW_LICENSEE_AML_SUCCESS = "New licensee added successfully and to be approved by BVI-FSC";
		public static final String ADD_NEW_LICENSEE_AMLCFT_SUCCESS = "New licensee added successfully and approved by BVI-FSC";
		public static final String LICENCE_NUMBER_EXISTS = "Licence Number already exist";
		public static final String COMPANY_DETAILS_UPDATED = "Your company details are updated";
		public static final String LICENCE_NUMBER_EXISTS_AND_DETAILS_UPDATED = "Licence Number already exists and the details are updated for the same";
		public static final String LICENCE_NUMBER_NOT_EXISTS = "Licence Number is empty";
		public static final String LICENCE_NAME_NOT_EXISTS = "Licence Name is empty";
		public static final String REGISTER_AGENT_NAME_NOT_EXISTS = "Registered Agent Name is empty";
		public static final String USER_RELATION_NOT_EXISTS = "User Relationship Name is empty";
		public static final String LICENCE_NUMBER_NOT_MATCHING = "Licence Number already exists and doesn't match with the Company Number provided";
		public static final String COMPANY_NUMBER_INVALID = "Company Number is Invalid";
		public static final String LICENCE_NUMBER_INVALID = "Licence Number is Invalid";
		public static final String SUCCESSFUL_APPROVED = "Successfully submitted and Approved ";
		public static final String SUCCESSFUL = "Successfully Approved ";
		public static final String OTHER_DESCRIPTION_REQUIRED = "Description is required for Other Relationship to Licence";
		public static final String UPDATE_DETAILS = "Personal Details Updated Successfully";
		public static final String PERSONAL_DETAILS = "Personal Details not Exits";
		public static final String EXTENSION_DAYS = "Return Extension Submitted Successfully";
		public static final String SUPER_USER = "Roles";
		public static final String USER_DETAILS_SENT = "User Created Successfully and Email Sent Successfully.";
		public static final String USER_DETAILS_NOT_SENT = "User Created Successfully and Email Sending Failed.";
		public static final String COMPANY_HAS_HISTORY= "Company has no History.";
		public static final String COMPANY_DETAILS_NOT_EXISTS= "Company details not exists.";
		public static final String ADD_INTERNAL_USERS = "Internal Users Added Successfully";
		public static final String PROVIDE_RELATION_TO_LICENCE = "Please Provide a Relationship to Licensee";
		public static final String USER_PENDING_APPROVAL = "User is pending for Approval";
		public static final String USER_INACTIVE = "User account is inactive or access has been rejected.";


		
		

		
		
	}
	public static class FiduciaryLicence {
		public static final String LICENCE_EXSISTS = "Licence number already exist";
		public static final String LICENCE_ADDED_SUCCESSFULLY = "Licence added successfully";
		public static final String INVALID_LICENCE_ID = "Invalid Licence id";
		public static final String LICENCE_DELETED_SUCCESSFULLY = "Licence deleted successfully";
		public static final String LICENCE_UPDATED_SUCCESSFULLY = "Licence updated successfully";
		public static final String LICENCE_COMPANY_ADD_OR_UPDATE_FAILED = "Licence company add/upadte is failed";
		public static final String LICENCE_STAFFING_ADD_OR_UPDATE_FAILED = "Licence staffing  add/upadte is failed";
		public static final String LICENCE_INSURANCE_ADD_OR_UPDATE_FAILED = "Licence Insurance  add/upadte is failed";
		public static final String LICENCE_DECLERATION_ADD_OR_UPDATE_FAILED = "Licence Decleration  add/upadte is failed";
	}
	
	public static class AmlCftReturn{
		public static final String SUBMIT_SUCCESS = "AML/CFT Annual Returns submitted successfully";
		public static final String SUBMIT_EXISTS = "AML/CFT Annual Returns previously exists";
		public static final String DRAFT_SUBMIT_SUCCESS = "AML/CFT Annual Returns draft submitted successfully";
		public static final String INVALID_LICENCE_NUMBER = "Invalid Licence Number";
		public static final String FILE_UPLOAD_FAILURE = "Error while uploading file. Please contact administrator";
		public static final String COMPANY_NUMBER_REQUIRED = "Please enter the Company Number";
		//public static final String INVALID_COMPANY_NUMBER = "Invalid Company Number";
		public static final String FILE_UPLOADED = "Already one file is uploaded against this Company Number.";
		public static final String FILE_NOT_UPLOADED = "No file is uploaded against this Company Number.";
		public static final String COMPANY_NUMBER_NOT_APPROVED = "This company number is not available or approved";
		public static final String COMPANY_NUMBER_APPROVED = "This company number is approved";
		public static final String LICENCE_NUMBER_NOT_APPROVED = "This licence number is not available or approved";
		public static final String LICENCE_NUMBER_APPROVED = "This licence number is approved";
		public static final String ONLINE = "Online";
		public static final String EXCEL_FORM = "Excel Import";
		public static final String LINK_USER_FAILURE = "Company Number Not Linked to any User";
		public static final String LINK_USER = "Company/Licence is already registered. Please provide the reason for reregistration";
	}
}
