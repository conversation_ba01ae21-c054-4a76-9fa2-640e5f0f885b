package com.radiant.urfs.domain;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "LICENSEE_RELATION_TBL", catalog = "dbo")
public class LicenseeRelationTbl {

	private int licenseeRelationId;
	private String relationToLicensee;
	private char activeFlag;
	private Date lastModifiedDatetime;
	private UserTbl lastModifiedBy;
	
	public LicenseeRelationTbl() {}
	
	public LicenseeRelationTbl(int licenseeRelationId, String relationToLicensee, char activeFlag,
			Date lastModifiedDatetime, UserTbl lastModifiedBy) {
		super();
		this.licenseeRelationId = licenseeRelationId;
		this.relationToLicensee = relationToLicensee;
		this.activeFlag = activeFlag;
		this.lastModifiedDatetime = lastModifiedDatetime;
		this.lastModifiedBy = lastModifiedBy;
	}
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "LICENSEE_RELATION_ID", unique = true, nullable = true)
	public int getLicenseeRelationId() {
		return licenseeRelationId;
	}
	public void setLicenseeRelationId(int licenseeRelationId) {
		this.licenseeRelationId = licenseeRelationId;
	}
	
	@Column(name = "LICENSEE_RELATION_NAME", nullable = false, length = 100)
	public String getRelationToLicensee() {
		return relationToLicensee;
	}
	public void setRelationToLicensee(String relationToLicensee) {
		this.relationToLicensee = relationToLicensee;
	}
	
	@Column(name = "ACTIVE_FLAG", nullable = false, length = 1)
	public char getActiveFlag() {
		return activeFlag;
	}
	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_MODIFIED_DATETIME", length = 23)
	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}
	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LAST_MODIFIED_BY")
	public UserTbl getLastModifiedBy() {
		return lastModifiedBy;
	}
	public void setLastModifiedBy(UserTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}
	
	
}
