package com.radiant.urfs.domain;


import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "ENTITY_ASSOCIATION_TEMP_TBL")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityAssociationTempTbl {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ENTITY_ASSOCIATION_TEMP_ID")
    private Integer entityAssociationTempId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ENTITY_ASSOCIATION_ID")
    private EntityAssociationTbl entityAssociation;  // Nullable as per the DB script

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ENTITY_TEMP_ID")
    private EntityTempTbl entityTemp;

    @Column(name = "ASSOCIATION_NAME", length = 40)
    private String associationName;

    @Column(name = "MEMBERSHIP_START_DATE")
    @Temporal(TemporalType.DATE)
    private Date membershipStartDate;

    @Column(name = "MEMBERSHIP_END_DATE")
    @Temporal(TemporalType.DATE)
    private Date membershipEndDate;

    @Column(name = "MEMBERSHIP_LEVEL", length = 40)
    private String membershipLevel;

    @Column(name = "ACTIVE_FLAG", length = 1)
    private char activeFlag;

    @Column(name = "LAST_MODIFIED_DATETIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastModifiedDatetime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "LAST_MODIFIED_BY")
    private UserSBTbl lastModifiedBy;

	public Integer getEntityAssociationTempId() {
		return entityAssociationTempId;
	}

	public void setEntityAssociationTempId(Integer entityAssociationTempId) {
		this.entityAssociationTempId = entityAssociationTempId;
	}

	public EntityAssociationTbl getEntityAssociation() {
		return entityAssociation;
	}

	public void setEntityAssociation(EntityAssociationTbl entityAssociation) {
		this.entityAssociation = entityAssociation;
	}

	public EntityTempTbl getEntityTemp() {
		return entityTemp;
	}

	public void setEntityTemp(EntityTempTbl entityTemp) {
		this.entityTemp = entityTemp;
	}

	public String getAssociationName() {
		return associationName;
	}

	public void setAssociationName(String associationName) {
		this.associationName = associationName;
	}

	public Date getMembershipStartDate() {
		return membershipStartDate;
	}

	public void setMembershipStartDate(Date membershipStartDate) {
		this.membershipStartDate = membershipStartDate;
	}

	public Date getMembershipEndDate() {
		return membershipEndDate;
	}

	public void setMembershipEndDate(Date membershipEndDate) {
		this.membershipEndDate = membershipEndDate;
	}

	public String getMembershipLevel() {
		return membershipLevel;
	}

	public void setMembershipLevel(String membershipLevel) {
		this.membershipLevel = membershipLevel;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public UserSBTbl getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(UserSBTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}
    
    
    
    
}
