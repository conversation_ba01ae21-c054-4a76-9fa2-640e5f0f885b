package com.radiant.urfs.web.model;

import java.io.Serializable;
import java.util.Date;

import com.radiant.urfs.dao.impl.WebCertificationTbl;
import com.radiant.urfs.domain.WebAssociationTempTbl;

public class WebEntityTemp implements Serializable {

	private Integer entityTempId;
	private Integer entityId;
	private String entityNumber;
	private String entityName;
	private String phone;
	private String url;
	private Date incorporationDate;
	private String companyHead;
	private String numEmployees;
	private String address;
	private String companyProfile;
//	    private byte[] logo;
	// private Integer paymentsId;
	private char activeFlag;
	private Date lastModifiedDatetime;
	private Integer lastModifiedBy;
	private char authInd;
	private Integer authBy;
	private Integer companyTypeId;
	private Integer businessPurposeId;
	private Integer businessOperationId;
	private Integer userId; // Assuming only ID is needed for simplicity
	private String username;
	private String firstname;
	private String lastname;
	private String transactionName;
	private Integer transactionTypeId;
	private String sbrNumber;
	private Integer webDocumentTypeId;
	private WebDocumentTbl webDocument;
	private WebPartershipTbl webPartnership; // Field for partnership details
	private WebCertificationTbl webCertificationTbl;
	private WebAssociationTempTbl webAssociationTempTbl;
	private String status;
	private String emailAddress;
	private String FederalEmployerIdentificationNumber;
	private String StateOfIncorporation;
	private String BusinessKeywords;
	private String AreasInterests;
	private String Estimatedannualrevenue;
	private String OwnerFirstName;
	private String OwnerLastName;
	private String OwnerPhone;
	private Character sizeCompanyInd; // Change from char to Character
	private String userphone;
	private String useremailAddress;
	private Character logInd;

	public Character getLogInd() {
		return logInd;
	}

	public void setLogInd(Character logInd) {
		this.logInd = logInd;
	}

	public String getFederalEmployerIdentificationNumber() {
		return FederalEmployerIdentificationNumber;
	}

	public void setFederalEmployerIdentificationNumber(String federalEmployerIdentificationNumber) {
		FederalEmployerIdentificationNumber = federalEmployerIdentificationNumber;
	}

	public String getStateOfIncorporation() {
		return StateOfIncorporation;
	}

	public void setStateOfIncorporation(String stateOfIncorporation) {
		StateOfIncorporation = stateOfIncorporation;
	}

	public String getBusinessKeywords() {
		return BusinessKeywords;
	}

	public void setBusinessKeywords(String businessKeywords) {
		BusinessKeywords = businessKeywords;
	}

	public String getAreasInterests() {
		return AreasInterests;
	}

	public void setAreasInterests(String areasInterests) {
		AreasInterests = areasInterests;
	}

	public String getEstimatedannualrevenue() {
		return Estimatedannualrevenue;
	}

	public void setEstimatedannualrevenue(String estimatedannualrevenue) {
		Estimatedannualrevenue = estimatedannualrevenue;
	}

	public String getOwnerFirstName() {
		return OwnerFirstName;
	}

	public void setOwnerFirstName(String ownerFirstName) {
		OwnerFirstName = ownerFirstName;
	}

	public String getOwnerLastName() {
		return OwnerLastName;
	}

	public void setOwnerLastName(String ownerLastName) {
		OwnerLastName = ownerLastName;
	}

	public String getOwnerPhone() {
		return OwnerPhone;
	}

	public void setOwnerPhone(String ownerPhone) {
		OwnerPhone = ownerPhone;
	}

	public String getOwnerEmail() {
		return OwnerEmail;
	}

	public void setOwnerEmail(String ownerEmail) {
		OwnerEmail = ownerEmail;
	}

	private String OwnerEmail;

	public Integer getTransactionTypeId() {
		return transactionTypeId;
	}

	public void setTransactionTypeId(Integer transactionTypeId) {
		this.transactionTypeId = transactionTypeId;
	}

	public String getTransactionName() {
		return transactionName;
	}

	public void setTransactionName(String transactionName) {
		this.transactionName = transactionName;
	}

	public Integer getEntityTempId() {
		return entityTempId;
	}

	public void setEntityTempId(Integer entityTempId) {
		this.entityTempId = entityTempId;
	}

	public Integer getEntityId() {
		return entityId;
	}

	public void setEntityId(Integer entityId) {
		this.entityId = entityId;
	}

	public String getEntityNumber() {
		return entityNumber;
	}

	public void setEntityNumber(String entityNumber) {
		this.entityNumber = entityNumber;
	}

	public String getEntityName() {
		return entityName;
	}

	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Date getIncorporationDate() {
		return incorporationDate;
	}

	public void setIncorporationDate(Date incorporationDate) {
		this.incorporationDate = incorporationDate;
	}

	public String getCompanyHead() {
		return companyHead;
	}

	public void setCompanyHead(String companyHead) {
		this.companyHead = companyHead;
	}

	public String getNumEmployees() {
		return numEmployees;
	}

	public void setNumEmployees(String numEmployees) {
		this.numEmployees = numEmployees;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getCompanyProfile() {
		return companyProfile;
	}

	public void setCompanyProfile(String companyProfile) {
		this.companyProfile = companyProfile;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public Integer getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(Integer lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}

	public char getAuthInd() {
		return authInd;
	}

	public void setAuthInd(char authInd) {
		this.authInd = authInd;
	}

	public Integer getAuthBy() {
		return authBy;
	}

	public void setAuthBy(Integer authBy) {
		this.authBy = authBy;
	}

	public Integer getCompanyTypeId() {
		return companyTypeId;
	}

	public void setCompanyTypeId(Integer companyTypeId) {
		this.companyTypeId = companyTypeId;
	}

	public Integer getBusinessPurposeId() {
		return businessPurposeId;
	}

	public void setBusinessPurposeId(Integer businessPurposeId) {
		this.businessPurposeId = businessPurposeId;
	}

	public Integer getBusinessOperationId() {
		return businessOperationId;
	}

	public void setBusinessOperationId(Integer businessOperationId) {
		this.businessOperationId = businessOperationId;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getFirstname() {
		return firstname;
	}

	public void setFirstname(String firstname) {
		this.firstname = firstname;
	}

	public String getLastname() {
		return lastname;
	}

	public void setLastname(String lastname) {
		this.lastname = lastname;
	}

	public String getSbrNumber() {
		return sbrNumber;
	}

	public void setSbrNumber(String sbrNumber) {
		this.sbrNumber = sbrNumber;
	}

	public Integer getWebDocumentTypeId() {
		return webDocumentTypeId;
	}

	public void setWebDocumentTypeId(Integer webDocumentTypeId) {
		this.webDocumentTypeId = webDocumentTypeId;
	}

	public WebDocumentTbl getWebDocument() {
		return webDocument;
	}

	public void setWebDocument(WebDocumentTbl webDocument) {
		this.webDocument = webDocument;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getEmailAddress() {
		return emailAddress;
	}

	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}

	public WebPartershipTbl getWebPartnership() {
		return webPartnership;
	}

	public void setWebPartnership(WebPartershipTbl webPartnership) {
		this.webPartnership = webPartnership;
	}

	public WebCertificationTbl getWebCertificationTbl() {
		return webCertificationTbl;
	}

	public void setWebCertificationTbl(WebCertificationTbl webCertificationTbl) {
		this.webCertificationTbl = webCertificationTbl;
	}

	public WebAssociationTempTbl getWebAssociationTempTbl() {
		return webAssociationTempTbl;
	}

	public void setWebAssociationTempTbl(WebAssociationTempTbl webAssociationTempTbl) {
		this.webAssociationTempTbl = webAssociationTempTbl;
	}

	public Character getSizeCompanyInd() {
		return sizeCompanyInd;
	}

	public void setSizeCompanyInd(Character sizeCompanyInd) {
		this.sizeCompanyInd = sizeCompanyInd;
	}

	public String getUserphone() {
		return userphone;
	}

	public void setUserphone(String userphone) {
		this.userphone = userphone;
	}

	public String getUseremailAddress() {
		return useremailAddress;
	}

	public void setUseremailAddress(String useremailAddress) {
		this.useremailAddress = useremailAddress;
	}

}
