spring.application.name=ssb
spring.datasource.url=******************************************************************
spring.datasource.username=ssbuser
spring.datasource.password=Busine$$@007
spring.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.SQLServer2012Dialect
spring.jpa.show-sql=true
spring.session.jdbc.schema=IAICC_DEV
server.port=8443

logging.file=logs/ssbbe.log
logging.level.root=INFO

# Business Registry API URL
business.registry.api.url=http://bregistry.iaicc.org:8080/api

spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=500
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.leak-detection-threshold=60000

spring.servlet.multipart.enabled=true
spring.jpa.properties.hibernate.format_sql=true
spring.servlet.multipart.file-size-threshold=2MB

spring.activiti.database-schema-update=false

jwt.client_id=sciits-fiduciary
jwt.client_secret=sciits-secret
jwt.grant_type=password
jwt.refresh_token=refresh_token
jwt.score_read=read
jwt.score_write=write
jwt.secret=MaYzkSjmkzPC57L

AbstractUserDetailsAuthenticationProvider.badCredentials=Invalid username or password

security.oauth2.resource.filter-order=3
spring.main.allow-bean-definition-overriding=true
spring.jpa.properties.hibernate.current_session_context_class=org.springframework.orm.hibernate4.SpringSessionContext
#############
# PayPal Sandbox Credentials
paypal.client.id=Aa_VTfV_nhIoaPeVHy_XkaTD3B00mBYRYaL8_68qPbnIdDd5ZWXvInFFVpO6zjZBgSQUHSLsqZ9XHanp
paypal.client.secret=EF7hrucbwito4sF2G0EVF9_GDwEqDvMuux5G9HYotnBpXyILwsgX7eUdGyRxMJCvS16s_pbm3BW7Op4k

# PayPal Mode (sandbox for testing or live for production)
paypal.mode=sandbox

# PayPal redirect and cancel URLs
paypal.final_redirect_url=https://www.sandbox.paypal.com/ncp/payment/ZTATDLLSV66NW/6D6278082H603383S
paypal.cancel_url=http://localhost:8080/paypal/cancel

#############
#Mailing deails
mail.host=smtp.gmail.com
mail.port=587
mail.username=<EMAIL>
mail.password=kzaefyobjyvanzwb
mail.ccRecipients=<EMAIL>


# Other properties
mail.smtp.auth=true
mail.smtp.starttls.enable=true
#############

bvi_username=<EMAIL>

#bvi_username=<EMAIL>
#Forgot password
#reset.password = http://localhost:8080/Sbr-FE/#/resetpassword?token=

reset.password =http://sbregistry.radiant.digital:8080/Sbr-FE/#/resetpassword?token=

#uplod File Size
spring.servlet.multipart.max-file-size=1024MB
spring.servlet.multipart.max-request-size=1024MB
spring.mvc.hiddenmethod.filter.enabled = true

reportingPeriodYears = 2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030