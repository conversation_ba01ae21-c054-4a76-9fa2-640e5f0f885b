
package com.radiant.urfs.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import com.radiant.urfs.domain.DocumentTbl;
import com.radiant.urfs.domain.UserLoginTbl;
import com.radiant.urfs.domain.UserSBLoginTbl;
import com.radiant.urfs.exception.BviReturnsException;
//import com.radiant.urfs.web.model.WebUserRegistrationInfo;
import com.radiant.urfs.web.model.SBDetails;
import com.radiant.urfs.web.model.WebPassword;
import com.radiant.urfs.web.model.WebUser;
import com.radiant.urfs.web.model.WebUserData;

public interface UserService {

	public ResponseEntity<?> saveNewUserRegistration(SBDetails sbDetails, MultipartFile addndoc)
			throws BviReturnsException;

	public Map<Integer, DocumentTbl> getDocumentsFromFiles(MultipartFile addndoc) throws Exception;

	public ResponseEntity<?> ammendments(SBDetails sbDetails, MultipartFile addndoc, Integer userId)
			throws BviReturnsException;

	public ResponseEntity<?> getPaymentSummaryDetails(String paymentCode) throws BviReturnsException;

	public boolean checkUsername(String userName) throws BviReturnsException;

	public ResponseEntity<?> changePassword(WebPassword webPassword) throws BviReturnsException;

	public ResponseEntity<?> forgotPassword(String userName, String emailAddress) throws BviReturnsException;

	public ResponseEntity<?> validateOldPassword(WebUserData webUserData) throws BviReturnsException;

	public ResponseEntity<?> resetPassword(String token, String newPassword) throws BviReturnsException;

	public ResponseEntity<?> getDetailsByUserName(String userName) throws BviReturnsException;

	public ResponseEntity<?> getAllUsers() throws BviReturnsException;

	public ResponseEntity<?> registerUser(WebUser userSignupTbl) throws BviReturnsException;

	public UserLoginTbl verifyUserName(String username) throws BviReturnsException;

	public ResponseEntity<?> getDashboardList(char authInd) throws BviReturnsException;

	public ResponseEntity<?> getApprovedDashboardList() throws BviReturnsException;

	public ResponseEntity<?> getAmmendmentsDashboardList(Integer userId) throws BviReturnsException;

	public ResponseEntity<?> getSearchDetails(String sbNumber, String startDate, String endDate, String entityName,
			Integer businessOperationId, String companyHead, String url, String status, Integer companyTypeId,
			String AreasInterests, String BusinessKeywords) throws BviReturnsException;

	public ResponseEntity<?> approve(List<Integer> entityTempIds) throws BviReturnsException;

	public ResponseEntity<?> reject(String comments, List<Integer> entityTempIds) throws BviReturnsException;

	public Optional<UserSBLoginTbl> authenticate(String username, String password) throws BviReturnsException;

	public ResponseEntity<?> getBussinessPurposeDetails() throws BviReturnsException;

	public ResponseEntity<?> getPartnershipDetails() throws BviReturnsException;

	public ResponseEntity<?> getBussinessOperationDetails() throws BviReturnsException;

	public ResponseEntity<?> getCompanyTypeDetails() throws BviReturnsException;

	public ResponseEntity<?> getRegistrationDashboardList(String sbrNumber) throws BviReturnsException;

	public ResponseEntity<?> getTransactionsDetails() throws BviReturnsException;

	public ResponseEntity<?> sendEmailDetails(String comments,String entityName,String subject)throws BviReturnsException;

	public ResponseEntity<?> enableDisableUser(String activeFlag, Integer userId, String entityName)
			throws BviReturnsException;

	public ResponseEntity<?> activeInactiveUsers() throws BviReturnsException;

	public ResponseEntity<?> getTransactionsDetailsByUser(Integer companyName) throws BviReturnsException;

	public ResponseEntity<?> registerNewUser(SBDetails sbDetails) throws BviReturnsException;

	public ResponseEntity<?> updateUser(SBDetails sbDetails, Integer userId) throws BviReturnsException;

	public ResponseEntity<?> getRegistrationPendingList(String sbrNumber) throws BviReturnsException;

	public ResponseEntity<?> getApprovedRegisterList() throws BviReturnsException;

	public ResponseEntity<?> getDetailsByUser(Integer lastModifiedBy) throws BviReturnsException;

}
