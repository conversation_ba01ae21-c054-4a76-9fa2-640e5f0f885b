package com.radiant.urfs.domain;


import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

/**
 * DocumentTypeTbl generated by hbm2java
 */
@Entity
@Table(name = "DOCUMENT_TYPE_TBL")
public class DocumentTypeTbl implements java.io.Serializable {

	private Integer documentTypeId;
	private UserSBTbl userTbl;
	private String documentType;
	private String documentTypeDesc;
	private char activeFlag;
	private Date lastModifiedDatetime;
	private Set<DocumentTbl> documentTbls = new HashSet<DocumentTbl>(0);

	public DocumentTypeTbl() {
	}

	public DocumentTypeTbl(UserSBTbl userTbl, String documentType, String documentTypeDesc, char activeFlag,
			Date lastModifiedDatetime) {
		this.userTbl = userTbl;
		this.documentType = documentType;
		this.documentTypeDesc = documentTypeDesc;
		this.activeFlag = activeFlag;
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public DocumentTypeTbl(UserSBTbl userTbl, String documentType, String documentTypeDesc, char activeFlag,
			Date lastModifiedDatetime, Set<DocumentTbl> documentTbls) {
		this.userTbl = userTbl;
		this.documentType = documentType;
		this.documentTypeDesc = documentTypeDesc;
		this.activeFlag = activeFlag;
		this.lastModifiedDatetime = lastModifiedDatetime;
		this.documentTbls = documentTbls;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "DOCUMENT_TYPE_ID", unique = true, nullable = false)
	public Integer getDocumentTypeId() {
		return this.documentTypeId;
	}

	public void setDocumentTypeId(Integer documentTypeId) {
		this.documentTypeId = documentTypeId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LAST_MODIFIED_BY", nullable = false)
	public UserSBTbl getUserTbl() {
		return this.userTbl;
	}

	public void setUserTbl(UserSBTbl userTbl) {
		this.userTbl = userTbl;
	}

	@Column(name = "DOCUMENT_TYPE", unique = true, nullable = false, length = 60)
	public String getDocumentType() {
		return this.documentType;
	}

	public void setDocumentType(String documentType) {
		this.documentType = documentType;
	}

	@Column(name = "DOCUMENT_TYPE_DESC", nullable = false)
	public String getDocumentTypeDesc() {
		return this.documentTypeDesc;
	}

	public void setDocumentTypeDesc(String documentTypeDesc) {
		this.documentTypeDesc = documentTypeDesc;
	}

	@Column(name = "ACTIVE_FLAG", nullable = false, length = 1)
	public char getActiveFlag() {
		return this.activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_MODIFIED_DATETIME", nullable = false, length = 23)
	public Date getLastModifiedDatetime() {
		return this.lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "documentTypeTbl")
	public Set<DocumentTbl> getDocumentTbls() {
		return this.documentTbls;
	}

	public void setDocumentTbls(Set<DocumentTbl> documentTbls) {
		this.documentTbls = documentTbls;
	}

}
