package com.radiant.urfs.domain;

 import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.Date;


	
@JsonIgnoreProperties(ignoreUnknown = true)

	@Entity
	@Table(name = "ENTITY_PARTNERSHIP_TEMP_TBL")
	public class PartnershipTempTbl implements Serializable {

	    @Id
	    @GeneratedValue(strategy = GenerationType.IDENTITY)
	    @Column(name = "ENTITY_PARTNERSHIP_TEMP_ID")
	    private int entityPartnershipTempId;

	    @ManyToOne
	    @JoinColumn(name = "ENTITY_PARTNERSHIP_ID", referencedColumnName = "ENTITY_PARTNERSHIP_ID")
	    private PartnershipTbl entityPartnership;

	    @ManyToOne
	    @JoinColumn(name = "ENTITY_TEMP_ID", referencedColumnName = "ENTITY_TEMP_ID")
	    private EntityTempTbl entityTemp;

	    @Column(name = "PARTNERSHIP_DESCRIPTION", length = 40)
	    private String partnershipDescription;

	    @Column(name = "PARTNER_COMPANY_NAME", length = 40)
	    private String partnerCompanyName;
	    
//	    @Column(name = "PARTNERSHIP_DURATION")
//	    private int partnershipDuration;

	    @ManyToOne
	    @JoinColumn(name = "PARTNERSHIP_TYPE_ID", referencedColumnName = "PARTNERSHIP_TYPE_ID")
	    private PartnershipTypeTbl partnershipType;


	    @Column(name = "PARTNERSHIP_END_DATE")
	    @Temporal(TemporalType.DATE)
	    private Date partnershipEndDate;

	    @Column(name = "ACTIVE_FLAG", length = 1)
	    private char activeFlag;

	    @Column(name = "LAST_MODIFIED_DATETIME")
	    @Temporal(TemporalType.DATE)
	    private Date lastModifiedDatetime;

	    @ManyToOne
	    @JoinColumn(name = "LAST_MODIFIED_BY", referencedColumnName = "USER_ID")
	    private UserSBTbl lastModifiedBy;

	    // Getters and Setters
	    public int getEntityPartnershipTempId() {
	        return entityPartnershipTempId;
	    }

	    public void setEntityPartnershipTempId(int entityPartnershipTempId) {
	        this.entityPartnershipTempId = entityPartnershipTempId;
	    }

	    public PartnershipTbl getEntityPartnership() {
	        return entityPartnership;
	    }

	    public void setEntityPartnership(PartnershipTbl entityPartnership) {
	        this.entityPartnership = entityPartnership;
	    }

	    public EntityTempTbl getEntityTemp() {
	        return entityTemp;
	    }

	    public void setEntityTemp(EntityTempTbl entityTemp) {
	        this.entityTemp = entityTemp;
	    }

	    public String getPartnerCompanyName() {
	        return partnerCompanyName;
	    }

	    public void setPartnerCompanyName(String partnerCompanyName) {
	        this.partnerCompanyName = partnerCompanyName;
	    }

	    public PartnershipTypeTbl getPartnershipType() {
	        return partnershipType;
	    }

	    public void setPartnershipType(PartnershipTypeTbl partnershipType) {
	        this.partnershipType = partnershipType;
	    }

	   

	    public String getPartnershipDescription() {
			return partnershipDescription;
		}

		public void setPartnershipDescription(String partnershipDescription) {
			this.partnershipDescription = partnershipDescription;
		}

		public Date getPartnershipEndDate() {
			return partnershipEndDate;
		}

		public void setPartnershipEndDate(Date partnershipEndDate) {
			this.partnershipEndDate = partnershipEndDate;
		}

		public char getActiveFlag() {
	        return activeFlag;
	    }

	    public void setActiveFlag(char activeFlag) {
	        this.activeFlag = activeFlag;
	    }

	    public Date getLastModifiedDatetime() {
	        return lastModifiedDatetime;
	    }

	    public void setLastModifiedDatetime(Date lastModifiedDatetime) {
	        this.lastModifiedDatetime = lastModifiedDatetime;
	    }

		public UserSBTbl getLastModifiedBy() {
			return lastModifiedBy;
		}

		public void setLastModifiedBy(UserSBTbl lastModifiedBy) {
			this.lastModifiedBy = lastModifiedBy;
		}

//		public int getPartnershipDuration() {
//			return partnershipDuration;
//		}
//
//		public void setPartnershipDuration(int partnershipDuration) {
//			this.partnershipDuration = partnershipDuration;
//		}

		
	  

		
	}



