package com.radiant.urfs.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.radiant.urfs.web.model.WebUser;

import java.util.HashMap;
import java.util.Map;

@Service
public class BusinessRegistryService {

    private static final Logger log = LoggerFactory.getLogger(BusinessRegistryService.class);

    private final RestTemplate restTemplate = new RestTemplate();

    @Value("${business.registry.api.url}")
    private String registryUrl; // Example: http://bregistry.iaicc.org:8080/stageiaicc-1.0-BE

    public void syncCompany(WebUser userSignupTbl) {
        try {
            Map<String, Object> payload = new HashMap<>();
            payload.put("email", userSignupTbl.getEmail());
            payload.put("username", userSignupTbl.getUsername());
            payload.put("membershipId", userSignupTbl.getUserId());
            if (userSignupTbl.getEntityName() != null) {
                payload.put("company", userSignupTbl.getEntityName());
            }
            payload.put("status", "ACTIVE");

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(payload, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(
                    registryUrl + "/companies", entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Company synced to Business Registry: {}", userSignupTbl.getUsername());
            } else {
                log.error("Failed to sync company. Status: {}, Body: {}",
                        response.getStatusCode(), response.getBody());
            }
        } catch (Exception e) {
            log.error("Error syncing company to Business Registry", e);
        }
    }
}
