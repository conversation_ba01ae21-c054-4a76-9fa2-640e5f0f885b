package com.radiant.urfs.domain;

import java.io.Serializable;

public class UserApproveRequest implements Serializable {

	private static long serialVersionUID = 1L;
	private String taskId;
	private Boolean approve;

	public String getTaskId() {
		return taskId;
	}

	public Boolean getApprove() {
		return approve;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public void setApprove(Boolean approve) {
		this.approve = approve;
	}

}
