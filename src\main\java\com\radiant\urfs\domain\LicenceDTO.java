package com.radiant.urfs.domain;

public class LicenceDTO {

	private String licenceNumber;
	private String licenceHolder;
	private String reportingPeriod;
	private String reportingPeriodMonth;
	private String licenceType;
	private int licenceDetailsId;
	private String licenseeName;
	private String registeredAgent;
	private String relationToLicensee;
	private String companyNumber;
	private String registeredAgentName;
	private String userId;
	private String firstName;
	private String lastName;
	private String noOfCompaniesLinked;
	private Integer noOfCompaniesPendingApproval;
	private Integer noOfCompaniesApproved;
	private Integer noOfCompaniesRejected;
	private String status;
	private String createDate;
	private String formStatus;
	private String amendmentComments;
	
	public String getLicenseeName() {
		return licenseeName;
	}
	public void setLicenseeName(String licenseeName) {
		this.licenseeName = licenseeName;
	}
	public String getRegisteredAgent() {
		return registeredAgent;
	}
	public void setRegisteredAgent(String registeredAgent) {
		this.registeredAgent = registeredAgent;
	}
	public String getLicenceHolder() {
		return licenceHolder;
	}
	public void setLicenceHolder(String licenceHolder) {
		this.licenceHolder = licenceHolder;
	}
	public String getReportingPeriod() {
		return reportingPeriod;
	}
	public void setReportingPeriod(String reportingPeriod) {
		this.reportingPeriod = reportingPeriod;
	}
	public String getLicenceNumber() {
		return licenceNumber;
	}
	public void setLicenceNumber(String licenceNumber) {
		this.licenceNumber = licenceNumber;
	}
	public String getReportingPeriodMonth() {
		return reportingPeriodMonth;
	}
	public void setReportingPeriodMonth(String reportingPeriodMonth) {
		this.reportingPeriodMonth = reportingPeriodMonth;
	}
	public String getLicenceType() {
		return licenceType;
	}
	public void setLicenceType(String licenceType) {
		this.licenceType = licenceType;
	}
	public int getLicenceDetailsId() {
		return licenceDetailsId;
	}
	public void setLicenceDetailsId(int licenceDetailsId) {
		this.licenceDetailsId = licenceDetailsId;
	}
	public String getRelationToLicensee() {
		return relationToLicensee;
	}
	public void setRelationToLicensee(String relationToLicensee) {
		this.relationToLicensee = relationToLicensee;
	}
	public String getCompanyNumber() {
		return companyNumber;
	}
	public void setCompanyNumber(String companyNumber) {
		this.companyNumber = companyNumber;
	}
	public String getRegisteredAgentName() {
		return registeredAgentName;
	}
	public void setRegisteredAgentName(String registeredAgentName) {
		this.registeredAgentName = registeredAgentName;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	public String getLastName() {
		return lastName;
	}
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	public String getNoOfCompaniesLinked() {
		return noOfCompaniesLinked;
	}
	public void setNoOfCompaniesLinked(String noOfCompaniesLinked) {
		this.noOfCompaniesLinked = noOfCompaniesLinked;
	}
	public Integer getNoOfCompaniesPendingApproval() {
		return noOfCompaniesPendingApproval;
	}
	public void setNoOfCompaniesPendingApproval(Integer noOfCompaniesPendingApproval) {
		this.noOfCompaniesPendingApproval = noOfCompaniesPendingApproval;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public Integer getNoOfCompaniesApproved() {
		return noOfCompaniesApproved;
	}
	public void setNoOfCompaniesApproved(Integer noOfCompaniesApproved) {
		this.noOfCompaniesApproved = noOfCompaniesApproved;
	}
	public Integer getNoOfCompaniesRejected() {
		return noOfCompaniesRejected;
	}
	public void setNoOfCompaniesRejected(Integer noOfCompaniesRejected) {
		this.noOfCompaniesRejected = noOfCompaniesRejected;
	}
	public String getCreateDate() {
		return createDate;
	}
	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}
	public String getFormStatus() {
		return formStatus;
	}
	public void setFormStatus(String formStatus) {
		this.formStatus = formStatus;
	}
	public String getAmendmentComments() {
		return amendmentComments;
	}
	public void setAmendmentComments(String amendmentComments) {
		this.amendmentComments = amendmentComments;
	}
	
}
