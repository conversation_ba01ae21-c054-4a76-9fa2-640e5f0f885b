
package com.radiant.urfs.dao.impl;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.commons.lang3.RandomStringUtils;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.modelmapper.internal.bytebuddy.utility.RandomString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Repository;
import com.radiant.urfs.common.Constants;
import com.radiant.urfs.dao.PrudentialReturnsDao;
import com.radiant.urfs.dao.UserDao;
import com.radiant.urfs.domain.ActivitiResponse;
import com.radiant.urfs.domain.BusinessOperationTypeTbl;
import com.radiant.urfs.domain.BusinessPurposeTbl;
import com.radiant.urfs.domain.CertificationTbl;
import com.radiant.urfs.domain.CertificationTempTbl;
import com.radiant.urfs.domain.CompanyTypeTbl;
import com.radiant.urfs.domain.DocumentTbl;
import com.radiant.urfs.domain.EntityAssociationTbl;
import com.radiant.urfs.domain.EntityAssociationTempTbl;
import com.radiant.urfs.domain.EntityTbl;
import com.radiant.urfs.domain.EntityTempTbl;
import com.radiant.urfs.domain.LicenseeRelationTbl;
import com.radiant.urfs.domain.PartnershipTbl;
import com.radiant.urfs.domain.PartnershipTempTbl;
import com.radiant.urfs.domain.PartnershipTypeTbl;
import com.radiant.urfs.domain.PaymentSummaryDetails;
import com.radiant.urfs.domain.PaypalTransaction;
import com.radiant.urfs.domain.ProductPricingTbl;
import com.radiant.urfs.domain.RoleTbl;
import com.radiant.urfs.domain.UserDetailsTbl;
import com.radiant.urfs.domain.UserLicenceDetailsTbl;
import com.radiant.urfs.domain.UserLoginTbl;
import com.radiant.urfs.domain.UserSBLoginTbl;
import com.radiant.urfs.domain.UserSBRoleTbl;
import com.radiant.urfs.domain.UserSBTbl;
import com.radiant.urfs.domain.UserTbl;
import com.radiant.urfs.domain.WebAssociationTempTbl;
import com.radiant.urfs.exception.BviReturnsException;
import com.radiant.urfs.repository.UserTblRepository;
import com.radiant.urfs.util.EmailManager;
import com.radiant.urfs.util.PasswordEncy;
import com.radiant.urfs.web.model.BaseData;
import com.radiant.urfs.web.model.EmailDetails;
import com.radiant.urfs.web.model.SBDetails;
import com.radiant.urfs.web.model.WebEnableDisableUserDetails;
import com.radiant.urfs.web.model.WebEntityTemp;
import com.radiant.urfs.web.model.WebMessage;
import com.radiant.urfs.web.model.WebPartershipTbl;
import com.radiant.urfs.web.model.WebPartnershipTypeTbl;
import com.radiant.urfs.web.model.WebPassword;
import com.radiant.urfs.web.model.WebPaypalTransaction;
import com.radiant.urfs.web.model.WebUser;
import com.radiant.urfs.web.model.WebUserData;

@Repository
public class UserDaoImpl extends PrudentialReturnsDao<Serializable, BaseData> implements UserDao {

	private static final Logger log = LoggerFactory.getLogger(UserDaoImpl.class);
	private static final String UserLoginTbl = null;
	private String logBaseTag = "UserDaoImpl: ";
	private static final AtomicInteger counter = new AtomicInteger(1);
	SimpleDateFormat dateFormat = new SimpleDateFormat(Constants.DATE_FORMAT);
	PasswordEncy passwordEncryptor = PasswordEncy.getInstance();
	String logTag = "UserDaoImpl: ";
	@Value("${spring.session.jdbc.schema}")
	private String schema;

	@Autowired
	private UserTblRepository userTblRepository;

	/*
	 * @Autowired private TaskInfoRepository taskRepo;
	 */

	@Autowired
	private PasswordEncoder passwordEncoder;

	@Value("${reset.password}")
	private String RESET_PASSWORD;

	@Override
	public boolean checkUsername(String userName) throws BviReturnsException {
		Session session = null;
		boolean userNmaeExist = false;
		UserSBLoginTbl userLoginTbl = null;
		try {
			session = getSession();

			Criteria criteria = session.createCriteria(UserSBLoginTbl.class);
			criteria.add(Restrictions.eq("username", userName));
			userLoginTbl = (UserSBLoginTbl) criteria.uniqueResult();

			if (userLoginTbl != null)
				userNmaeExist = true;

		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in AmlCftAnnualReturnDaoImpl : checkUsername userName : "
					+ userName + e;
			log.error(errMessage, e);
			throw new BviReturnsException(e);
		} finally {
			closeSession(session);
		}
		return userNmaeExist;
	}

	@Override
	public LicenseeRelationTbl getRelationToLicenseeById(int licenseeRelationId) {
		Session session = null;
		LicenseeRelationTbl licenseeRelationTbl = null;
		try {
			session = getSession();

			Criteria criteria = session.createCriteria(LicenseeRelationTbl.class);
			criteria.add(Restrictions.eq("licenseeRelationId", licenseeRelationId));
			criteria.add(Restrictions.eq("activeFlag", Constants.ACTIVE));
			licenseeRelationTbl = (LicenseeRelationTbl) criteria.uniqueResult();

		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in : getRelationToLicenseeById licenseeRelationId:"
					+ licenseeRelationId + " " + e;
			log.error(errMessage, e);
		} finally {
			closeSession(session);
		}
		return licenseeRelationTbl;
	}

	@Override
	public UserLoginTbl getUserLoginTbl(String userName) throws BviReturnsException {
		Session session = null;
		UserLoginTbl userLoginTbl = null;
		try {
			session = getSession();

			Criteria criteria = session.createCriteria(UserLoginTbl.class);
			criteria.createCriteria("roles", "roleTbl");
			criteria.add(Restrictions.eq("username", userName));
			userLoginTbl = (UserLoginTbl) criteria.uniqueResult();

		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in : getUserLoginTbl userName:" + userName + " " + e;
			log.error(errMessage, e);
		} finally {
			closeSession(session);
		}
		return userLoginTbl;
	}

	@Override
	public UserTbl getUserTbl(int userId) throws BviReturnsException {
		Session session = null;
		UserTbl userTbl = null;
		try {
			session = getSession();

			Criteria criteria = session.createCriteria(UserTbl.class);
			criteria.add(Restrictions.eq("userId", userId));
			userTbl = (UserTbl) criteria.uniqueResult();

		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in : getUserTbl userId:" + userId + " " + e;
			log.error(errMessage, e);
		} finally {
			closeSession(session);
		}
		return userTbl;
	}

	@Override
	public void saveUserLicenceDetails(UserLicenceDetailsTbl userLicenceDetailsTbl) throws BviReturnsException {
		Session session = null;
		try {
			session = getSession();

			session.saveOrUpdate(userLicenceDetailsTbl);

		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in : saveUserLicenceDetails" + e;
			log.error(errMessage, e);
		} finally {
			closeSession(session);
		}
	}

	@Override
	public void saveUserLoginTbl(UserLoginTbl userLoginTbl) throws BviReturnsException {
		Session session = null;
		try {
			session = getSession();

			session.saveOrUpdate(userLoginTbl);

		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in : getRelationToLicenseeById of" + e;
			log.error(errMessage, e);
		} finally {
			closeSession(session);
		}
	}

	@Override
	public void saveUserTbl(UserTbl userTbl) throws BviReturnsException {
		Session session = null;
		try {
			session = getSession();

			session.saveOrUpdate(userTbl);

		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in : getRelationToLicenseeById of" + e;
			log.error(errMessage, e);
		} finally {
			closeSession(session);
		}
	}

	@Override
	public ResponseEntity<?> changePassword(WebPassword webPassword) throws BviReturnsException {
		Session session = null;
		ActivitiResponse activitiResponse = new ActivitiResponse();
		try {
			session = getSession();

			// Fetch the user login details
			Criteria criteria = session.createCriteria(UserSBLoginTbl.class);

			criteria.add(Restrictions.eq("userId.userId", webPassword.getUserId()));
			criteria.add(Restrictions.eq("activeFlag", Constants.ACTIVE));

			UserSBLoginTbl userLoginTbl = (UserSBLoginTbl) criteria.uniqueResult();

			if (userLoginTbl != null) {
				// Encode and set the new password
				userLoginTbl.setPassword(passwordEncoder.encode(webPassword.getNewPassword()));

				// Update the last modified date
				userLoginTbl.setLastModifiedDatetime(new Date());

				// Fetch the logged-in user as modifier
				UserSBTbl logedinUser = session.get(UserSBTbl.class, webPassword.getUserId());
				if (logedinUser != null) {
					userLoginTbl.setLastModifiedBy(logedinUser);
				}

				// Save the changes
				session.getTransaction().begin();
				session.saveOrUpdate(userLoginTbl);
				session.getTransaction().commit();

				// Fetch user details for email notification
				Criteria userDetailsCriteria = session.createCriteria(UserDetailsTbl.class);
				userDetailsCriteria.add(Restrictions.eq("user.userId", userLoginTbl.getUserId().getUserId()));
				userDetailsCriteria.add(Restrictions.eq("activeFlag", Constants.ACTIVE));

				UserDetailsTbl userDetailsTbl = (UserDetailsTbl) userDetailsCriteria.uniqueResult();

				if (userDetailsTbl != null && userDetailsTbl.getEmailAddress() != null) {
					// Send password change notification email
					EmailDetails emailDetails = new EmailDetails();
					emailDetails.setToRecipients(new String[] { userDetailsTbl.getEmailAddress() });
					emailDetails.setSubject("Password Changed Successfully");
					emailDetails.setMessageContent("Your IAICC Account Password has been changed successfully.");

					Runnable emailTask = () -> {
						EmailManager emailManager = EmailManager.getInstance();
						try {
							emailManager.send(emailDetails);
						} catch (BviReturnsException e) {
							e.printStackTrace();
						}
					};
					new Thread(emailTask).start();
				}

				activitiResponse.setStatus("success");
				activitiResponse.setMessage(Constants.User.CHANGE_PASSWORD_SUCCESS);
				return ResponseEntity.ok(activitiResponse);
			} else {
				activitiResponse.setStatus("error");
				activitiResponse.setMessage("User not found or inactive.");
				return ResponseEntity.status(HttpStatus.NOT_FOUND).body(activitiResponse);
			}

		} catch (Exception e) {
			String logTag = "Exception occurred while changing password: userId=" + webPassword.getUserId();
			log.error(logTag, e);
			throw new BviReturnsException(logTag, e);
		} finally {
			closeSession(session);
		}
	}

	@Override
	public ResponseEntity<?> forgotPassword(String userName, String emailAddress) throws BviReturnsException {
		Session session = null;
		ActivitiResponse response = new ActivitiResponse();
		try {
			session = getSession();

			// Query the UserSBLoginTbl
			Criteria loginCriteria = session.createCriteria(UserSBLoginTbl.class);
			loginCriteria.add(Restrictions.eq("activeFlag", Constants.ACTIVE));
			loginCriteria.add(Restrictions.eq("username", userName));
			UserSBLoginTbl userLoginTbl = (UserSBLoginTbl) loginCriteria.uniqueResult();

			if (userLoginTbl == null) {
				return new ResponseEntity<>(new WebMessage(0, Constants.User.USER_NAME_NOT_EXSISTS),
						HttpStatus.BAD_REQUEST);
			}

			// Query the UserDetailsTbl
			Criteria userDetailsCriteria = session.createCriteria(UserDetailsTbl.class);
			userDetailsCriteria.add(Restrictions.eq("emailAddress", emailAddress));
			userDetailsCriteria.add(Restrictions.eq("user", userLoginTbl.getUserId())); // Ensure it belongs to the same
																						// user

			UserDetailsTbl userDetailsTbl = (UserDetailsTbl) userDetailsCriteria.uniqueResult();

			if (userDetailsTbl == null) {
				return new ResponseEntity<>(new WebMessage(0, Constants.User.EMAIL_NOT_EXSISTS),
						HttpStatus.BAD_REQUEST);
			}

			// Generate random token and save it against the user
			String token = RandomString.make(30);

			session.getTransaction().begin();
			userLoginTbl.setResetPasswordToken(token);
			session.saveOrUpdate(userLoginTbl);
			session.getTransaction().commit();

			String resetPasswordLink = RESET_PASSWORD + token;

			// Sending reset password link to the user's registered email
			EmailDetails emailDetails = new EmailDetails();
			String[] recipients = { userDetailsTbl.getEmailAddress() };
			emailDetails.setToRecipients(recipients);
			emailDetails.setSubject("BVI Returns - Reset Password");
			emailDetails.setMessageContent("Hello, " + "You have requested to reset your password. "
					+ "Click the link below to change your password:" + "<p><a href=\"" + resetPasswordLink
					+ "\">Change my password</a></p>"
					+ "<br>Ignore this email if you remember your password or did not make the request.");

			// Send the email asynchronously
			Runnable emailTask = () -> {
				EmailManager emailManager = EmailManager.getInstance();
				try {
					emailManager.send(emailDetails);
				} catch (BviReturnsException e) {
					e.printStackTrace();
				}
			};
			new Thread(emailTask).start();

			response.setStatus("success");
			response.setMessage(Constants.User.FORGOT_PASSWORD_LINK_MAIL_SUCCESS);
			return ResponseEntity.ok(response);
		} catch (Exception e) {
			logTag = "Exception occurred while forgot password in UserDaoImpl: ";
			log.error(logTag, e);
			throw new BviReturnsException(logTag, e);
		} finally {
			closeSession(session);
		}
	}

	@Override
	public UserLoginTbl getUserLoginTblByUserId(Integer userId) throws BviReturnsException {
		Session session = null;
		UserLoginTbl userLoginTbl = null;
		try {
			session = getSession();

			Criteria criteria = session.createCriteria(UserLoginTbl.class);
			criteria.createCriteria("roles", "roleTbl");
			criteria.add(Restrictions.eq("userTblByUserId.userId", userId));
			userLoginTbl = (UserLoginTbl) criteria.uniqueResult();

		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in : getUserLoginTblByUserId of" + e;
			log.error(errMessage, e);
		} finally {
			closeSession(session);
		}
		return userLoginTbl;
	}

//	@Override
//	public boolean validateOldPassword(WebUserData webUserData) throws BviReturnsException {
//		Session session = null;
//		UserSBLoginTbl userLoginTbl = null;
//		boolean oldPasswordisCorrect = false;
//		try {
//			session = getSession();
//
//			Criteria criteria = session.createCriteria(UserSBLoginTbl.class);
//			criteria.add(Restrictions.eq("username", webUserData.getUserName()));
//			criteria.add(Restrictions.eq("userId.userId", webUserData.getUserId()));
//
////			criteria.add(Restrictions.eq("email", webUserDetails.getEmail()));
//			userLoginTbl = (UserSBLoginTbl) criteria.uniqueResult();
//			log.info("password:" + userLoginTbl.getPassword());
//			log.info("webUserDatapassword:" + webUserData.getPassword());
//
//			String decPassword = passwordEncryptor.decrypt(userLoginTbl.getPassword());
//
//			if (userLoginTbl != null) {
//				if (passwordEncoder.matches(webUserData.getPassword(), decPassword)) { // Old password
//																						// matches
//					oldPasswordisCorrect = true;
//				}
//			}
//
//		} catch (Exception e) {
//			logTag = "Exception occured while checking old password : ";
//			log.error(logTag, e);
//			throw new BviReturnsException(logTag, e);
//		} finally {
//			closeSession(session);
//		}
//		return oldPasswordisCorrect;
//	}

	@Override
	public boolean validateOldPassword(WebUserData webUserData) throws BviReturnsException {
		Session session = null;
	    UserSBLoginTbl userLoginTbl = null;
	    boolean oldPasswordIsCorrect = false;

	    try {
	        session = getSession();

	        // Fetch UserSBLoginTbl using username and userId
	        Criteria criteria = session.createCriteria(UserSBLoginTbl.class);
	        criteria.add(Restrictions.eq("username", webUserData.getUserName()));
	        criteria.add(Restrictions.eq("userId.userId", webUserData.getUserId()));

	        userLoginTbl = (UserSBLoginTbl) criteria.uniqueResult();

	        if (userLoginTbl == null) {
	            log.error("User not found with username: " + webUserData.getUserName());
	            return false;
	        }

	        // Fetch UserDetailsTbl using email and userId
	        Criteria criteria2 = session.createCriteria(UserDetailsTbl.class);
	        criteria2.add(Restrictions.eq("emailAddress", webUserData.getEmail()));
	        criteria2.add(Restrictions.eq("user.userId", webUserData.getUserId()));

	        UserDetailsTbl userDetailsTbl = (UserDetailsTbl) criteria2.uniqueResult();

	        if (userDetailsTbl == null) {
	            log.error("User email not found or does not match: " + webUserData.getEmail());
	            return false;
	        }

	        String inputPassword = webUserData.getPassword().trim();
	        String databasePassword = userLoginTbl.getPassword().trim();
	        
	        log.info("Input Password: [{}], Database Password: [{}]", inputPassword, databasePassword);

	        // Use BCrypt for password verification
	        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
	        boolean passwordMatches = passwordEncoder.matches(inputPassword, databasePassword);

	        if (passwordMatches) {
	            oldPasswordIsCorrect = true;
	        } else {
	            log.error("Password does not match. Input: [{}], Hashed: [{}]", inputPassword, databasePassword);
	        }

	    } catch (Exception e) {
	        log.error("Exception while checking old password", e);
	        throw new BviReturnsException("Exception while checking old password", e);
	    } finally {
	        closeSession(session);
	    }

	    return oldPasswordIsCorrect;
	}

	@Override
	public ResponseEntity<?> resetPassword(String token, String newPassword) throws BviReturnsException {
		Session session = null;
		try {
			session = getSession();

			Criteria userCriteria = session.createCriteria(UserSBLoginTbl.class);
			userCriteria.add(Restrictions.eq("resetPasswordToken", token));
			UserSBLoginTbl userLoginTbl = (UserSBLoginTbl) userCriteria.uniqueResult();

			if (userLoginTbl == null) {
				return new ResponseEntity<>(new WebMessage("Invalid token", Constants.SUCCESS), HttpStatus.OK);
			} else {
				userLoginTbl.setPassword(passwordEncoder.encode(newPassword));
				// userLoginTbl.setResetPasswordToken(null);
				userLoginTbl.setLastModifiedDatetime(new Date());

				session.getTransaction().begin();
				session.save(userLoginTbl);
				session.getTransaction().commit();
			}

		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in : resetPassword" + e;
			log.error(errMessage, e);
		} finally {
			closeSession(session);
		}
		return new ResponseEntity<>(new WebMessage("You have successfully changed your password.", Constants.SUCCESS),
				HttpStatus.OK);
	}

	@Override
	public List<String> getRegistrationPendingUsers() throws BviReturnsException {
		Session session = null;
		List<Integer> userIdList = null;
		List<Integer> userLoginIdList = null;
		List<String> executionIdList = new ArrayList<>();
		Map<String, String> map = new HashMap<>();
		int userId1 = 0;
		try {
			session = getSession();

			String sql = "select distinct USER_LOGIN_ID from FIDUCIARY.TASKINFO_TBL where TYPE_OF_RETURN = 'Registration' and USER_LOGIN_ID in (select ul.USER_LOGIN_ID from USER_LICENCE_DETAILS_TBL li, USER_LOGIN_TBL ul where ul.USER_ID = li.USER_ID and li.ACTIVE_FLAG = ' ')";
			SQLQuery query = session.createSQLQuery(sql);
			userLoginIdList = query.list();

			/*
			 * for (Integer userLoginId : userLoginIdList) { Criteria criteria3 =
			 * session.createCriteria(TaskInfo.class); criteria3.setProjection(
			 * Projections.projectionList().add(Projections.property("executionId"),
			 * "executionId")); criteria3.createCriteria("userLoginId", "UserLoginTbl");
			 * criteria3.add(Restrictions.eq("UserLoginTbl.userLoginId", userLoginId));
			 * criteria3.setMaxResults(1); String executionId = (String)
			 * criteria3.uniqueResult();
			 * 
			 * if (executionId != null) map.put(executionId, executionId); }
			 */
			executionIdList = new ArrayList<>(map.values());

		} catch (Exception e) {
			logTag = "Exception occured while getRegistrationPendingUsers : " + userId1;
			log.error(logTag, e);
			throw new BviReturnsException(logTag, e);
		} finally {
			closeSession(session);
		}
		return executionIdList;
	}

	@Override
	public List<String> getFiledAmlReturns() throws BviReturnsException {
		Session session = null;
		List<String> executionIdList = null;
		try {
			session = getSession();

			/*
			 * Criteria criteria2 = session.createCriteria(TaskInfo.class);
			 * criteria2.add(Restrictions.eq("typeOfReturn", Constants.AML_CFT));
			 * criteria2.setProjection(
			 * Projections.projectionList().add(Projections.property("executionId"),
			 * "executionId")); executionIdList = criteria2.list();
			 */
			/*
			 * String sql =
			 * "select executionId from TaskInfo where typeOfReturn = 'AMLCFT'"; SQLQuery
			 * query = session.createSQLQuery(sql); executionIdList = query.list();
			 */

		} catch (Exception e) {
			logTag = "Exception occured while getRegistrationPendingUsers : ";
			log.error(logTag, e);
			throw new BviReturnsException(logTag, e);
		} finally {
			closeSession(session);
		}
		return executionIdList;
	}

	String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789~`!@#$%^&*/?";
	private String firstName;

	@Override
	public ResponseEntity<?> getAllUsers() throws BviReturnsException {
		Session session = null;
		List<String> usersList = new ArrayList<>();
		try {
			session = getSession();

			String sql = "select DISTINCT USERNAME from USER_LOGIN_TBL";
			SQLQuery query = session.createSQLQuery(sql);
			usersList = query.list();

		} catch (Exception e) {
			logTag = "Exception occured while getAllUsers : ";
			log.error(logTag, e);
			throw new BviReturnsException(logTag, e);
		} finally {
			closeSession(session);
		}
		return new ResponseEntity<>(usersList, HttpStatus.OK);
	}

	@Override
	public UserTbl getLoggedInUserDetails(Session session) {
		String logTag = "getLoggedInUserDetails(): ";
		log.info("Entering into : " + logTag);
		String username = null;
		try {
			Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			if (principal instanceof UserDetails) {
				username = ((UserDetails) principal).getUsername();
			} else {
				username = principal.toString();
			}
		} catch (Exception e) {
			log.error("From Import Batch job: So No logged in User");
			username = "bviadmin";
		}

		if (session == null) {
			session = getSession();
		}
		Query query = session.createSQLQuery("SELECT USER_ID FROM " + schema
				+ ".dbo.USER_LOGIN_TBL where USERNAME= :username AND ACTIVE_FLAG = :active");
		query.setParameter("username", username);
		query.setParameter("active", Constants.ACTIVE);
		Integer userId = (Integer) query.uniqueResult();
		Criteria criteria = session.createCriteria(UserTbl.class);
		criteria.add(Restrictions.eq("userId", userId));
		criteria.add(Restrictions.eq("activeFlag", Constants.ACTIVE));
		return (UserTbl) criteria.uniqueResult();
	}

	@Override
	public ResponseEntity<?> userRegistration(WebUser webUser) throws BviReturnsException {
		// String logTag = logTag + "userRegistration(): ";
		log.info("Entering into : " + logTag);
		Session session = null;
		try {

			String firstName = webUser.getFirstName();
			firstName = firstName.substring(0, 1).toUpperCase() + firstName.substring(1).toLowerCase();
			String lastName = webUser.getLastName();
			lastName = lastName.substring(0, 1).toUpperCase() + lastName.substring(1).toLowerCase();
			session = getSession();
			EmailDetails emailDetails = new EmailDetails();
			Boolean emailStatus = true;
			String[] recipients = { webUser.getEmail() };
			// ResponseEntity<?> responseEntity = saveUser(session, webUser);
			// if (responseEntity != null /* && "200
			// OK".contains(responseEntity.getStatusCode().toString()) */) {
			String decPassword = passwordEncryptor.decrypt(webUser.getPassword());
			emailDetails.setToRecipients(recipients);
			emailDetails.setSubject("VIRRGIN Returns User Credentials");
			emailDetails.setMessageContent("Dear " + firstName + " " + lastName + "\n"
					+ "Your login has been created in VIRRGIN Returns with the below details: \n" + "User Name : "
					+ webUser.getUsername() + "\n" + "Password : " + decPassword + "\n"
					+ " It is recommended that you change your password after login.");

			Runnable myrunnable = new Runnable() {
				public void run() {
					EmailManager emailManager = EmailManager.getInstance();
					try {
						emailManager.send(emailDetails);
					} catch (BviReturnsException e) {
						e.printStackTrace();
					}
				}
			};

			new Thread(myrunnable).start();

			if (emailStatus) {
				return new ResponseEntity<>(new WebMessage(webUser.getUserId(), Constants.User.USER_DETAILS_SENT),
						HttpStatus.OK);
			} else {
				return new ResponseEntity<>(new WebMessage(webUser.getUserId(), Constants.User.USER_DETAILS_NOT_SENT),
						HttpStatus.OK);
			}
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred: " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public UserLoginTbl verifyUserName(String userName) throws BviReturnsException {
		log.info("Entering into : " + logTag);
		Session session = null;
		UserLoginTbl userLogin = new UserLoginTbl();
		try {
			session = getSession();
			Criteria criteria = session.createCriteria(UserLoginTbl.class);
			criteria.add(Restrictions.eq("username", userName));
			userLogin = (UserLoginTbl) criteria.uniqueResult();

			return userLogin;
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred: " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}

	}

	public ResponseEntity<?> getLicenceNumberByCompanyNumber(String companyNumber) throws BviReturnsException {
		Session session = null;
		List<String> licenseNumbersList = new ArrayList<>();
		try {
			session = getSession();
			String sql = "select DISTINCT LICENCE_NUMBER from [dbo].[USER_LICENCE_DETAILS_TBL] where COMPANY_NUMBER=:companyNumber AND ACTIVE_FLAG='Y'";
			SQLQuery query = session.createSQLQuery(sql);
			query.setString("companyNumber", companyNumber);
			licenseNumbersList = query.list();

		} catch (Exception e) {
			logTag = "Exception occured while getLicenceNumberByCompanyNumber : ";
			log.error(logTag, e);
			throw new BviReturnsException(logTag, e);
		} finally {
			closeSession(session);
		}
		return new ResponseEntity<>(licenseNumbersList, HttpStatus.OK);
	}

	@Override
	public ResponseEntity<?> saveNewUserRegistration(SBDetails sbDetails, DocumentTbl addndocument)
			throws BviReturnsException {
		Session session = null;
		UserSBTbl userTbl = new UserSBTbl();
		SimpleDateFormat dateFormat = new SimpleDateFormat(Constants.DATE_FORMAT);

		try {
			session = getSession();
			session.getTransaction().begin();

			// Set userTbl properties
			userTbl.setFirstName(sbDetails.getUserTbl().getFirstName().trim());
			userTbl.setLastName(sbDetails.getUserTbl().getLastName().trim());
			userTbl.setActiveFlag(Constants.ACTIVE);
			userTbl.setLastModifiedDatetime(new Date());
			session.saveOrUpdate(userTbl);

			// Set role
			RoleTbl role = new RoleTbl();
			role.setRoleId(2);

			// Create new UserDetails object
			UserDetailsTbl userDetailsTbl = new UserDetailsTbl();
			userDetailsTbl.setEmailAddress(sbDetails.getUserDetailsTbl().getEmailAddress());
			userDetailsTbl.setPhone(sbDetails.getUserDetailsTbl().getPhone());
			userDetailsTbl.setUser(userTbl); // Set the entire UserSBTbl object
			userDetailsTbl.setLastModifiedBy(userTbl);
			userDetailsTbl.setLastModifiedDatetime(new Date());
			userDetailsTbl.setActiveFlag(Constants.ACTIVE);
			session.saveOrUpdate(userDetailsTbl);

			// Create new UserRole object
			UserSBRoleTbl userRoleTbl = new UserSBRoleTbl();
			userRoleTbl.setUser(userTbl); // Set the UserSBTbl object
			userRoleTbl.setRole(role); // Set the RoleTbl object
			userRoleTbl.setLastModifiedDatetime(new Date());
			userRoleTbl.setLastModifiedBy(userTbl); // Set the UserSBTbl object for last modified by
			userRoleTbl.setActiveFlag(Constants.ACTIVE);
			session.saveOrUpdate(userRoleTbl);

			// Create new UserLogin object
			UserSBLoginTbl userLoginTbl = new UserSBLoginTbl();
			userLoginTbl.setUsername(sbDetails.getUserLoginTbl().getUsername());
			userLoginTbl.setPassword(passwordEncoder.encode(sbDetails.getUserLoginTbl().getPassword()));
			// userLoginTbl.setPassword(sbDetails.getUserLoginTbl().getPassword());
			userLoginTbl.setUserId(userTbl);
			userLoginTbl.setLastModifiedDatetime(new Date());
			userLoginTbl.setLastModifiedBy(userTbl);
			userLoginTbl.setActiveFlag(Constants.PENDING);
			session.saveOrUpdate(userLoginTbl);

			// Handle Paypal Transactions
			if (sbDetails.getPaypalTransactionList() != null && !sbDetails.getPaypalTransactionList().isEmpty()) {
				for (PaypalTransaction paypalTransaction : sbDetails.getPaypalTransactionList()) {
					paypalTransaction.setPaypalConfirmationNumber(paypalTransaction.getPaypalConfirmationNumber());
					paypalTransaction.setPaypalMessage(paypalTransaction.getPaypalMessage());
					paypalTransaction.setPaypalTransactionAmt(paypalTransaction.getPaypalTransactionAmt());
					paypalTransaction.setPaypalTransactionDatetime(paypalTransaction.getPaypalTransactionDatetime());
					paypalTransaction.setLastModifiedDatetime(new Date());
					paypalTransaction.setCompanyName(paypalTransaction.getCompanyName());
					paypalTransaction.setLastModifiedBy(userTbl);
					paypalTransaction.setUserId(paypalTransaction.getUserId());

					session.saveOrUpdate(paypalTransaction);
				}
			}

			// Create EntityTempTbl
			EntityTempTbl entityTempTbl = new EntityTempTbl();
			// entityTempTbl.setDocumentTblByDocumentAddnId(addndocument);
			entityTempTbl.setEntityName(sbDetails.getEntityTempTbl().getEntityName());
			// entityTempTbl.setEntityNumber(sbDetails.getEntityTempTbl().getEntityNumber());
			entityTempTbl.setSbrNumber(EntityNumberGenerator.generateSBRNumber());
			entityTempTbl.setUrl(sbDetails.getEntityTempTbl().getUrl());
			entityTempTbl.setPhone(sbDetails.getEntityTempTbl().getPhone());
			entityTempTbl.setIncorporationDate(sbDetails.getEntityTempTbl().getIncorporationDate());
			entityTempTbl.setEmailAddress(sbDetails.getEntityTempTbl().getEmailAddress());
			entityTempTbl.setNumEmployees(sbDetails.getEntityTempTbl().getNumEmployees());
			entityTempTbl.setCompanyProfile(sbDetails.getEntityTempTbl().getCompanyProfile());
			entityTempTbl.setAddress(sbDetails.getEntityTempTbl().getAddress());
			entityTempTbl.setLastModifiedDatetime(new Date());
			entityTempTbl.setLastModifiedBy(userTbl);
			entityTempTbl.setAuthInd(Constants.PENDING);
			entityTempTbl.setActiveFlag(Constants.PENDING);
			entityTempTbl.setAuthBy(userTbl);
			entityTempTbl.setUserId(userTbl);
			entityTempTbl.setFederalEmployerIdentificationNumber(
					sbDetails.getEntityTempTbl().getFederalEmployerIdentificationNumber());
			entityTempTbl.setStateOfIncorporation(sbDetails.getEntityTempTbl().getStateOfIncorporation());
			entityTempTbl.setBusinessKeywords(sbDetails.getEntityTempTbl().getBusinessKeywords());
			entityTempTbl.setAreasInterests(sbDetails.getEntityTempTbl().getAreasInterests());
			entityTempTbl.setEstimatedAnnualRevenue(sbDetails.getEntityTempTbl().getEstimatedAnnualRevenue());
			entityTempTbl.setOwnerFirstName(sbDetails.getEntityTempTbl().getOwnerFirstName());
			entityTempTbl.setOwnerLastName(sbDetails.getEntityTempTbl().getOwnerLastName());
			entityTempTbl.setOwnerPhone(sbDetails.getEntityTempTbl().getOwnerPhone());
			entityTempTbl.setOwnerEmail(sbDetails.getEntityTempTbl().getOwnerEmail());
			if (sbDetails.getEntityTempTbl().getSizeCompanyInd() != ' ') { // or another default character
				entityTempTbl.setSizeCompanyInd(sbDetails.getEntityTempTbl().getSizeCompanyInd());
			}

			// Set additional fields if available
			if (sbDetails.getEntityTempTbl().getCompanyTypeId() != null) {
				CompanyTypeTbl companyTypeTbl = session.get(CompanyTypeTbl.class,
						sbDetails.getEntityTempTbl().getCompanyTypeId().getCompanyTypeId());
				entityTempTbl.setCompanyTypeId(companyTypeTbl);
			}

			if (sbDetails.getEntityTempTbl().getBusinessOperationId() != null) {
				BusinessOperationTypeTbl businessOperationTypeTbl = session.get(BusinessOperationTypeTbl.class,
						sbDetails.getEntityTempTbl().getBusinessOperationId().getBusinessOperationId());
				entityTempTbl.setBusinessOperationId(businessOperationTypeTbl);
			}

			if (sbDetails.getEntityTempTbl().getBusinessPurposeId() != null) {
				BusinessPurposeTbl businessPurposeTypeTbl = session.get(BusinessPurposeTbl.class,
						sbDetails.getEntityTempTbl().getBusinessPurposeId().getBusinessPurposeId());
				entityTempTbl.setBusinessPurposeId(businessPurposeTypeTbl);
			}

//			if (sbDetails.getEntityTempTbl().getPaymentsId() != null) {
//				PaymentsTbl paymentsTbl = session.get(PaymentsTbl.class,
//						sbDetails.getEntityTempTbl().getPaymentsId().getPaymentsId());
//				entityTempTbl.setPaymentsId(paymentsTbl);
//			}

			session.saveOrUpdate(entityTempTbl);

			if (sbDetails.getEntityAssociationTempTblList() != null
					&& !sbDetails.getEntityAssociationTempTblList().isEmpty()) {
				for (EntityAssociationTempTbl association : sbDetails.getEntityAssociationTempTblList()) {
					// Check if the association has meaningful data
					if (association.getAssociationName() != null
							&& !association.getAssociationName().trim().isEmpty()) {
						// Create a new EntityAssociationTempTbl object
						EntityAssociationTempTbl entityAssociationTempTbl = new EntityAssociationTempTbl();

						// Set association details from sbDetails
						entityAssociationTempTbl.setEntityTemp(entityTempTbl); // Associate with the entity
						entityAssociationTempTbl.setAssociationName(association.getAssociationName());
						entityAssociationTempTbl.setMembershipStartDate(association.getMembershipStartDate());
						entityAssociationTempTbl.setMembershipEndDate(association.getMembershipEndDate());
						entityAssociationTempTbl.setMembershipLevel(association.getMembershipLevel());
						entityAssociationTempTbl.setActiveFlag(Constants.ACTIVE);
						entityAssociationTempTbl.setLastModifiedDatetime(new Date());
						entityAssociationTempTbl.setLastModifiedBy(userTbl); // Set the user who last modified this

						// Save or update the association
						session.saveOrUpdate(entityAssociationTempTbl);
					}
				}
			}

			// Handle PartnershipTempTbl
			if (sbDetails.getPartnershipTempList() != null && !sbDetails.getPartnershipTempList().isEmpty()) {
				for (PartnershipTempTbl partnershipTempTbl : sbDetails.getPartnershipTempList()) {
					partnershipTempTbl.setEntityTemp(entityTempTbl);
					partnershipTempTbl.setPartnershipDescription(partnershipTempTbl.getPartnershipDescription());
					partnershipTempTbl.setPartnershipEndDate(partnershipTempTbl.getPartnershipEndDate());

					int partnershipTypeId = partnershipTempTbl.getPartnershipType().getPartnershipTypeId();

					// Retrieve the PartnershipTypeTbl object from the database based on its ID
					PartnershipTypeTbl partnershipType = session.get(PartnershipTypeTbl.class, partnershipTypeId);

					// Set the PartnershipTypeTbl object on the PartnershipTempTbl
					partnershipTempTbl.setPartnershipType(partnershipType);

					partnershipTempTbl.setActiveFlag(Constants.ACTIVE);
					partnershipTempTbl.setLastModifiedDatetime(new Date());
					partnershipTempTbl.setLastModifiedBy(entityTempTbl.getUserId());

					session.saveOrUpdate(partnershipTempTbl);
				}
			}

			// Handle CertificationTempTbl
			if (sbDetails.getCertificationTempList() != null && !sbDetails.getCertificationTempList().isEmpty()) {
				for (CertificationTempTbl certificationTempTbl : sbDetails.getCertificationTempList()) {
					certificationTempTbl.setCertificationIssuingBody(certificationTempTbl.getCertificationIssuingBody());
					certificationTempTbl.setCertificationDate(certificationTempTbl.getCertificationDate());
					certificationTempTbl.setCertificationExpiryDate(certificationTempTbl.getCertificationExpiryDate()); // set
					certificationTempTbl.setEntityTemp(entityTempTbl);
					certificationTempTbl.setActiveFlag(Constants.ACTIVE);
					certificationTempTbl.setLastModifiedDatetime(new Date());
					certificationTempTbl.setLastModifiedBy(entityTempTbl.getUserId());
					session.saveOrUpdate(certificationTempTbl);
				}
			}

			session.getTransaction().commit();

			String firstName = userTbl.getFirstName();
			firstName = firstName.substring(0, 1).toUpperCase() + firstName.substring(1).toLowerCase();
			String lastName = userTbl.getLastName();
			lastName = lastName.substring(0, 1).toUpperCase() + lastName.substring(1).toLowerCase();
			session = getSession();
			EmailDetails emailDetails = new EmailDetails();
			Boolean emailStatus = true;
			String[] recipients = { userDetailsTbl.getEmailAddress() };
			// String decPassword =
			// passwordEncryptor.decrypt(sbDetails.getUserLoginTbl().getPassword());
			String decPassword = sbDetails.getUserLoginTbl().getPassword();
			// Update subject to indicate pending approval
			emailDetails.setToRecipients(recipients);
			emailDetails.setSubject("Small Business Registry (IAICC) - Your Registration is Pending Approval");
			// Update the message content to inform the user that the account is pending
			// approval
			emailDetails.setMessageContent("Dear " + firstName + " " + lastName + ",\n\n"
					+ "Thank you for registering with the Small Business Registry (IAICC). Your registration is currently **Pending for Approval**. Once approved, you will receive an email with your login credentials.\n\n"
					+ "Here are your registration details:\n" + "Username: " + sbDetails.getUserLoginTbl().getUsername()
					+ "\n" + "Email: " + userDetailsTbl.getEmailAddress() + "\n\n"
					+ "In the meantime, feel free to explore the resources available on our platform and reach out to our support team if you have any questions.\n\n"
					+ "We appreciate your patience and look forward to your active participation in the Small Business Registry.\n\n"
					+ "Best regards,\n" + "The Small Business Registry (IAICC) Team\n" + "https://iaicc.org/ \n"
					+ "<EMAIL>");

			Runnable myrunnable = new Runnable() {
				public void run() {
					EmailManager emailManager = EmailManager.getInstance();
					try {
						emailManager.send(emailDetails);
					} catch (BviReturnsException e) {
						e.printStackTrace();
					}
				}
			};
			new Thread(myrunnable).start();
			if (emailStatus) {
				return new ResponseEntity<>(
						new WebMessage(Constants.User.REGISTRATION_PENDING, Constants.User.USER_DETAILS_SENT),
						HttpStatus.OK);
			} else {
				return new ResponseEntity<>(new WebMessage(Constants.User.USER_DETAILS_NOT_SENT), HttpStatus.OK);
			}

		} catch (Exception e) {
			if (session.getTransaction() != null) {
				session.getTransaction().rollback();
			}
			String errMessage = "Exception occurred in saveNewUserRegistration userId : "
					+ (userTbl != null ? userTbl.getUserId() : "Unknown") + e;
			log.error(errMessage, e);
			throw new BviReturnsException(e);
		} finally {
			closeSession(session);
		}
	}

	private CertificationTbl getCertificationType(Integer certificationTypeId) {
		Session session = null;
		CertificationTbl certificationTypeTbl = null;
		try {
			session = getSession();
			certificationTypeTbl = session.get(CertificationTbl.class, certificationTypeId);
		} finally {
			if (session != null) {
				closeSession(session);
			}
		}
		return certificationTypeTbl;
	}

	private PartnershipTypeTbl getPartnershipType(Integer partnershipTypeId) {
		Session session = null;
		PartnershipTypeTbl partnershipTypeTbl = null;
		try {
			session = getSession();
			partnershipTypeTbl = session.get(PartnershipTypeTbl.class, partnershipTypeId);
		} finally {
			if (session != null) {
				closeSession(session);
			}
		}
		return partnershipTypeTbl;
	}

	private EntityAssociationTbl getAssociationType(Integer associationTypeId) {
		Session session = null;
		EntityAssociationTbl entityAssociationTbl = null;
		try {
			session = getSession();
			entityAssociationTbl = session.get(EntityAssociationTbl.class, associationTypeId);
		} finally {
			if (session != null) {
				closeSession(session);
			}
		}
		return entityAssociationTbl;
	}

	@Override
	public ResponseEntity<?> getPaymentSummaryDetails(String paymentCode) throws BviReturnsException {
		log.info("UserDaoImpl" + "getPaymentSummaryDetails" + "Start");
		Session session = null;
		PaymentSummaryDetails webPaymentSummaryDetails = new PaymentSummaryDetails();

		try {
			session = getSession();
			Criteria criteria = session.createCriteria(ProductPricingTbl.class);
			criteria.add(Restrictions.eq("productNumber", paymentCode));
			criteria.add(Restrictions.eq("activeFlag", Constants.ACTIVE));
			ProductPricingTbl productPricing = (ProductPricingTbl) criteria.uniqueResult();

			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MMM dd yyyy");
			SimpleDateFormat day = new SimpleDateFormat("EEEE");
			String dayOftheWeek = day.format(new Date());
			String transactionDate = dayOftheWeek + ", " + simpleDateFormat.format(new Date());
			webPaymentSummaryDetails.setTransactionDate(transactionDate);

			if (productPricing != null) {
				webPaymentSummaryDetails.setTransactionName(productPricing.getProductName());
				webPaymentSummaryDetails.setTransactionFee((productPricing.getPrice().doubleValue()));

			}

		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while getting list of mbcAnnualPaymentStrikeoffList " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		} finally {
			closeSession(session);
		}
		return new ResponseEntity<>(webPaymentSummaryDetails, HttpStatus.OK);
	}

	@Override
	public ResponseEntity<?> getDashboardList(char authInd) throws BviReturnsException {
		Session session = null;
		List<WebEntityTemp> webEntityTempList = new ArrayList<>();
		try {
			session = getSession();

			Criteria entityTempCriteria = session.createCriteria(EntityTempTbl.class);
			entityTempCriteria.add(Restrictions.eq("authInd", authInd));
			entityTempCriteria.addOrder(Order.desc("lastModifiedDatetime"));
			List<EntityTempTbl> entityTempTblList = entityTempCriteria.list();

			if (entityTempTblList != null) {
				for (EntityTempTbl entityTempTbl : entityTempTblList) {

					Integer userId = entityTempTbl.getUserId().getUserId();

					WebEntityTemp webEntityTemp = new WebEntityTemp();

					Criteria userSBLoginCriteria = session.createCriteria(UserSBLoginTbl.class);
					userSBLoginCriteria.add(Restrictions.eq("userId.userId", userId));

					UserSBLoginTbl userLoginTbl = (UserSBLoginTbl) userSBLoginCriteria.uniqueResult();

					webEntityTemp.setEntityTempId(entityTempTbl.getEntityTempId());
					webEntityTemp.setUsername(userLoginTbl.getUsername());
					webEntityTemp.setFirstname(entityTempTbl.getUserId().getFirstName());
					webEntityTemp.setLastname(entityTempTbl.getUserId().getLastName());
					// webEntityTemp.setEntityNumber(entityTempTbl.getEntityNumber());
					webEntityTemp.setSbrNumber(entityTempTbl.getSbrNumber());
					webEntityTemp.setEntityName(entityTempTbl.getEntityName());
					webEntityTemp.setEmailAddress(entityTempTbl.getEmailAddress());
					webEntityTemp.setAreasInterests(entityTempTbl.getAreasInterests());
					webEntityTemp.setBusinessKeywords(entityTempTbl.getBusinessKeywords());
					webEntityTemp.setEstimatedannualrevenue(entityTempTbl.getEstimatedAnnualRevenue());
					webEntityTemp.setOwnerFirstName(entityTempTbl.getOwnerFirstName());
					webEntityTemp.setOwnerEmail(entityTempTbl.getOwnerEmail());
					webEntityTemp.setOwnerLastName(entityTempTbl.getOwnerLastName());
					webEntityTemp.setOwnerPhone(entityTempTbl.getOwnerPhone());
//					if(entityTempTbl.getSizeCompanyInd() !=null)
//					webEntityTemp.setSizeCompanyInd(entityTempTbl.getSizeCompanyInd());
					if (entityTempTbl.getSizeCompanyInd() != null) {
						webEntityTemp.setSizeCompanyInd(entityTempTbl.getSizeCompanyInd());
					}

					Criteria userLoginCriteria = session.createCriteria(UserDetailsTbl.class);
					userSBLoginCriteria.add(Restrictions.eq("userId.userId", userId));

					// Fetch Association Details
					Criteria associationCriteria = session.createCriteria(EntityAssociationTempTbl.class);
					associationCriteria
							.add(Restrictions.eq("entityTemp.entityTempId", entityTempTbl.getEntityTempId()));
					List<EntityAssociationTempTbl> associationList = associationCriteria.list();
					UserSBTbl user1 = new UserSBTbl();
					user1.setUserId(userId);
					if (associationList != null && !associationList.isEmpty()) {
						for (EntityAssociationTempTbl association : associationList) {

							WebAssociationTempTbl webAssociationTempTbl = new WebAssociationTempTbl();
							// webAssociationTempTbl.setWebAssociationTempId(association.getEntityAssociationTempId());
							webAssociationTempTbl.setAssociationName(association.getAssociationName());
							webAssociationTempTbl.setMembershipStartDate(association.getMembershipStartDate());
							webAssociationTempTbl.setMembershipEndDate(association.getMembershipEndDate());
							webAssociationTempTbl.setMembershipLevel(association.getMembershipLevel());
							webAssociationTempTbl.setActiveFlag(association.getActiveFlag());
							webAssociationTempTbl.setLastModifiedDatetime(association.getLastModifiedDatetime());
							webAssociationTempTbl.setLastModifiedBy(user1);
							webEntityTemp.setWebAssociationTempTbl(webAssociationTempTbl);

						}
					}

					// Fetch Partnership Details
					Criteria partnershipCriteria = session.createCriteria(PartnershipTempTbl.class);
					partnershipCriteria
							.add(Restrictions.eq("entityTemp.entityTempId", entityTempTbl.getEntityTempId()));
					List<PartnershipTempTbl> partnershipList = partnershipCriteria.list();
					WebUser user = new WebUser();
					user.setUserId(userId);
					if (partnershipList != null && !partnershipList.isEmpty()) {
						for (PartnershipTempTbl partnership : partnershipList) {
							WebPartershipTbl webPartnership = new WebPartershipTbl();
							webPartnership.setEntityPartnershipId(partnership.getEntityPartnershipTempId());
							webPartnership.setPartnerCompanyName(partnership.getPartnerCompanyName());
							webPartnership.setPartnershipEndDate(partnership.getPartnershipEndDate());
							webPartnership.setPartnershipDescription(partnership.getPartnershipDescription());
							webPartnership.setActiveFlag(partnership.getActiveFlag());
							webPartnership.setLastModifiedDatetime(partnership.getLastModifiedDatetime());
							// webPartnership.setLastModifiedBy(userId);
							webPartnership.setLastModifiedBy(user);
							// Set the partnership to the webEntityTemp
							webEntityTemp.setWebPartnership(webPartnership);
						}
					}

					// Fetch Cerfication Details

					Criteria cerficationCriteria = session.createCriteria(CertificationTempTbl.class);
					cerficationCriteria
							.add(Restrictions.eq("entityTemp.entityTempId", entityTempTbl.getEntityTempId()));
					List<CertificationTempTbl> certificationList = cerficationCriteria.list();
					UserSBTbl user2 = new UserSBTbl();
					user2.setUserId(userId);
					if (certificationList != null && !certificationList.isEmpty()) {
						for (CertificationTempTbl certification : certificationList) {
							WebCertificationTbl webCertification = new WebCertificationTbl();
							webCertification.setEntityCertificationId(certification.getEntityCertificationTempId());
							webCertification.setCertificationName(certification.getCertificationName());
							webCertification.setCertificationExpiryDate(certification.getCertificationDate());
							webCertification.setCertificationIssuingBody(certification.getCertificationIssuingBody());
							webCertification.setActiveFlag(certification.getActiveFlag());
							webCertification.setLastModifiedDatetime(certification.getLastModifiedDatetime());
							webCertification.setLastModifiedBy(user2);

							// Set the partnership to the webEntityTemp
							webEntityTemp.setWebCertificationTbl(webCertification);
						}
					}
					if (entityTempTbl.getUrl() != null)
						webEntityTemp.setUrl(entityTempTbl.getUrl());
					if (entityTempTbl.getIncorporationDate() != null) {
						webEntityTemp.setIncorporationDate(entityTempTbl.getIncorporationDate());
					}
					if (entityTempTbl.getPhone() != null) {
						webEntityTemp.setPhone(entityTempTbl.getPhone());
					}

//					if (entityTempTbl.getNumEmployees() != null) {
//						webEntityTemp.setNumEmployees(entityTempTbl.getNumEmployees());
//					}

					if (entityTempTbl.getNumEmployees() != null) {
						webEntityTemp.setNumEmployees(entityTempTbl.getNumEmployees());
					}
					if (entityTempTbl.getCompanyProfile() != null) {
						webEntityTemp.setCompanyProfile(entityTempTbl.getCompanyProfile());
					}
					if (entityTempTbl.getAddress() != null) {
						webEntityTemp.setAddress(entityTempTbl.getAddress());
					}
					webEntityTemp.setLastModifiedDatetime(new Date());
					webEntityTemp.setLastModifiedBy(userId);
					// webEntityTemp.setAuthInd(Constants.PENDING);
					// webEntityTemp.setAuthInd(authInd);
					webEntityTemp.setAuthBy(userId);
					webEntityTemp.setUserId(userId);
					if (entityTempTbl.getActiveFlag() == Constants.ACTIVE) {
						webEntityTemp.setTransactionName("Amendments");
					} else {
						webEntityTemp.setTransactionName("Registration");
					}
					if (null != entityTempTbl.getCompanyTypeId()) {
						CompanyTypeTbl companyTypeTbl = session.get(CompanyTypeTbl.class,
								entityTempTbl.getCompanyTypeId().getCompanyTypeId());
						webEntityTemp.setCompanyTypeId(companyTypeTbl.getCompanyTypeId());
					}

//					if (entityTempTbl.getDocumentTblByDocumentAddnId() != null) {
//						Criteria documentTblCritria = session.createCriteria(DocumentTbl.class);
//						documentTblCritria.add(Restrictions.eq("activeFlag", Constants.ACTIVE));
//						documentTblCritria.add(Restrictions.eq("documentId",
//								entityTempTbl.getDocumentTblByDocumentAddnId().getDocumentId()));
//						DocumentTbl documentTbl = (DocumentTbl) documentTblCritria.uniqueResult();
//
//						WebDocumentTbl webDocument = new WebDocumentTbl();
//
//						if (documentTbl != null) {
//							webDocument.setDocumentName(documentTbl.getDocumentName());
//							webDocument.setDocumentFileExtn(documentTbl.getDocumentFileExtn());
//							webDocument.setDocumentId(documentTbl.getDocumentTypeTbl().getDocumentTypeId());
//							webEntityTemp.setWebDocument(webDocument);
//						}
//					}

					if (null != entityTempTbl.getBusinessOperationId()) {
						BusinessOperationTypeTbl businessOperationTypeTbl = session.get(BusinessOperationTypeTbl.class,
								entityTempTbl.getBusinessOperationId().getBusinessOperationId());
						webEntityTemp.setBusinessOperationId(businessOperationTypeTbl.getBusinessOperationId());
					}

//					if (null != entityTempTbl.getPaymentsId()) {
//						PaymentsTbl paymentsTbl = session.get(PaymentsTbl.class,
//								entityTempTbl.getPaymentsId().getPaymentsId());
//						webEntityTemp.setPaymentsId(paymentsTbl.getPaymentsId());
//					}

					webEntityTempList.add(webEntityTemp);
				}
			}

			if (webEntityTempList.isEmpty()) {
				return new ResponseEntity<>(new WebMessage("No records found"), HttpStatus.OK);
			}

		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in : getDashboardList" + e;
			log.error(errMessage, e);
		} finally {
			closeSession(session);
		}
		return new ResponseEntity<>(webEntityTempList, HttpStatus.OK);
	}

	@Override
	public ResponseEntity<?> getApprovedDashboardList() throws BviReturnsException {
		Session session = null;
		List<WebEntityTemp> webEntityTempList = new ArrayList<>();
		int approvedCount = 0;
		int rejectedCount = 0;

		try {
			session = getSession();

			Criteria entityTempCriteria = session.createCriteria(EntityTbl.class);
			entityTempCriteria.add(Restrictions.or(Restrictions.eq("authInd", Constants.APPROVE),
					Restrictions.eq("authInd", Constants.REJECT)));
			entityTempCriteria.addOrder(Order.desc("lastModifiedDatetime"));

			List<EntityTbl> entityTblList = entityTempCriteria.list();

			if (entityTblList != null) {
				for (EntityTbl entityTbl : entityTblList) {
					Integer userId = entityTbl.getUserId().getUserId();
					WebEntityTemp webEntityTemp = new WebEntityTemp();

					Criteria userSBLoginCriteria = session.createCriteria(UserSBLoginTbl.class);
					userSBLoginCriteria.add(Restrictions.eq("userId.userId", userId));
					UserSBLoginTbl userLoginTbl = (UserSBLoginTbl) userSBLoginCriteria.uniqueResult();

					webEntityTemp.setEntityId(entityTbl.getEntityId());
					webEntityTemp.setUsername(userLoginTbl.getUsername());
					webEntityTemp.setFirstname(entityTbl.getUserId().getFirstName());
					webEntityTemp.setLastname(entityTbl.getUserId().getLastName());
					webEntityTemp.setSbrNumber(entityTbl.getSbrNumber());
					webEntityTemp.setEntityName(entityTbl.getEntityName());
					webEntityTemp.setUrl(entityTbl.getUrl());
					webEntityTemp.setIncorporationDate(entityTbl.getIncorporationDate());
					webEntityTemp.setPhone(entityTbl.getPhone());
					webEntityTemp.setNumEmployees(entityTbl.getNumEmployees());
					webEntityTemp.setCompanyProfile(entityTbl.getCompanyProfile());
					webEntityTemp.setAddress(entityTbl.getAddress());
					webEntityTemp.setActiveFlag(entityTbl.getActiveFlag());
					webEntityTemp.setLastModifiedDatetime(new Date());
					webEntityTemp.setLastModifiedBy(userId);

					if (entityTbl.getAuthInd() == Constants.APPROVE) {
						webEntityTemp.setStatus("Approved");
						approvedCount++;
					} else {
						webEntityTemp.setStatus("Rejected");
						rejectedCount++;
					}

					webEntityTemp.setAuthInd(entityTbl.getAuthInd());
					webEntityTemp.setAuthBy(userId);
					webEntityTemp.setUserId(userId);
					webEntityTemp.setLogInd(entityTbl.getLogInd());

					if (null != entityTbl.getCompanyTypeId()) {
						CompanyTypeTbl companyTypeTbl = session.get(CompanyTypeTbl.class,
								entityTbl.getCompanyTypeId().getCompanyTypeId());
						webEntityTemp.setCompanyTypeId(companyTypeTbl.getCompanyTypeId());
					}

					if (null != entityTbl.getBusinessOperationId()) {
						BusinessOperationTypeTbl businessOperationTypeTbl = session.get(BusinessOperationTypeTbl.class,
								entityTbl.getBusinessOperationId().getBusinessOperationId());
						webEntityTemp.setBusinessOperationId(businessOperationTypeTbl.getBusinessOperationId());
					}

					if (!"Ammendments".equals(webEntityTemp.getTransactionName())) {
						webEntityTempList.add(webEntityTemp);
					}
				}
			} else {
				return new ResponseEntity<>(new WebMessage("No records found"), HttpStatus.OK);
			}
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in : getDashboardList" + e;
			log.error(errMessage, e);
		} finally {
			closeSession(session);
		}

		// Creating a response object with counts
		Map<String, Object> response = new HashMap<>();
		response.put("approvedCount", approvedCount);
		response.put("rejectedCount", rejectedCount);
		response.put("dashboardList", webEntityTempList);

		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@Override
	public ResponseEntity<?> approve(List<Integer> entityTempIds) throws BviReturnsException {
		Session session = null;
		// UserSBTbl authUser = null;
		EntityTempTbl entityTempTbl = null;
		EntityTbl entityTbl = null;
		PartnershipTbl partnershipTbl = null;
		CertificationTbl certificationTbl = null;
		CertificationTempTbl certificationTempTbl = null;
		// List<EntityTbl> entityTblList = null;

		try {
			session = getSession();
			// authUser = getLoggedInUser(session);

			for (Integer entityTempId : entityTempIds) {
				entityTempTbl = session.get(EntityTempTbl.class, entityTempId);
				Criteria criteria = session.createCriteria(EntityTbl.class);
				criteria.add(Restrictions.eq("sbrNumber", entityTempTbl.getSbrNumber()));
				entityTbl = (EntityTbl) criteria.uniqueResult();

				session.getTransaction().begin();

				// If userLicenceDetailsTbl is null, create a new instance
				if (entityTbl == null) {
					entityTbl = new EntityTbl();
				}

				// entityTbl = (EntityTbl) criteria.uniqueResult();

				session.getTransaction().begin();
				// If userLicenceDetailsTbl is null, create a new instance
				if (entityTbl == null) {
					entityTbl = new EntityTbl();
				}

				// Set values from temporary table to permanent table
				// Set values from temporary table to permanent table
				entityTbl.setEmailAddress(entityTempTbl.getEmailAddress());
				if (entityTempTbl.getEntityName() != null) {
					entityTbl.setEntityName(entityTempTbl.getEntityName());
				}

				if (entityTempTbl.getSbrNumber() != null) {
					entityTbl.setSbrNumber(entityTempTbl.getSbrNumber());
				}
				if (entityTempTbl.getPhone() != null) {
					entityTbl.setPhone(entityTempTbl.getPhone());
				}
				if (entityTempTbl.getUrl() != null) {
					entityTbl.setUrl(entityTempTbl.getUrl());
				}

//				if (entityTempTbl.getPaymentsId() != null) {
//					entityTbl.setPaymentId(entityTempTbl.getPaymentsId());
//				}

				if (entityTempTbl.getBusinessOperationId() != null) {
					entityTbl.setBusinessOperationId(entityTempTbl.getBusinessOperationId());
				}
				if (entityTempTbl.getBusinessPurposeId() != null) {
					entityTbl.setBusinessPurposeId(entityTempTbl.getBusinessPurposeId());
				}
				if (entityTempTbl.getCompanyTypeId() != null) {
					entityTbl.setCompanyTypeId(entityTempTbl.getCompanyTypeId());
				}
//				if (entityTempTbl.getTransactionTypeId() != null) {
//					entityTbl.setTransactionTypeId(entityTempTbl.getTransactionTypeId());
//				}
//				if (entityTempTbl.getDocumentTblByDocumentAddnId() != null) {
//					entityTbl.setDocumentTblByDocumentAddnId(entityTempTbl.getDocumentTblByDocumentAddnId());
//				}
				entityTbl.setActiveFlag(Constants.ACTIVE);

				entityTbl.setLastModifiedDatetime(new Date());

				entityTbl.setLastModifiedBy(entityTempTbl.getUserId());

				if (entityTempTbl.getIncorporationDate() != null) {
					entityTbl.setIncorporationDate(entityTempTbl.getIncorporationDate());
				}

				if (entityTempTbl.getNumEmployees() != null) {
					entityTbl.setNumEmployees(entityTempTbl.getNumEmployees());
				}
				if (entityTempTbl.getAddress() != null) {
					entityTbl.setAddress(entityTempTbl.getAddress());
				}
				if (entityTempTbl.getCompanyProfile() != null) {
					entityTbl.setCompanyProfile(entityTempTbl.getCompanyProfile());
				}
				if (entityTempTbl.getUserId() != null) {
					entityTbl.setUserId(entityTempTbl.getUserId());
				}

				if (entityTempTbl.getFederalEmployerIdentificationNumber() != null) {
					entityTbl.setFederalEmployerIdentificationNumber(
							entityTempTbl.getFederalEmployerIdentificationNumber());
				}

				if (entityTempTbl.getStateOfIncorporation() != null) {
					entityTbl.setStateOfIncorporation(entityTempTbl.getStateOfIncorporation());
				}

				if (entityTempTbl.getBusinessKeywords() != null) {
					entityTbl.setBusinessKeywords(entityTempTbl.getBusinessKeywords());
				}

				if (entityTempTbl.getAreasInterests() != null) {
					entityTbl.setAreasInterests(entityTempTbl.getAreasInterests());
				}

				if (entityTempTbl.getEstimatedAnnualRevenue() != null) {
					entityTbl.setEstimatedAnnualRevenue(entityTempTbl.getEstimatedAnnualRevenue());
				}

				if (entityTempTbl.getOwnerFirstName() != null) {
					entityTbl.setOwnerFirstName(entityTempTbl.getOwnerFirstName());
				}

				if (entityTempTbl.getOwnerLastName() != null) {
					entityTbl.setOwnerLastName(entityTempTbl.getOwnerLastName());
				}

				if (entityTempTbl.getOwnerPhone() != null) {
					entityTbl.setOwnerPhone(entityTempTbl.getOwnerPhone());
				}

				if (entityTempTbl.getOwnerEmail() != null) {
					entityTbl.setOwnerEmail(entityTempTbl.getOwnerEmail());
				}

				entityTbl.setSizeCompanyInd(entityTempTbl.getSizeCompanyInd());

				session.saveOrUpdate(entityTbl);

				Criteria associationCriteria = session.createCriteria(EntityAssociationTempTbl.class);
				associationCriteria.add(Restrictions.eq("entityTemp.entityTempId", entityTempTbl.getEntityTempId()));
				List<EntityAssociationTempTbl> associationList = associationCriteria.list();
				for (EntityAssociationTempTbl associationTemp : associationList) {
					EntityAssociationTbl association = new EntityAssociationTbl();
					association.setEntity(entityTbl);
					association.setAssociationName(associationTemp.getAssociationName());
					association.setMembershipStartDate(associationTemp.getMembershipStartDate());
					association.setMembershipEndDate(associationTemp.getMembershipEndDate());
					association.setMembershipLevel(associationTemp.getMembershipLevel());
					association.setActiveFlag(Constants.ACTIVE);
					association.setLastModifiedBy(entityTempTbl.getUserId());

					association.setLastModifiedDatetime(new Date());
					session.saveOrUpdate(association);
				}
			}

			Criteria partnershipCriteria = session.createCriteria(PartnershipTempTbl.class);
			partnershipCriteria.add(Restrictions.eq("entityTemp.entityTempId", entityTempTbl.getEntityTempId()));
			List<PartnershipTempTbl> partnershipList = partnershipCriteria.list();
			if (entityTempTbl.getEntityTempId() != null) {
				for (PartnershipTempTbl partnershipTemp : partnershipList) {
					PartnershipTbl partnership = new PartnershipTbl();
					partnership.setEntity(entityTbl);
					partnership.setPartnerCompanyName(partnershipTemp.getPartnerCompanyName());
					partnership.setPartnershipType(partnershipTemp.getPartnershipType());
					partnership.setPartnershipDescription(partnershipTemp.getPartnershipDescription());
					partnership.setPartnershipEndDate(partnershipTemp.getPartnershipEndDate());
					partnership.setActiveFlag(Constants.ACTIVE);
					partnership.setLastModifiedDatetime(new Date());
					partnership.setLastModifiedBy(entityTempTbl.getUserId()); // Assuming this is the logged-in user
					session.saveOrUpdate(partnership);
				}
			}

			Criteria certificationCriteria = session.createCriteria(CertificationTempTbl.class);
			certificationCriteria.add(Restrictions.eq("entityTemp.entityTempId", entityTempTbl.getEntityTempId()));
			List<CertificationTempTbl> certificationList = certificationCriteria.list();
			if (entityTempTbl.getEntityTempId() != null) {
				for (CertificationTempTbl cerificationTemp : certificationList) {
					CertificationTbl certification = new CertificationTbl();
					certification.setEntity(entityTbl);
					certification.setCertificationName(cerificationTemp.getCertificationName());
					certification.setCertificationDate(new Date());
					certification.setCertificationExpiryDate(new Date());
					certification.setCertificationIssuingBody(cerificationTemp.getCertificationIssuingBody());
					certification.setActiveFlag(Constants.ACTIVE);
					certification.setLastModifiedDatetime(new Date());
					certification.setLastModifiedBy(entityTempTbl.getUserId()); // Assuming this is the logged-in
																				// user
					session.saveOrUpdate(certification);
				}
			}

			UserDetailsTbl userLoginTbl = new UserDetailsTbl();
			Criteria userDetailsCriteria = session.createCriteria(UserDetailsTbl.class);
			userDetailsCriteria.add(Restrictions.eq("user.userId", entityTempTbl.getUserId().getUserId()));
			userLoginTbl = (UserDetailsTbl) userDetailsCriteria.uniqueResult();

			UserSBLoginTbl loginTbl = new UserSBLoginTbl();
			Criteria userCriteria = session.createCriteria(UserSBLoginTbl.class);
			userCriteria.add(Restrictions.eq("userId.userId", entityTempTbl.getUserId().getUserId()));
			loginTbl = (UserSBLoginTbl) userCriteria.uniqueResult();

			loginTbl.setActiveFlag(Constants.ACTIVE);

			// Update authInd and authBy in the temporary table
			entityTempTbl.setActiveFlag(' ');
			entityTempTbl.setAuthInd('A');
			entityTempTbl.setAuthBy(entityTempTbl.getUserId());
			entityTempTbl.setLastModifiedDatetime(new Date());
			session.saveOrUpdate(loginTbl);

			session.saveOrUpdate(entityTempTbl);
			entityTempTbl.setEntityId(entityTbl.getEntityId());

			session.getTransaction().commit();

			// Email content for approval
			String[] recipients = { userLoginTbl.getEmailAddress() };
			EmailDetails emailDetails = new EmailDetails();
			emailDetails.setToRecipients(recipients);
			emailDetails.setSubject("Small Business Registry (IAICC) - Your Registration Has Been Approved");
			emailDetails.setMessageContent("Dear " + entityTempTbl.getUserId().getFirstName() + " "
					+ entityTempTbl.getUserId().getLastName() + ",\n\n"
					+ "Congratulations! Your registration with the Small Business Registry (IAICC) has been **Approved**. Below are your login credentials to access the platform:\n"
					+ "Username: " + entityTbl.getUserId().getFirstName() + "\n" + // Adjust as necessary
					"If you encounter any issues or have any questions, feel free to contact our support team at [Support Email] or [Support Phone Number].\n\n"
					+ "Thank you for being a part of the Small Business Registry.\n\n" + "Best regards,\n"
					+ "The Small Business Registry (IAICC) Team\n" + "https://iaicc.org/ \n" + "<EMAIL>");
			// Send the email
			Runnable myrunnable = new Runnable() {
				public void run() {
					EmailManager emailManager = EmailManager.getInstance();
					try {
						emailManager.send(emailDetails);
					} catch (BviReturnsException e) {
						e.printStackTrace();
					}
				}
			};
			new Thread(myrunnable).start();

			// Return ResponseEntity based on the scenario
			return new ResponseEntity<>(new WebMessage(Constants.User.SUCCESSFUL), HttpStatus.OK);
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in : getApprovedCompanyList" + e;
			log.error(errMessage, e);
		} finally {
			closeSession(session);
		}
		return new ResponseEntity<>(null);
	}

	@Override
	public ResponseEntity<?> reject(String comments, List<Integer> entityTempIds) throws BviReturnsException {
		String logTag = "YourClassName : rejectUser()";
		log.info("Entered into : " + logTag);
		Session session = getSession();
		ActivitiResponse activitiResponse = new ActivitiResponse();
		try {
			// Get the logged-in user details

			UserSBTbl authUser = getLoggedInUser(session);
			for (Integer entityTempId : entityTempIds) {
				// Retrieve the UserLicenceDetailsTempTbl entityAM!cfT202!
				EntityTempTbl entityTempTbl = session.get(EntityTempTbl.class, entityTempId);

				// Begin transaction
				session.getTransaction().begin();
				UserDetailsTbl userLoginTbl = new UserDetailsTbl();
				Criteria userDetailsCriteria = session.createCriteria(UserDetailsTbl.class);
				userDetailsCriteria.add(Restrictions.eq("user.userId", entityTempTbl.getUserId().getUserId())); // Corrected
				userLoginTbl = (UserDetailsTbl) userDetailsCriteria.uniqueResult();

				UserSBLoginTbl loginTbl = new UserSBLoginTbl();
				Criteria userCriteria = session.createCriteria(UserSBLoginTbl.class);
				userCriteria.add(Restrictions.eq("userId.userId", entityTempTbl.getUserId().getUserId()));
				loginTbl = (UserSBLoginTbl) userCriteria.uniqueResult();

				loginTbl.setActiveFlag(Constants.INACTIVE);

				// Update authInd to 'R' in the temporary table
				// entityTempTbl.setComments(comments);
				entityTempTbl.setAuthInd('R');
				// entityTempTbl.setAuthDateTime(new Date());
				entityTempTbl.setAuthBy(entityTempTbl.getUserId());
				entityTempTbl.setLastModifiedDatetime(new Date());
				entityTempTbl.setActiveFlag(Constants.INACTIVE);
				// Save or update the temp table
				session.saveOrUpdate(loginTbl);
				session.saveOrUpdate(entityTempTbl);

				log.info("User rejection successful.");

				// Commit transaction
				session.getTransaction().commit();
				// Email content for rejection
				String[] rejectionRecipients = { userLoginTbl.getEmailAddress() };
				EmailDetails rejectionEmailDetails = new EmailDetails();
				rejectionEmailDetails.setToRecipients(rejectionRecipients);
				rejectionEmailDetails
						.setSubject("Small Business Registry (IAICC) - Your Registration Has Been Rejected");
				rejectionEmailDetails.setMessageContent("Dear " + entityTempTbl.getUserId().getFirstName() + " "
						+ entityTempTbl.getUserId().getLastName() + ",\n\n"
						+ "We regret to inform you that your registration with the Small Business Registry (IAICC) has been **Rejected**.\n\n"
						+ "If you believe this is an error or you would like more information regarding your registration, please reach out to our support <NAME_EMAIL> .\n\n"
						+ "Thank you for your interest in the Small Business Registry, and we hope to assist you in the future.\n\n"
						+ "Best regards,\n" + "The Small Business Registry (IAICC) Team\n" + "https://iaicc.org/ \n"
						+ "<EMAIL>");
				// Send the rejection email
				Runnable rejectionRunnable = new Runnable() {
					public void run() {
						EmailManager emailManager = EmailManager.getInstance();
						try {
							emailManager.send(rejectionEmailDetails);
						} catch (BviReturnsException e) {
							e.printStackTrace();
						}
					}
				};
				new Thread(rejectionRunnable).start();

			}
			// Return response
			return new ResponseEntity<>(new WebMessage(Constants.User.USER_REGISTRATION_REJECT), HttpStatus.OK);
		} catch (Exception e) {
			String errorMessage = logTag + " Exception occurred while rejecting user: " + e.getMessage();
			log.error(errorMessage, e);
			throw new BviReturnsException(errorMessage, e);
		} finally {
			closeSession(session);
		}
	}

	public ResponseEntity<?> ammendments(SBDetails sbDetails, DocumentTbl addndocument, Integer userId)
			throws BviReturnsException {

		Session session = null;
		Map<String, Object> variables = new HashMap<>();
		boolean registrationExists = false;

		try {
			session = getSession();
			session.getTransaction().begin();
			// Retrieve the EntityTempTbl record based on sbrNumber and authInd
			Criteria criteria = session.createCriteria(EntityTempTbl.class);
			criteria.add(Restrictions.eq("sbrNumber", sbDetails.getEntityTbl().getSbrNumber()));
			criteria.add(Restrictions.eq("authInd", Constants.APPROVE));

			EntityTempTbl entitytempTbl = (EntityTempTbl) criteria.uniqueResult();

			if (entitytempTbl == null) {
				return new ResponseEntity<>(new WebMessage(Constants.User.AMMENDMENTS_NOT_FOUND),
						HttpStatus.BAD_REQUEST);
			}
			// Update EntityTempTbl fields
			if (sbDetails.getEntityTbl() != null) {
				entitytempTbl.setActiveFlag(Constants.ACTIVE);
				entitytempTbl.setAuthInd(Constants.PENDING);

				if (sbDetails.getEntityTbl().getEntityName() != null)
					entitytempTbl.setEntityName(sbDetails.getEntityTbl().getEntityName());
				if (sbDetails.getEntityTbl().getEmailAddress() != null)
					entitytempTbl.setEmailAddress(sbDetails.getEntityTbl().getEmailAddress());
				if (sbDetails.getEntityTbl().getSbrNumber() != null)
					entitytempTbl.setSbrNumber(sbDetails.getEntityTbl().getSbrNumber());
				if (sbDetails.getEntityTbl().getUrl() != null)
					entitytempTbl.setUrl(sbDetails.getEntityTbl().getUrl());
				if (sbDetails.getEntityTbl().getPhone() != null)
					entitytempTbl.setPhone(sbDetails.getEntityTbl().getPhone());
				if (sbDetails.getEntityTbl().getIncorporationDate() != null)
					entitytempTbl.setIncorporationDate(sbDetails.getEntityTbl().getIncorporationDate());
				if (sbDetails.getEntityTbl().getNumEmployees() != null)
					entitytempTbl.setNumEmployees(sbDetails.getEntityTbl().getNumEmployees());
				if (sbDetails.getEntityTbl().getCompanyProfile() != null)
					entitytempTbl.setCompanyProfile(sbDetails.getEntityTbl().getCompanyProfile());
				if (sbDetails.getEntityTbl().getAddress() != null)
					entitytempTbl.setAddress(sbDetails.getEntityTbl().getAddress());
				if (sbDetails.getEntityTbl().getFederalEmployerIdentificationNumber() != null) {
					entitytempTbl.setFederalEmployerIdentificationNumber(
							sbDetails.getEntityTbl().getFederalEmployerIdentificationNumber());
				}
				if (sbDetails.getEntityTbl().getStateOfIncorporation() != null) {
					entitytempTbl.setStateOfIncorporation(sbDetails.getEntityTbl().getStateOfIncorporation());
				}
				if (sbDetails.getEntityTbl().getBusinessKeywords() != null) {
					entitytempTbl.setBusinessKeywords(sbDetails.getEntityTbl().getBusinessKeywords());
				}
				if (sbDetails.getEntityTbl().getAreasInterests() != null) {
					entitytempTbl.setAreasInterests(sbDetails.getEntityTbl().getAreasInterests());
				}
				if (sbDetails.getEntityTbl().getEstimatedAnnualRevenue() != null) {
					entitytempTbl.setEstimatedAnnualRevenue(sbDetails.getEntityTbl().getEstimatedAnnualRevenue());
				}
				if (sbDetails.getEntityTbl().getOwnerFirstName() != null) {
					entitytempTbl.setOwnerFirstName(sbDetails.getEntityTbl().getOwnerFirstName());
				}
				if (sbDetails.getEntityTbl().getOwnerLastName() != null) {
					entitytempTbl.setOwnerLastName(sbDetails.getEntityTbl().getOwnerLastName());
				}
				if (sbDetails.getEntityTbl().getOwnerPhone() != null) {
					entitytempTbl.setOwnerPhone(sbDetails.getEntityTbl().getOwnerPhone());
				}
				if (sbDetails.getEntityTbl().getOwnerEmail() != null) {
					entitytempTbl.setOwnerEmail(sbDetails.getEntityTbl().getOwnerEmail());
				}
				if (sbDetails.getEntityTbl().getSizeCompanyInd() != null) {
					entitytempTbl.setSizeCompanyInd(sbDetails.getEntityTbl().getSizeCompanyInd());
				}
				if (sbDetails.getEntityTbl().getUserId() != null) {
					entitytempTbl.setLastModifiedBy(sbDetails.getEntityTbl().getUserId());
				}
				entitytempTbl.setLastModifiedDatetime(new Date());

				// Set BusinessPurposeTbl if it exists
				if (null != sbDetails.getEntityTbl().getBusinessPurposeId()) {
					BusinessPurposeTbl businessPurposeTbl = session.get(BusinessPurposeTbl.class,
							sbDetails.getEntityTbl().getBusinessPurposeId().getBusinessPurposeId());
					entitytempTbl.setBusinessPurposeId(businessPurposeTbl);
				}

				// Set CompanyTypeTbl if it exists
				if (null != sbDetails.getEntityTbl().getCompanyTypeId()) {
					CompanyTypeTbl companyTypeTbl = session.get(CompanyTypeTbl.class,
							sbDetails.getEntityTbl().getCompanyTypeId().getCompanyTypeId());
					entitytempTbl.setCompanyTypeId(companyTypeTbl);
				}

				// Set BusinessOperationTypeTbl if it exists
				if (null != sbDetails.getEntityTbl().getBusinessOperationId()) {
					BusinessOperationTypeTbl businessOperationTypeTbl = session.get(BusinessOperationTypeTbl.class,
							sbDetails.getEntityTbl().getBusinessOperationId().getBusinessOperationId());
					entitytempTbl.setBusinessOperationId(businessOperationTypeTbl);
				}
				session.update(entitytempTbl); // Update entityTempTbl in the session
			} else {
				return new ResponseEntity<>(new WebMessage("EntityTbl is null"), HttpStatus.BAD_REQUEST);
			}
			// Handling EntityAssociationTempTbl updates and inserts
			if (sbDetails.getEntityAssociationTempTblList() != null
					&& !sbDetails.getEntityAssociationTempTblList().isEmpty()) {
				for (EntityAssociationTempTbl association : sbDetails.getEntityAssociationTempTblList()) {
					if (association.getEntityAssociationTempId() != null) {
						// Update if ID is provided
						EntityAssociationTempTbl existingAssociation = session.get(EntityAssociationTempTbl.class,
								association.getEntityAssociationTempId());
						if (existingAssociation != null) {
							existingAssociation.setEntityTemp(entitytempTbl);
							existingAssociation.setAssociationName(association.getAssociationName());
							existingAssociation.setMembershipStartDate(association.getMembershipStartDate());
							existingAssociation.setMembershipEndDate(association.getMembershipEndDate());
							existingAssociation.setMembershipLevel(association.getMembershipLevel());
							existingAssociation.setActiveFlag(Constants.ACTIVE);
							existingAssociation.setLastModifiedDatetime(new Date());
							existingAssociation.setLastModifiedBy(entitytempTbl.getUserId());
							session.update(existingAssociation);
						} else {
							System.out.println("No record found for id: " + association.getEntityAssociationTempId());
						}

					}
				}
				// Handling CertificationTempTbl updates and inserts
				if (sbDetails.getCertificationTempList() != null && !sbDetails.getCertificationTempList().isEmpty()) {
					for (CertificationTempTbl certification : sbDetails.getCertificationTempList()) {

						// Update existing certification
						CertificationTempTbl existingCertification = session.get(CertificationTempTbl.class,
								certification.getEntityCertificationTempId());
						if (existingCertification != null) {
							existingCertification.setCertificationDate(certification.getCertificationDate());
							existingCertification.setCertificationName(certification.getCertificationName());
							existingCertification
									.setCertificationExpiryDate(certification.getCertificationExpiryDate());
							existingCertification.setEntityTemp(entitytempTbl);
							existingCertification
									.setCertificationIssuingBody(certification.getCertificationIssuingBody());
							existingCertification.setLastModifiedDatetime(new Date());
							existingCertification.setLastModifiedBy(entitytempTbl.getUserId());
							session.update(existingCertification);
						} else {
							System.out.println("No record found for certification ID: "
									+ certification.getEntityCertificationTempId());
						}

					}
				}
				// Handling PartnershipTempTbl updates and inserts
				if (sbDetails.getPartnershipTempList() != null && !sbDetails.getPartnershipTempList().isEmpty()) {
					for (PartnershipTempTbl partnership : sbDetails.getPartnershipTempList()) {

						// Update existing partnership
						PartnershipTempTbl existingPartnership = session.get(PartnershipTempTbl.class,
								partnership.getEntityPartnershipTempId());
						if (existingPartnership != null) {
							existingPartnership.setPartnerCompanyName(partnership.getPartnerCompanyName());
							existingPartnership.setPartnershipDescription(partnership.getPartnershipDescription());
							existingPartnership.setPartnershipEndDate(partnership.getPartnershipEndDate());
							existingPartnership.setEntityTemp(entitytempTbl);

							// Retrieve the PartnershipTypeTbl object from the database based on its ID
							PartnershipTypeTbl partnershipType = session.get(PartnershipTypeTbl.class,
									partnership.getPartnershipType().getPartnershipTypeId());

							if (partnershipType != null) {
								// Set the PartnershipTypeTbl object on the PartnershipTempTbl
								existingPartnership.setPartnershipType(partnershipType);
							} else {
								System.out.println("Partnership type not found for ID: "
										+ partnership.getPartnershipType().getPartnershipTypeId());
							}
							existingPartnership.setLastModifiedDatetime(new Date());
							existingPartnership.setLastModifiedBy(entitytempTbl.getUserId());
							session.update(existingPartnership);
						} else {
							System.out.println(
									"No record found for partnership ID: " + partnership.getEntityPartnershipTempId());
						}
					}
				}
			}

			// Retrieve and update UserSBTbl
			UserSBTbl userTbl = session.get(UserSBTbl.class, userId);
			if (userTbl != null) {
				if (sbDetails.getUserTbl().getFirstName() != null) {
					userTbl.setFirstName(sbDetails.getUserTbl().getFirstName());
				}
				if (sbDetails.getUserTbl().getLastName() != null) {
					userTbl.setLastName(sbDetails.getUserTbl().getLastName());
				}
				userTbl.setLastModifiedDatetime(new Date());
				userTbl.setActiveFlag(Constants.ACTIVE);
				session.update(userTbl);
			} else {
				return new ResponseEntity<>(new WebMessage("UserTbl details not found"), HttpStatus.BAD_REQUEST);
			}

			// Retrieve and update UserSBLoginTbl
			Criteria userSBLoginCriteria = session.createCriteria(UserSBLoginTbl.class);
			userSBLoginCriteria.add(Restrictions.eq("userId.userId", userId));

			UserSBLoginTbl userLoginTbl = (UserSBLoginTbl) userSBLoginCriteria.uniqueResult();

			if (userLoginTbl != null) {
//		            if (sbDetails.getUserLoginTbl().getUsername() != null) {
//		                userLoginTbl.setUsername(sbDetails.getUserLoginTbl().getUsername());
//		            }
//		            if (sbDetails.getUserLoginTbl().getPassword() != null) {
//		                userLoginTbl.setPassword(sbDetails.getUserLoginTbl().getPassword());
//		            }
				userLoginTbl.setLastModifiedDatetime(new Date());
				userLoginTbl.setActiveFlag(Constants.ACTIVE);
				userLoginTbl.setLastModifiedBy(userTbl);
				session.update(userLoginTbl);
			} else {
				return new ResponseEntity<>(new WebMessage("UserLoginTbl details not found"), HttpStatus.BAD_REQUEST);
			}

			// Retrieve and update UserDetailsTbl
			Criteria userDetailsCriteria = session.createCriteria(UserDetailsTbl.class);
			userDetailsCriteria.add(Restrictions.eq("user.userId", userId));
			UserDetailsTbl userDetailsTbl = (UserDetailsTbl) userDetailsCriteria.uniqueResult();

			if (userDetailsTbl != null) {
				if (sbDetails.getUserDetailsTbl().getEmailAddress() != null) {
					userDetailsTbl.setEmailAddress(sbDetails.getUserDetailsTbl().getEmailAddress());
				}
				if (sbDetails.getUserDetailsTbl().getPhone() != null) {
					userDetailsTbl.setPhone(sbDetails.getUserDetailsTbl().getPhone());
				}
				userDetailsTbl.setLastModifiedDatetime(new Date());
				userDetailsTbl.setActiveFlag(Constants.ACTIVE);
				userDetailsTbl.setLastModifiedBy(userTbl);
				session.update(userDetailsTbl);
			} else {
				return new ResponseEntity<>(new WebMessage("User details not found"), HttpStatus.BAD_REQUEST);
			}

			// Commit transaction
			session.getTransaction().commit();
			// Email content for changes pending approval
			String[] rejectionRecipients = { userDetailsTbl.getEmailAddress() };
			EmailDetails rejectionEmailDetails = new EmailDetails();
			rejectionEmailDetails.setToRecipients(rejectionRecipients);
			rejectionEmailDetails.setSubject("Small Business Registry (IAICC) - Your Changes Are Pending Approval");

			rejectionEmailDetails.setMessageContent("Dear " + entitytempTbl.getUserId().getFirstName() + " "
					+ entitytempTbl.getUserId().getLastName() + ",\n\n"
					+ "Thank you for your recent submission to the Small Business Registry (IAICC). "
					+ "We would like to inform you that your changes have been submitted successfully and are currently Pending for Approval.\n\n"
					+ "If you have any questions or need further assistance regarding your submission, please do not hesitate to reach out to our support <NAME_EMAIL>.\n\n"
					+ "We appreciate your patience and thank you for your interest in the Small Business Registry. "
					+ "We look forward to assisting you further.\n\n" + "Best regards,\n"
					+ "The Small Business Registry (IAICC) Team\n" + "https://iaicc.org/\n" + "<EMAIL>");

			// Send the email
			Runnable rejectionRunnable = new Runnable() {
				public void run() {
					EmailManager emailManager = EmailManager.getInstance();
					try {
						emailManager.send(rejectionEmailDetails);
					} catch (BviReturnsException e) {
						e.printStackTrace();
					}
				}
			};
			new Thread(rejectionRunnable).start();

			return new ResponseEntity<>(new WebMessage(Constants.User.AMMENDMENTS_PENDING, Constants.SUCCESS),
					HttpStatus.OK);

		} catch (Exception e) {
			if (session.getTransaction() != null) {
				session.getTransaction().rollback();
			}
			log.error("Exception occurred in ammendments(): ", e);
			throw new BviReturnsException(e);
		} finally {
			closeSession(session);
		}
	}

	@Override
	public UserSBTbl getLoggedInUser(Session session) {
		String logTag = "getLoggedInUserDetails(): ";
		log.info("Entering into : " + logTag);
		String username = null;

		try {
			Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			if (principal instanceof UserDetails) {
				username = ((UserDetails) principal).getUsername();
			} else {
				username = principal.toString();
			}
		} catch (Exception e) {
			log.error("From Import Batch job: So No logged in User");
			username = "bviadmin";
		}

		if (session == null) {
			session = getSession();
		}
		Query query = session.createSQLQuery("SELECT USER_ID FROM " + schema
				+ ".dbo.USER_LOGIN_TBL where USERNAME= :username AND ACTIVE_FLAG = :active");
		query.setParameter("username", username);
		query.setParameter("active", Constants.ACTIVE);
		Integer userId = (Integer) query.uniqueResult();
		Criteria criteria = session.createCriteria(UserSBTbl.class);
		criteria.add(Restrictions.eq("userId", userId));
		criteria.add(Restrictions.eq("activeFlag", Constants.ACTIVE));
		return (UserSBTbl) criteria.uniqueResult();
	}

	@Override
	public ResponseEntity<?> getAmmendmentsDashboardList(Integer userId) throws BviReturnsException {
		Session session = null;
		List<WebEntityTemp> webEntityList = new ArrayList<>();

		try {
			session = getSession();

			Criteria entityCriteria = session.createCriteria(EntityTbl.class);
			entityCriteria.add(Restrictions.eq("userId.userId", userId));
			entityCriteria.addOrder(Order.desc("lastModifiedDatetime"));
			List<EntityTbl> entityTblList = entityCriteria.list();

			if (entityTblList != null) {
				Map<Integer, Integer> companiesPendingApprovalCountMap = new HashMap<>();
				for (EntityTbl entityTbl : entityTblList) {

					WebEntityTemp webEntity = new WebEntityTemp();

					Criteria userSBLoginCriteria = session.createCriteria(UserSBLoginTbl.class);
					userSBLoginCriteria.add(Restrictions.eq("userId.userId", userId));

					UserSBLoginTbl userLoginTbl = (UserSBLoginTbl) userSBLoginCriteria.uniqueResult();

					webEntity.setEntityId(entityTbl.getEntityId());
					webEntity.setUsername(userLoginTbl.getUsername());
					webEntity.setFirstname(entityTbl.getUserId().getFirstName());
					webEntity.setLastname(entityTbl.getUserId().getLastName());

					// webEntity.setEntityNumber(entityTbl.getEntityNumber());
					webEntity.setSbrNumber(entityTbl.getSbrNumber());
					webEntity.setEntityName(entityTbl.getEntityName());
					webEntity.setEmailAddress(entityTbl.getEmailAddress());
					webEntity.setUrl(entityTbl.getUrl());
					webEntity.setIncorporationDate(entityTbl.getIncorporationDate());
					webEntity.setLogInd(entityTbl.getLogInd());

					if (entityTbl.getUrl() != null)
						webEntity.setUrl(entityTbl.getUrl());
					if (entityTbl.getIncorporationDate() != null) {
						webEntity.setIncorporationDate(entityTbl.getIncorporationDate());
					}
					if (entityTbl.getPhone() != null) {
						webEntity.setPhone(entityTbl.getPhone());
					}
//					if (entityTbl.getCompanyHead() != null) {
//						webEntity.setCompanyHead(entityTbl.getCompanyHead());
//					}
					if (entityTbl.getNumEmployees() != null) {
						webEntity.setNumEmployees(entityTbl.getNumEmployees());
					}
					if (entityTbl.getCompanyProfile() != null) {
						webEntity.setCompanyProfile(entityTbl.getCompanyProfile());
					}
					if (entityTbl.getAddress() != null) {
						webEntity.setAddress(entityTbl.getAddress());
					}
					if (entityTbl.getEmailAddress() != null) {
						webEntity.setEmailAddress(entityTbl.getEmailAddress());
					}
					if (entityTbl.getSizeCompanyInd() != null) {
						webEntity.setSizeCompanyInd(entityTbl.getSizeCompanyInd());
					}
					if (entityTbl.getOwnerFirstName() != null) {
						webEntity.setOwnerFirstName(entityTbl.getOwnerFirstName());
					}
					if (entityTbl.getOwnerLastName() != null) {
						webEntity.setOwnerLastName(entityTbl.getOwnerLastName());
					}
					if (entityTbl.getOwnerEmail() != null) {
						webEntity.setOwnerEmail(entityTbl.getOwnerEmail());
					}
					if (entityTbl.getOwnerPhone() != null) {
						webEntity.setOwnerPhone(entityTbl.getOwnerPhone());
					}
					if (entityTbl.getAreasInterests() != null) {
						webEntity.setAreasInterests(entityTbl.getAreasInterests());
					}
					if (entityTbl.getStateOfIncorporation() != null) {
						webEntity.setStateOfIncorporation(entityTbl.getStateOfIncorporation());
					}
					if (entityTbl.getBusinessKeywords() != null) {
						webEntity.setBusinessKeywords(entityTbl.getBusinessKeywords());
					}
					if (entityTbl.getEstimatedAnnualRevenue() != null) {
						webEntity.setEstimatedannualrevenue(entityTbl.getEstimatedAnnualRevenue());
					}
					if (entityTbl.getFederalEmployerIdentificationNumber() != null) {
						webEntity.setFederalEmployerIdentificationNumber(
								entityTbl.getFederalEmployerIdentificationNumber());
					}
					if (entityTbl.getFederalEmployerIdentificationNumber() != null) {
						webEntity.setFederalEmployerIdentificationNumber(
								entityTbl.getFederalEmployerIdentificationNumber());
					}
					webEntity.setLastModifiedDatetime(new Date());
					webEntity.setLastModifiedBy(userId);

					if (null != entityTbl.getCompanyTypeId()) {
						CompanyTypeTbl companyTypeTbl = session.get(CompanyTypeTbl.class,
								entityTbl.getCompanyTypeId().getCompanyTypeId());
						webEntity.setCompanyTypeId(companyTypeTbl.getCompanyTypeId());
					}

//					if (entityTbl.getDocumentTblByDocumentAddnId() != null) {
//						Criteria documentTblCritria = session.createCriteria(DocumentTbl.class);
//						documentTblCritria.add(Restrictions.eq("activeFlag", Constants.ACTIVE));
//						documentTblCritria.add(Restrictions.eq("documentId",
//								entityTbl.getDocumentTblByDocumentAddnId().getDocumentId()));
//						DocumentTbl documentTbl = (DocumentTbl) documentTblCritria.uniqueResult();
//
//						WebDocumentTbl webDocument = new WebDocumentTbl();
//
//						if (documentTbl != null) {
//							webDocument.setDocumentName(documentTbl.getDocumentName());
//							webDocument.setDocumentFileExtn(documentTbl.getDocumentFileExtn());
//							webDocument.setDocumentId(documentTbl.getDocumentTypeTbl().getDocumentTypeId());
//							webEntity.setWebDocument(webDocument);
//						}
//					}

					// Fetch Association Details
					Criteria associationCriteria = session.createCriteria(EntityAssociationTbl.class);
					associationCriteria.add(Restrictions.eq("entity.entityId", entityTbl.getEntityId()));
					List<EntityAssociationTbl> associationList = associationCriteria.list();
					UserSBTbl user1 = new UserSBTbl();
					user1.setUserId(userId);
					if (associationList != null && !associationList.isEmpty()) {
						for (EntityAssociationTbl association : associationList) {

							WebAssociationTempTbl webAssociationTbl = new WebAssociationTempTbl();
							webAssociationTbl.setWebentityAssociationTempId(association.getEntityAssociationId());
							webAssociationTbl.setAssociationName(association.getAssociationName());
							webAssociationTbl.setMembershipStartDate(association.getMembershipStartDate());
							webAssociationTbl.setMembershipEndDate(association.getMembershipEndDate());
							webAssociationTbl.setMembershipLevel(association.getMembershipLevel());
							webAssociationTbl.setActiveFlag(association.getActiveFlag());
							webAssociationTbl.setLastModifiedDatetime(association.getLastModifiedDatetime());
							webAssociationTbl.setLastModifiedBy(user1);
							webEntity.setWebAssociationTempTbl(webAssociationTbl);

						}
					}

					// Fetch Partnership Details
					Criteria partnershipCriteria = session.createCriteria(PartnershipTbl.class);
					partnershipCriteria.add(Restrictions.eq("entity.entityId", entityTbl.getEntityId()));
					List<PartnershipTbl> partnershipList = partnershipCriteria.list();
					WebUser user = new WebUser();
					user.setUserId(userId);
					if (partnershipList != null && !partnershipList.isEmpty()) {
						for (PartnershipTbl partnership : partnershipList) {
							WebPartershipTbl webPartnership = new WebPartershipTbl();
							webPartnership.setEntityPartnershipId(partnership.getEntityPartnershipId());
							webPartnership.setPartnerCompanyName(partnership.getPartnerCompanyName());
							webPartnership.setPartnershipEndDate(partnership.getPartnershipEndDate());
							webPartnership.setPartnershipDescription(partnership.getPartnershipDescription());
							webPartnership.setActiveFlag(partnership.getActiveFlag());
							webPartnership.setLastModifiedDatetime(partnership.getLastModifiedDatetime());
							webPartnership.setPartnershipEndDate(partnership.getPartnershipEndDate());

							// webPartnership.setLastModifiedBy(userId);
							webPartnership.setLastModifiedBy(user);
							// Set the partnership to the webEntityTemp
							webEntity.setWebPartnership(webPartnership);
						}
					}

					// Fetch Cerfication Details

					Criteria cerficationCriteria = session.createCriteria(CertificationTbl.class);
					cerficationCriteria.add(Restrictions.eq("entity.entityId", entityTbl.getEntityId()));
					List<CertificationTbl> certificationList = cerficationCriteria.list();
					UserSBTbl user2 = new UserSBTbl();
					user2.setUserId(userId);
					if (certificationList != null && !certificationList.isEmpty()) {
						for (CertificationTbl certification : certificationList) {
							WebCertificationTbl webCertification = new WebCertificationTbl();
							webCertification.setEntityCertificationId(certification.getEntityCertificationId());
							webCertification.setCertificationName(certification.getCertificationName());
							webCertification.setCertificationExpiryDate(certification.getCertificationDate());
							webCertification.setCertificationDate(certification.getCertificationDate());
							webCertification.setCertificationIssuingBody(certification.getCertificationIssuingBody());
							webCertification.setActiveFlag(certification.getActiveFlag());
							webCertification.setLastModifiedDatetime(certification.getLastModifiedDatetime());
							webCertification.setLastModifiedBy(user2);

							// Set the partnership to the webEntityTemp
							webEntity.setWebCertificationTbl(webCertification);
						}
					}
					webEntityList.add(webEntity);
				}
			} else {
				return new ResponseEntity<>(new WebMessage("No records found"), HttpStatus.OK);
			}
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in : getDashboardList" + e;
			log.error(errMessage, e);
		} finally {
			closeSession(session);
		}
		return new ResponseEntity<>(webEntityList, HttpStatus.OK);
	}

	@Override
	public ResponseEntity<?> getSearchDetails(String sbNumber, String startDate, String endDate, String entityName,
			Integer businessOperationId, String companyHead, String url, String status, Integer companyTypeId,
			String AreasInterests, String BusinessKeywords) throws BviReturnsException {
		Session session = null;
		List<WebEntityTemp> webEntityList = new ArrayList<>();
		Date endsDate = null;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		try {
			session = getSession();

			Criteria entityCriteria = session.createCriteria(EntityTbl.class);

			Calendar c2 = Calendar.getInstance();
			if (endDate != null && !"".equalsIgnoreCase(endDate)) {
				c2.setTime(sdf.parse(endDate));
				c2.add(Calendar.DAY_OF_MONTH, 1);
				endsDate = c2.getTime();
			}

			// Add search parameters
			if (sbNumber != null) {
				entityCriteria.add(Restrictions.eq("sbrNumber", sbNumber));
			}
			if ((startDate != null) && (endsDate != null)) {
				entityCriteria.add(
						Restrictions.between("incorporationDate", new SimpleDateFormat("yyyy-MM-dd").parse(startDate),
								new SimpleDateFormat("yyyy-MM-dd").parse(sdf.format(endsDate))));
			}
			if (entityName != null) {
				entityCriteria.add(Restrictions.like("entityName", "%" + entityName + "%"));
			}

			if (AreasInterests != null) {
				entityCriteria.add(Restrictions.like("AreasInterests", "%" + AreasInterests + "%"));
			}

			if (BusinessKeywords != null) {
				entityCriteria.add(Restrictions.like("BusinessKeywords", "%" + BusinessKeywords + "%"));
			}

			if (companyTypeId != null) {
				entityCriteria.add(Restrictions.eq("companyTypeId.companyTypeId", companyTypeId));
			}
			if (businessOperationId != null) {
				entityCriteria.add(Restrictions.eq("businessOperationId.businessOperationId", businessOperationId));
			}

			if (companyHead != null) {
				entityCriteria.add(Restrictions.like("companyHead", "%" + companyHead + "%"));
			}
			if (url != null) {
				entityCriteria.add(Restrictions.like("url", "%" + url + "%"));
			}

			entityCriteria.addOrder(Order.desc("lastModifiedDatetime"));
			List<EntityTbl> entityTblList = entityCriteria.list();

			if (entityTblList != null) {
				for (EntityTbl entityTempTbl : entityTblList) {
					WebEntityTemp webEntity = new WebEntityTemp();

					// Set values from EntityTempTbl to WebEntityTemp
					webEntity.setEntityId(entityTempTbl.getEntityId());
					webEntity.setFirstname(entityTempTbl.getUserId().getFirstName());
					webEntity.setLastname(entityTempTbl.getUserId().getLastName());
					// webEntity.setEntityNumber(entityTempTbl.getEntityNumber());
					webEntity.setSbrNumber(entityTempTbl.getSbrNumber());
					webEntity.setEntityName(entityTempTbl.getEntityName());
					webEntity.setUrl(entityTempTbl.getUrl());
					webEntity.setIncorporationDate(entityTempTbl.getIncorporationDate());
					webEntity.setEmailAddress(entityTempTbl.getEmailAddress());
					webEntity.setPhone(entityTempTbl.getPhone());
					webEntity.setAddress(entityTempTbl.getEmailAddress());
					webEntity.setSizeCompanyInd(entityTempTbl.getSizeCompanyInd());
					webEntity.setFederalEmployerIdentificationNumber(
							entityTempTbl.getFederalEmployerIdentificationNumber());
					webEntity.setStateOfIncorporation(entityTempTbl.getStateOfIncorporation());
					webEntity.setBusinessKeywords(entityTempTbl.getBusinessKeywords());
					webEntity.setUrl(entityTempTbl.getUrl());
					webEntity.setNumEmployees(entityTempTbl.getNumEmployees());
					webEntity.setAreasInterests(entityTempTbl.getAreasInterests());
					webEntity.setOwnerEmail(entityTempTbl.getOwnerEmail());
					webEntity.setOwnerFirstName(entityTempTbl.getOwnerFirstName());
					webEntity.setOwnerLastName(entityTempTbl.getOwnerLastName());
					webEntity.setOwnerPhone(entityTempTbl.getOwnerPhone());
					webEntity.setCompanyProfile(entityTempTbl.getCompanyProfile());
					webEntity.setAddress(entityTempTbl.getAddress());
					webEntity.setEstimatedannualrevenue(entityTempTbl.getEstimatedAnnualRevenue());
					webEntity.setAreasInterests(entityTempTbl.getAreasInterests());
					webEntity.setBusinessKeywords(entityTempTbl.getBusinessKeywords());

					if (entityTempTbl.getBusinessPurposeId() != null)
						webEntity.setBusinessPurposeId(entityTempTbl.getBusinessPurposeId().getBusinessPurposeId());

					if (entityTempTbl.getBusinessOperationId() != null)
						webEntity.setBusinessOperationId(
								entityTempTbl.getBusinessOperationId().getBusinessOperationId());

					if (entityTempTbl.getCompanyTypeId() != null)
						webEntity.setCompanyTypeId(entityTempTbl.getCompanyTypeId().getCompanyTypeId());

					// Add to result list
					webEntityList.add(webEntity);
				}
			} else {
				return new ResponseEntity<>(new WebMessage("No records found"), HttpStatus.OK);
			}
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in : getDashboardList" + e;
			log.error(errMessage, e);
		} finally {
			closeSession(session);
		}
		if (webEntityList.isEmpty()) {
			return new ResponseEntity<>(new WebMessage("No records found"), HttpStatus.OK);
		}
		return new ResponseEntity<>(webEntityList, HttpStatus.OK);
	}

	@Override
	public ResponseEntity<?> getBussinessPurposeDetails() throws BviReturnsException {
		Session session = null;
		List<BusinessPurposeTbl> businessPurposeList = new ArrayList<>();

		try {
			// Obtain session
			session = getSession();

			// Use criteria to get all BusinessPurposeTbl records
			Criteria entityBusiness = session.createCriteria(BusinessPurposeTbl.class);

			// Get the list of BusinessPurposeTbl
			businessPurposeList = entityBusiness.list();

			// Check if the list is empty
			if (businessPurposeList.isEmpty()) {
				return new ResponseEntity<>(new WebMessage("Business purposes not found"), HttpStatus.NOT_FOUND);
			}
		} catch (Exception e) {
			// Log and handle the exception
			String errMessage = "Exception occurred in : getBussinessPurposeDetails " + e;
			log.error(errMessage, e);
			throw new BviReturnsException("Error retrieving business purpose details", e);
		} finally {
			// Close session
			closeSession(session);
		}

		// Return 200 OK with the business purpose details if found
		return new ResponseEntity<>(businessPurposeList, HttpStatus.OK);
	}

	@Override
	public ResponseEntity<?> getPartnershipDetails() throws BviReturnsException {
		Session session = null;
		List<PartnershipTypeTbl> partnershipTypeList = new ArrayList<>();

		try {
			// Obtain session
			session = getSession();

			// Use criteria to get all PartnershipTypeTbl records
			Criteria entityPartnership = session.createCriteria(PartnershipTypeTbl.class);

			// Get the list of PartnershipTypeTbl
			partnershipTypeList = entityPartnership.list();

			// Check if the list is empty
			if (partnershipTypeList.isEmpty()) {
				return new ResponseEntity<>(new WebMessage("Partnership details not found"), HttpStatus.NOT_FOUND);
			}
		} catch (Exception e) {
			// Log and handle the exception
			String errMessage = "Exception occurred in : getPartnershipDetails " + e;
			log.error(errMessage, e);
			throw new BviReturnsException("Error retrieving partnership details", e);
		} finally {
			// Close session
			closeSession(session);
		}

		// Return 200 OK with the partnership details if found
		return new ResponseEntity<>(partnershipTypeList, HttpStatus.OK);
	}

	@Override
	public ResponseEntity<?> getBussinessOperationDetails() throws BviReturnsException {
		Session session = null;
		List<BusinessOperationTypeTbl> BusinessOperationTypeList = new ArrayList<>();

		try {
			// Obtain session
			session = getSession();

			Criteria entityBussinessOperation = session.createCriteria(BusinessOperationTypeTbl.class);

			BusinessOperationTypeList = entityBussinessOperation.list();

			// Check if the list is empty
			if (BusinessOperationTypeList.isEmpty()) {
				return new ResponseEntity<>(new WebMessage("BusinessOperation details not found"),
						HttpStatus.NOT_FOUND);
			}
		} catch (Exception e) {
			// Log and handle the exception
			String errMessage = "Exception occurred in : getBussinessOperationDetails " + e;
			log.error(errMessage, e);
			throw new BviReturnsException("Error retrieving getBussinessOperationDetails ", e);
		} finally {
			// Close session
			closeSession(session);
		}

		// Return 200 OK with the partnership details if found
		return new ResponseEntity<>(BusinessOperationTypeList, HttpStatus.OK);
	}

	@Override
	public ResponseEntity<?> getCompanyTypeDetails() throws BviReturnsException {
		Session session = null;
		List<CompanyTypeTbl> CompanyTypeTblList = new ArrayList<>();

		try {
			// Obtain session
			session = getSession();

			Criteria entityCompanyType = session.createCriteria(CompanyTypeTbl.class);

			CompanyTypeTblList = entityCompanyType.list();

			// Check if the list is empty
			if (CompanyTypeTblList.isEmpty()) {
				return new ResponseEntity<>(new WebMessage("CompanyTypeTblList details not found"),
						HttpStatus.NOT_FOUND);
			}
		} catch (Exception e) {
			// Log and handle the exception
			String errMessage = "Exception occurred in : getCompanyTypeTblList details " + e;
			log.error(errMessage, e);
			throw new BviReturnsException("Error retrieving getCompanyTypeTblList details ", e);
		} finally {
			// Close session
			closeSession(session);
		}

		// Return 200 OK with the partnership details if found
		return new ResponseEntity<>(CompanyTypeTblList, HttpStatus.OK);
	}

	@Override
	public ResponseEntity<?> getRegistrationDashboardList(String sbrNumber) throws BviReturnsException {
		Session session = null;
		List<WebEntityTemp> webEntityList = new ArrayList<>();

		try {
			session = getSession();

			// Create criteria to filter by sbrNumber in EntityTbl
			Criteria entityCriteria = session.createCriteria(EntityTbl.class);
			entityCriteria.add(Restrictions.eq("sbrNumber", sbrNumber));
			entityCriteria.addOrder(Order.desc("lastModifiedDatetime"));
			List<EntityTbl> entityTblList = entityCriteria.list();

			if (entityTblList != null && !entityTblList.isEmpty()) {
				for (EntityTbl entityTbl : entityTblList) {
					WebEntityTemp webEntityTemp = new WebEntityTemp();

					// Check for associated userId and retrieve user login details if available
					if (entityTbl.getUserId() != null) {
						Integer userId = entityTbl.getUserId().getUserId();

						Criteria userSBLoginCriteria = session.createCriteria(UserSBLoginTbl.class);
						userSBLoginCriteria.add(Restrictions.eq("userId.userId", userId)); // Assuming `userId` exists

						// Get user login details (handle multiple results if necessary)
						UserSBLoginTbl userLoginTbl = (UserSBLoginTbl) userSBLoginCriteria.uniqueResult();
						if (userLoginTbl != null) {
							webEntityTemp.setUsername(userLoginTbl.getUsername());
							webEntityTemp.setUserId(userId);

						}
						// GeT UserDetails
						Criteria userDetailsCriteria = session.createCriteria(UserDetailsTbl.class);
						userDetailsCriteria.add(Restrictions.eq("user.userId", userId));
						UserDetailsTbl userDetailsTbl = (UserDetailsTbl) userDetailsCriteria.uniqueResult();
						if (userDetailsTbl != null) {
							webEntityTemp.setUseremailAddress(userDetailsTbl.getEmailAddress());
							webEntityTemp.setUserphone(userDetailsTbl.getPhone());
						}

					}

					// Set entity details
					webEntityTemp.setEntityId(entityTbl.getEntityId());
					webEntityTemp.setEntityTempId(entityTbl.getEntityId());
					webEntityTemp.setFirstname(entityTbl.getUserId().getFirstName());
					webEntityTemp.setLastname(entityTbl.getUserId().getLastName());
					webEntityTemp.setSbrNumber(entityTbl.getSbrNumber());
					webEntityTemp.setEntityName(entityTbl.getEntityName());
					webEntityTemp.setEmailAddress(entityTbl.getEmailAddress());
					webEntityTemp.setUrl(entityTbl.getUrl());
					webEntityTemp.setIncorporationDate(entityTbl.getIncorporationDate());
					webEntityTemp.setPhone(entityTbl.getPhone());
					webEntityTemp.setNumEmployees(entityTbl.getNumEmployees());
					webEntityTemp.setCompanyProfile(entityTbl.getCompanyProfile());
					webEntityTemp.setAddress(entityTbl.getAddress());
					webEntityTemp.setSizeCompanyInd(entityTbl.getSizeCompanyInd());
					webEntityTemp.setOwnerFirstName(entityTbl.getOwnerFirstName());
					webEntityTemp.setOwnerLastName(entityTbl.getOwnerLastName());
					webEntityTemp.setOwnerPhone(entityTbl.getOwnerPhone());
					webEntityTemp.setOwnerEmail(entityTbl.getOwnerEmail());
					webEntityTemp.setAreasInterests(entityTbl.getAreasInterests());
					webEntityTemp.setStateOfIncorporation(entityTbl.getStateOfIncorporation());
					webEntityTemp.setBusinessKeywords(entityTbl.getBusinessKeywords());
					webEntityTemp.setEstimatedannualrevenue(entityTbl.getEstimatedAnnualRevenue());
					webEntityTemp
							.setFederalEmployerIdentificationNumber(entityTbl.getFederalEmployerIdentificationNumber());
					webEntityTemp.setLastModifiedDatetime(entityTbl.getLastModifiedDatetime());
					webEntityTemp.setSizeCompanyInd(entityTbl.getSizeCompanyInd());
					webEntityTemp.setEmailAddress(entityTbl.getEmailAddress());
					webEntityTemp.setAuthInd(entityTbl.getAuthInd());
					webEntityTemp.setLogInd(entityTbl.getLogInd());
					// webEntity.setTransactionName(entityTbl.getTransactionTypeId().getTransactionName());
					// Set company type
					if (entityTbl.getCompanyTypeId() != null) {
						CompanyTypeTbl companyTypeTbl = session.get(CompanyTypeTbl.class,
								entityTbl.getCompanyTypeId().getCompanyTypeId());
						webEntityTemp.setCompanyTypeId(companyTypeTbl.getCompanyTypeId());
					}

					// Retrieve and set document information if present
//					if (entityTempTbl.getDocumentTblByDocumentAddnId() != null) {
//						Criteria documentTblCriteria = session.createCriteria(DocumentTbl.class);
//						documentTblCriteria.add(Restrictions.eq("activeFlag", Constants.ACTIVE));
//						documentTblCriteria.add(Restrictions.eq("documentId",
//								entityTempTbl.getDocumentTblByDocumentAddnId().getDocumentId()));
//
//						DocumentTbl documentTbl = (DocumentTbl) documentTblCriteria.uniqueResult();
//
//						if (documentTbl != null) {
//							WebDocumentTbl webDocument = new WebDocumentTbl();
//							webDocument.setDocumentName(documentTbl.getDocumentName());
//							webDocument.setDocumentFileExtn(documentTbl.getDocumentFileExtn());
//							webDocument.setDocumentId(documentTbl.getDocumentTypeTbl().getDocumentTypeId());
//							webEntityTemp.setWebDocument(webDocument);
//						}
//					}

					if (null != entityTbl.getBusinessOperationId()) {
						BusinessOperationTypeTbl businessOperationTypeTbl = session.get(BusinessOperationTypeTbl.class,
								entityTbl.getBusinessOperationId().getBusinessOperationId());
						webEntityTemp.setBusinessOperationId(businessOperationTypeTbl.getBusinessOperationId());
					}

					if (null != entityTbl.getBusinessPurposeId()) {
						BusinessPurposeTbl businessPurposeTypeTbl = session.get(BusinessPurposeTbl.class,
								entityTbl.getBusinessPurposeId().getBusinessPurposeId());
						webEntityTemp.setBusinessPurposeId(businessPurposeTypeTbl.getBusinessPurposeId());
					}
//					if (null != entityTempTbl.getPaymentsId()) {
//						PaymentsTbl paymentsTbl = session.get(PaymentsTbl.class,
//								entityTempTbl.getPaymentsId().getPaymentsId());
//						webEntityTemp.setPaymentsId(paymentsTbl.getPaymentsId());
//						
//					}
					// Fetch Association Details
					Criteria associationCriteria = session.createCriteria(EntityAssociationTbl.class);
					associationCriteria.add(Restrictions.eq("entity.entityId", entityTbl.getEntityId()));
					List<EntityAssociationTbl> associationList = associationCriteria.list();
					UserSBTbl user1 = new UserSBTbl();

					if (associationList != null && !associationList.isEmpty()) {
						for (EntityAssociationTbl association : associationList) {

							WebAssociationTempTbl webAssociationTbl = new WebAssociationTempTbl();
							webAssociationTbl.setWebentityAssociationTempId(association.getEntityAssociationId());
							webAssociationTbl.setAssociationName(association.getAssociationName());
							webAssociationTbl.setMembershipStartDate(association.getMembershipStartDate());
							webAssociationTbl.setMembershipEndDate(association.getMembershipEndDate());
							webAssociationTbl.setMembershipLevel(association.getMembershipLevel());
							webAssociationTbl.setActiveFlag(association.getActiveFlag());
							webAssociationTbl.setLastModifiedDatetime(association.getLastModifiedDatetime());
							webAssociationTbl.setLastModifiedBy(user1);
							webEntityTemp.setWebAssociationTempTbl(webAssociationTbl);

						}
					}

					// Fetch Partnership Details
					Criteria partnershipCriteria = session.createCriteria(PartnershipTbl.class);
					partnershipCriteria.add(Restrictions.eq("entity.entityId", entityTbl.getEntityId()));
					List<PartnershipTbl> partnershipList = partnershipCriteria.list();
					WebUser user = new WebUser();
					// user.setUserId(userId);
					if (partnershipList != null && !partnershipList.isEmpty()) {
						for (PartnershipTbl partnership : partnershipList) {
							WebPartershipTbl webPartnership = new WebPartershipTbl();
							webPartnership.setEntityPartnershipId(partnership.getEntityPartnershipId());
							webPartnership.setPartnerCompanyName(partnership.getPartnerCompanyName());
							webPartnership.setPartnershipEndDate(partnership.getPartnershipEndDate());
							webPartnership.setPartnershipDescription(partnership.getPartnershipDescription());
							webPartnership.setActiveFlag(partnership.getActiveFlag());
							webPartnership.setLastModifiedDatetime(partnership.getLastModifiedDatetime());
							webPartnership.setPartnershipEndDate(partnership.getPartnershipEndDate());
							PartnershipTypeTbl partnershipType = partnership.getPartnershipType(); // This gets the
																									// PartnershipTypeTbl
																									// object
							if (partnershipType != null) {
								WebPartnershipTypeTbl webPartnershipType = new WebPartnershipTypeTbl();
								webPartnershipType.setPartnershipTypeId(partnershipType.getPartnershipTypeId());
								webPartnershipType.setPartnershipType(partnershipType.getPartnershipType());
								webPartnershipType.setPartnershipTypeDesc(partnershipType.getPartnershipTypeDesc());
								webPartnershipType.setActiveFlag(partnershipType.getActiveFlag());

								webPartnershipType.setLastModifiedDatetime(partnershipType.getLastModifiedDatetime());

								// Set the PartnershipType in WebPartnershipTbl
								webPartnership.setPartnershipType(webPartnershipType);
							}

//							UserDetailsTbl lastModifiedByUser = partnership.getLastModifiedByUser();
//					        if (lastModifiedByUser != null) {
//					            WebUser webUser = new WebUser();
//					            webUser.setUserId(lastModifiedByUser.getUserDetailsId());
//					             // Assuming UserSBTbl has a lastName field
//					            webUser.setEmail(lastModifiedByUser.getEmailAddress());
//					            // Set lastModifiedBy in the webPartnership object
//					            webPartnership.setLastModifiedBy(webUser);
//					        }
							// Set the partnership to the webEntityTemp
							webEntityTemp.setWebPartnership(webPartnership);
						}
					}

					// Fetch Cerfication Details

					Criteria cerficationCriteria = session.createCriteria(CertificationTbl.class);
					cerficationCriteria.add(Restrictions.eq("entity.entityId", entityTbl.getEntityId()));
					List<CertificationTbl> certificationList = cerficationCriteria.list();

					if (certificationList != null && !certificationList.isEmpty()) {
						for (CertificationTbl certification : certificationList) {
							WebCertificationTbl webCertification = new WebCertificationTbl();
							webCertification.setEntityCertificationId(certification.getEntityCertificationId());
							webCertification.setCertificationName(certification.getCertificationName());
							webCertification.setCertificationExpiryDate(certification.getCertificationDate());
							webCertification.setCertificationDate(certification.getCertificationDate());
							webCertification.setCertificationIssuingBody(certification.getCertificationIssuingBody());
							webCertification.setActiveFlag(certification.getActiveFlag());
							webCertification.setLastModifiedDatetime(certification.getLastModifiedDatetime());
							// webCertification.setLastModifiedBy(certification.getLastModifiedBy().getUserId());

							// Set the partnership to the webEntityTemp
							webEntityTemp.setWebCertificationTbl(webCertification);
						}
					}

					// Add the populated webEntity to the list
					webEntityList.add(webEntityTemp);
				}
			} else {
				// If no records found for the given SBR number
				return new ResponseEntity<>(new WebMessage("No records found for SBR Number: " + sbrNumber),
						HttpStatus.OK);
			}
		} catch (Exception e) {
			log.error("Exception occurred in getRegistrationDashboardList: {}", e.getMessage(), e);
			return new ResponseEntity<>(new WebMessage("Error occurred while fetching data"),
					HttpStatus.INTERNAL_SERVER_ERROR);
		} finally {
			closeSession(session);
		}

		return new ResponseEntity<>(webEntityList, HttpStatus.OK);
	}

	@Override
	public ResponseEntity<?> getTransactionsDetails() throws BviReturnsException {
		log.info("UserDaoImpl" + "getTransactionsDetails" + "Start");
		Session session = null;
		List<WebPaypalTransaction> webPaypalTransactionsList = new ArrayList<>();

		try {
			session = getSession();
			Criteria criteria = session.createCriteria(PaypalTransaction.class);
			criteria.addOrder(Order.desc("lastModifiedDatetime"));

			List<PaypalTransaction> paypalDetailsList = criteria.list();

			// Define date format for yyyy-MM-dd
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

			// Populate the list of WebPaypalTransaction
			for (PaypalTransaction paypalDetails : paypalDetailsList) {
				WebPaypalTransaction webPaypalTransaction = new WebPaypalTransaction();
				webPaypalTransaction.setPaypalTransactionsId(paypalDetails.getPaypalTransactionsId());

				// Format the date
				String formattedDate = dateFormat.format(paypalDetails.getPaypalTransactionDatetime());
				webPaypalTransaction.setPaypalTransactionDatetime(formattedDate);

				webPaypalTransaction.setPaypalTransactionAmt(paypalDetails.getPaypalTransactionAmt());
				webPaypalTransaction.setPaypalMessage(paypalDetails.getPaypalMessage());
				webPaypalTransaction.setPaypalConfirmationNumber(paypalDetails.getPaypalConfirmationNumber());

				String formattedLastModifiedDate = dateFormat.format(paypalDetails.getLastModifiedDatetime());
				webPaypalTransaction.setLastModifiedDatetime(formattedLastModifiedDate);

				webPaypalTransaction.setLastModifiedBy(paypalDetails.getLastModifiedBy());
				webPaypalTransaction.setCompanyName(paypalDetails.getCompanyName());

				webPaypalTransactionsList.add(webPaypalTransaction);
			}

		} catch (Exception e) {
			String errMessage = "Exception occurred while getting list of TransactionsDetails " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		} finally {
			closeSession(session);
		}

		return new ResponseEntity<>(webPaypalTransactionsList, HttpStatus.OK);
	}

//	@Override
//	public ResponseEntity<?> sendEmailDetails(String comments, String entityName,String subject) throws BviReturnsException {
//	    String logTag = "UserService : sendEmailDetails()";
//	    log.info("Entered into : " + logTag);
//	    Session session = null;
//	    
//	    try {
//	        session = getSession();
//	        
//	        // Retrieve UserDetails from EntityTempTbl using email address
//	        Criteria entityDetailsCriteria = session.createCriteria(EntityTempTbl.class);
//	        entityDetailsCriteria.add(Restrictions.eq("entityName", entityName));
//	        // Uncomment and fix this to get the actual entity from DB
//	         EntityTempTbl entityDetailsTbl = (EntityTempTbl) entityDetailsCriteria.uniqueResult();
//	        
//	      //  EntityTempTbl entityDetailsTbl = new EntityTempTbl(); // Replace with actual data fetching logic
//
//	        if (entityDetailsTbl == null || entityDetailsTbl.getEmailAddress() == null) {
//	            String errorMessage = "No entity found or email address is missing for entity: " + entityName;
//	            log.error(errorMessage);
//	            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorMessage);
//	        }
//
//	        // Prepare email details
//	        String[] rejectionRecipients = { entityDetailsTbl.getEmailAddress() };
//	        EmailDetails rejectionEmailDetails = new EmailDetails();
//	        rejectionEmailDetails.setToRecipients(rejectionRecipients);
//	        
//	        rejectionEmailDetails.setSubject("Small Business Registry (IAICC)");
//
//	       
//	     // Set the message content, incorporating the user-provided comments
//	        String messageContent = "Dear " + entityDetailsTbl.getEntityName() + ",\n\n"
//                    + "Subject: " + subject + "\n\n"
//                    + comments + "\n\n"
//                    + "Thanks & Regards,\n"
//                    + entityDetailsTbl.getUserId().getFirstName() + " " 
//                    + entityDetailsTbl.getUserId().getLastName();
//
//             rejectionEmailDetails.setMessageContent(messageContent);
//
//
//
//	        // Send the email asynchronously
//	        Runnable rejectionRunnable = new Runnable() {
//	            public void run() {
//	                EmailManager emailManager = EmailManager.getInstance();
//	                try {
//	                    emailManager.send(rejectionEmailDetails);
//	                } catch (BviReturnsException e) {
//	                    log.error("Error while sending email to " + entityDetailsTbl.getEmailAddress(), e);
//	                }
//	            }
//	        };
//	        new Thread(rejectionRunnable).start();
//
//	        log.info("Email sent successfully to " + entityDetailsTbl.getEmailAddress());
//	        return new ResponseEntity<>(new WebMessage("Email sent successfully"), HttpStatus.OK);
//	    } catch (Exception e) {
//	        String errMessage = logTag + " Exception occurred while sending email details: " + e.getMessage();
//	        log.error(errMessage, e);
//	        throw new BviReturnsException(errMessage, e);
//	    }
//	}
	
	@Override
	public ResponseEntity<?> sendEmailDetails(String comments, String entityName, String subject) throws BviReturnsException {
	    String logTag = "UserService : sendEmailDetails()";
	    log.info("Entered into : " + logTag);
	    Session session = null;
	    
	    try {
	        session = getSession();

	        EntityTempTbl entityDetailsTbl = null;
	        EntityTbl entityTbl = null;
	        String recipientEmail = null;
	        String recipientName = null;
	        UserSBTbl user = null;

	        // Check in EntityTempTbl first
	        Criteria entityDetailsCriteria = session.createCriteria(EntityTempTbl.class);
	        entityDetailsCriteria.add(Restrictions.eq("entityName", entityName));
	        entityDetailsTbl = (EntityTempTbl) entityDetailsCriteria.uniqueResult();

	        if (entityDetailsTbl != null && entityDetailsTbl.getEmailAddress() != null) {
	            recipientEmail = entityDetailsTbl.getEmailAddress();
	            recipientName = entityDetailsTbl.getEntityName();
	            user = entityDetailsTbl.getUserId();
	        } else {
	            // If not found in EntityTempTbl, check in EntityTbl
	            Criteria entityTblCriteria = session.createCriteria(EntityTbl.class);
	            entityTblCriteria.add(Restrictions.eq("entityName", entityName));
	            entityTbl = (EntityTbl) entityTblCriteria.uniqueResult();

	            if (entityTbl != null && entityTbl.getEmailAddress() != null) {
	                recipientEmail = entityTbl.getEmailAddress();
	                recipientName = entityTbl.getEntityName();
	                user = entityTbl.getUserId();
	            }
	        }

	        // If no entity found in either table, return error
	        if (recipientEmail == null) {
	            String errorMessage = "No entity found or email address is missing for entity: " + entityName;
	            log.error(errorMessage);
	            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorMessage);
	        }

	        // Prepare email details
	        String[] recipients = { recipientEmail };
	        EmailDetails emailDetails = new EmailDetails();
	        emailDetails.setToRecipients(recipients);
	        emailDetails.setSubject("Small Business Registry (IAICC)");

	        // Construct the message content
	        String messageContent = "Dear " + recipientName + ",\n\n"
	                + "Subject: " + subject + "\n\n"
	                + comments + "\n\n"
	                + "Thanks & Regards,\n";

	        if (user != null) {
	            messageContent += user.getFirstName() + " " + user.getLastName();
	        }

	        emailDetails.setMessageContent(messageContent);

	        // Send the email asynchronously
	        Runnable emailTask = () -> {
	            EmailManager emailManager = EmailManager.getInstance();
	            try {
	                emailManager.send(emailDetails);
	            } catch (BviReturnsException e) {
	    	        String errMessage = logTag + " Exception occurred while sending email details: " + e.getMessage();
	    	        log.error(errMessage, e);

	            }
	        };
	        new Thread(emailTask).start();

	        log.info("Email sent successfully to " + recipientEmail);
	        return new ResponseEntity<>(new WebMessage("Email sent successfully"), HttpStatus.OK);
	    } catch (Exception e) {
	        String errMessage = logTag + " Exception occurred while sending email details: " + e.getMessage();
	        log.error(errMessage, e);
	        throw new BviReturnsException(errMessage, e);
	    }
	}

	@Override
	public ResponseEntity<?> enableDisableUser(String activeFlag, Integer userId, String entityName)
			throws BviReturnsException {
		logTag = "enableUser()";
		log.info("Entering into : " + logTag);

		Session session = null;
		UserSBTbl userTbl = null;
		UserDetailsTbl userDetailsTbl = null;
		UserSBLoginTbl userLoginTbl = null;
		WebEnableDisableUserDetails webEnableDisableUserDetails = new WebEnableDisableUserDetails();

		try {
			session = getSession();

			// Fetch user details
			userTbl = session.get(UserSBTbl.class, userId);
			if (userTbl == null) {
				return new ResponseEntity<>(new WebMessage("UserTbl details not found"), HttpStatus.BAD_REQUEST);
			}

			// Fetch user details ID
			Criteria criteria = session.createCriteria(UserDetailsTbl.class);
			criteria.add(Restrictions.eq("user.userId", userId));
			criteria.setProjection(Projections.property("userDetailsId"));
			Integer userDetailsId = (int) criteria.uniqueResult();

			if (userDetailsId == null) {
				return new ResponseEntity<>(new WebMessage("UserDetailsId not found"), HttpStatus.BAD_REQUEST);
			}

			// Fetch the actual UserDetailsTbl
			userDetailsTbl = (UserDetailsTbl) session.get(UserDetailsTbl.class, userDetailsId);
			if (userDetailsTbl == null) {
				return new ResponseEntity<>(new WebMessage("UserDetails not found"), HttpStatus.BAD_REQUEST);
			}

			// Fetch the UserSBLoginTbl to update its activeFlag
			criteria = session.createCriteria(UserSBLoginTbl.class);
			criteria.add(Restrictions.eq("userId.userId", userId));
			userLoginTbl = (UserSBLoginTbl) criteria.uniqueResult();

			if (userLoginTbl == null) {
				return new ResponseEntity<>(new WebMessage("UserLoginTbl not found"), HttpStatus.BAD_REQUEST);
			}

			// Fetch the authentication user (for setting last modified by)
			criteria = session.createCriteria(UserSBTbl.class);
			criteria.add(Restrictions.eq("userId", userId));
			UserSBTbl authUserTbl = (UserSBTbl) criteria.uniqueResult();

			criteria = session.createCriteria(UserSBRoleTbl.class);
			criteria.add(Restrictions.eq("user.userId", userId));
			UserSBRoleTbl userSBRoleTbl = (UserSBRoleTbl) criteria.uniqueResult();

			// Fetch the authentication user (for setting last modified by)
			criteria = session.createCriteria(EntityTbl.class);
			criteria.add(Restrictions.eq("entityName", entityName));
			EntityTbl entityTblDetails = (EntityTbl) criteria.uniqueResult();

			// Update the active flag for userTbl, userDetailsTbl, and userLoginTbl
			if (Constants.INACTIVE == activeFlag.charAt(0)) {
				userTbl.setActiveFlag(Constants.INACTIVE);
				userDetailsTbl.setActiveFlag(Constants.INACTIVE);
				userLoginTbl.setActiveFlag(Constants.INACTIVE);
				userSBRoleTbl.setActiveFlag(Constants.INACTIVE);
				entityTblDetails.setActiveFlag(Constants.INACTIVE);

			} else if (Constants.ACTIVE == activeFlag.charAt(0)) {
				userTbl.setActiveFlag(Constants.ACTIVE);
				userDetailsTbl.setActiveFlag(Constants.ACTIVE);
				userLoginTbl.setActiveFlag(Constants.ACTIVE);
				userSBRoleTbl.setActiveFlag(Constants.ACTIVE);
				entityTblDetails.setActiveFlag(Constants.ACTIVE);

			}

			Date now = new Date();
			userTbl.setLastModifiedDatetime(now);
			userDetailsTbl.setLastModifiedDatetime(now);
			userDetailsTbl.setLastModifiedBy(authUserTbl);
			userLoginTbl.setLastModifiedDatetime(now);
			userLoginTbl.setLastModifiedBy(authUserTbl);

			// Begin transaction and save changes
			session.getTransaction().begin();
			session.saveOrUpdate(userTbl);
			session.saveOrUpdate(userDetailsTbl);
			session.saveOrUpdate(userLoginTbl);
			session.getTransaction().commit();

			// Prepare WebEnableDisableUserDetails for response
			webEnableDisableUserDetails.setEmailAddress(userDetailsTbl.getEmailAddress());
			webEnableDisableUserDetails.setFirstName(userTbl.getFirstName());
			webEnableDisableUserDetails.setLastName(userTbl.getLastName());
			webEnableDisableUserDetails.setActiveFlag(activeFlag);
			webEnableDisableUserDetails.setRaName(userDetailsTbl.getUser().getFirstName());

		} catch (Exception e) {
			log.error(logTag + " Exception occurred while enabling/disabling users: ", e);
			throw new BviReturnsException(
					logTag + " Exception occurred while enabling/disabling users: " + e.getMessage(), e);
		} finally {
			closeSession(session);
		}

		// Respond with appropriate message based on the flag value
		if (Constants.INACTIVE == activeFlag.charAt(0)) {
			return new ResponseEntity<>(new WebMessage("User disabled successfully"), HttpStatus.OK);
		} else {
			return new ResponseEntity<>(new WebMessage("User enabled successfully"), HttpStatus.OK);
		}
	}

	@Override
	public ResponseEntity<?> activeInactiveUsers() throws BviReturnsException {

		Session session = null;
		List<WebEntityTemp> webEntityTempList = new ArrayList<>();

		try {
			session = getSession();

			Criteria entityTempCriteria = session.createCriteria(EntityTbl.class);
//			entityTempCriteria.add(Restrictions.or(Restrictions.eq("authInd", Constants.APPROVE),
//					Restrictions.eq("authInd", Constants.REJECT)));
			entityTempCriteria.addOrder(Order.desc("lastModifiedDatetime"));
			List<EntityTbl> entityTblList = entityTempCriteria.list();

			if (entityTblList != null) {
				for (EntityTbl entityTempTbl : entityTblList) {

					Integer userId = entityTempTbl.getUserId().getUserId();

					WebEntityTemp webEntityTemp = new WebEntityTemp();

					Criteria userSBLoginCriteria = session.createCriteria(UserSBLoginTbl.class);
					userSBLoginCriteria.add(Restrictions.eq("userId.userId", userId));

					UserSBLoginTbl userLoginTbl = (UserSBLoginTbl) userSBLoginCriteria.uniqueResult();

					webEntityTemp.setEntityTempId(entityTempTbl.getEntityId());
					webEntityTemp.setUsername(userLoginTbl.getUsername());
					webEntityTemp.setFirstname(entityTempTbl.getUserId().getFirstName());
					webEntityTemp.setLastname(entityTempTbl.getUserId().getLastName());

					// webEntityTemp.setEntityNumber(entityTempTbl.getEntityNumber());
					webEntityTemp.setSbrNumber(entityTempTbl.getSbrNumber());
					webEntityTemp.setEntityName(entityTempTbl.getEntityName());
					if (entityTempTbl.getUrl() != null)
						webEntityTemp.setUrl(entityTempTbl.getUrl());
					if (entityTempTbl.getIncorporationDate() != null) {
						webEntityTemp.setIncorporationDate(entityTempTbl.getIncorporationDate());
					}
					if (entityTempTbl.getPhone() != null) {
						webEntityTemp.setPhone(entityTempTbl.getPhone());
					}

					if (entityTempTbl.getNumEmployees() != null) {
						webEntityTemp.setNumEmployees(entityTempTbl.getNumEmployees());
					}
					if (entityTempTbl.getCompanyProfile() != null) {
						webEntityTemp.setCompanyProfile(entityTempTbl.getCompanyProfile());
					}
					if (entityTempTbl.getAddress() != null) {
						webEntityTemp.setAddress(entityTempTbl.getAddress());
					}
					webEntityTemp.setActiveFlag(entityTempTbl.getActiveFlag());

					webEntityTemp.setLastModifiedDatetime(new Date());
					webEntityTemp.setLastModifiedBy(userId);

					webEntityTemp.setAuthBy(userId);
					webEntityTemp.setUserId(userId);

					if (null != entityTempTbl.getCompanyTypeId()) {
						CompanyTypeTbl companyTypeTbl = session.get(CompanyTypeTbl.class,
								entityTempTbl.getCompanyTypeId().getCompanyTypeId());
						webEntityTemp.setCompanyTypeId(companyTypeTbl.getCompanyTypeId());
					}

					if (null != entityTempTbl.getBusinessOperationId()) {
						BusinessOperationTypeTbl businessOperationTypeTbl = session.get(BusinessOperationTypeTbl.class,
								entityTempTbl.getBusinessOperationId().getBusinessOperationId());
						webEntityTemp.setBusinessOperationId(businessOperationTypeTbl.getBusinessOperationId());
					}

					webEntityTempList.add(webEntityTemp);

				}
			} else {
				return new ResponseEntity<>(new WebMessage("No records found"), HttpStatus.OK);
			}
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in : getDashboardList" + e;
			log.error(errMessage, e);
		} finally {
			closeSession(session);
		}
		return new ResponseEntity<>(webEntityTempList, HttpStatus.OK);
	}

	@Override
	public ResponseEntity<?> getTransactionsDetailsByUser(Integer lastModifiedBy) throws BviReturnsException {

		log.info("UserDaoImpl" + "getTransactionsDetails" + " Start");
		Session session = null;
		List<WebPaypalTransaction> webPaypalTransactionsList = new ArrayList<>();

		try {
			session = getSession();
			Criteria criteria = session.createCriteria(PaypalTransaction.class);
			// criteria.add(Restrictions.eq("companyName", companyName));

			criteria.add(Restrictions.eq("lastModifiedBy.userId", lastModifiedBy)); // filter by lastModifiedById

			// Get the list of PaypalTransaction objects from the criteria
			List<PaypalTransaction> paypalDetailsList = criteria.list();

			// Define date format for yyyy-MM-dd
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

			// Loop through each PaypalTransaction and populate WebPaypalTransaction
			for (PaypalTransaction paypalDetails : paypalDetailsList) {
				WebPaypalTransaction webPaypalTransaction = new WebPaypalTransaction();
				webPaypalTransaction.setPaypalTransactionsId(paypalDetails.getPaypalTransactionsId());

				// Format the date for transaction date and last modified date
				String formattedTransactionDate = dateFormat.format(paypalDetails.getPaypalTransactionDatetime());
				webPaypalTransaction.setPaypalTransactionDatetime(formattedTransactionDate);

				webPaypalTransaction.setPaypalTransactionAmt(paypalDetails.getPaypalTransactionAmt());
				webPaypalTransaction.setPaypalMessage(paypalDetails.getPaypalMessage());
				webPaypalTransaction.setPaypalConfirmationNumber(paypalDetails.getPaypalConfirmationNumber());

				String formattedLastModifiedDate = dateFormat.format(paypalDetails.getLastModifiedDatetime());
				webPaypalTransaction.setLastModifiedDatetime(formattedLastModifiedDate);

				webPaypalTransaction.setLastModifiedBy(paypalDetails.getLastModifiedBy());
				webPaypalTransaction.setCompanyName(paypalDetails.getCompanyName());

				// Add to the list
				webPaypalTransactionsList.add(webPaypalTransaction);
			}

		} catch (Exception e) {
			String errMessage = "Exception occurred while getting list of TransactionsDetails " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		} finally {
			closeSession(session);
		}

		return new ResponseEntity<>(webPaypalTransactionsList, HttpStatus.OK);
	}

	@Override
	public ResponseEntity<?> getDetailsByUserName(String userName) throws BviReturnsException {
		// TODO Auto-generated method stub
		return null;
	}

//	@Override
//	public ResponseEntity<?> registerNewUser(SBDetails sbDetails) throws BviReturnsException {
//		Session session = null;
//		UserSBTbl userTbl = new UserSBTbl();
//		String logTag = "registerNewUser";
//		String newPassword = RandomStringUtils.random(7, characters);
//		try {
//
//			session = getSession();
//			session.getTransaction().begin();
//			// Null checks for required properties
//			if (sbDetails.getUserTbl() == null) {
//				throw new BviReturnsException("User details cannot be null");
//			}
//			// Set userTbl properties
//			userTbl.setFirstName(sbDetails.getUserTbl().getFirstName().trim());
//			userTbl.setLastName(sbDetails.getUserTbl().getLastName().trim());
//			userTbl.setActiveFlag(Constants.ACTIVE);
//			userTbl.setLastModifiedDatetime(new Date());
//			session.saveOrUpdate(userTbl);
//
//			// Set role
//			RoleTbl role = new RoleTbl();
//			role.setRoleId(2);
//
//			// Create new UserDetails object
//			UserDetailsTbl userDetailsTbl = new UserDetailsTbl();
//			userDetailsTbl.setEmailAddress(sbDetails.getUserDetailsTbl().getEmailAddress());
//			userDetailsTbl.setPhone(sbDetails.getUserDetailsTbl().getPhone());
//			userDetailsTbl.setRegion(sbDetails.getUserDetailsTbl().getRegion());
//			userDetailsTbl.setUser(userTbl); // Set the entire UserSBTbl object
//			userDetailsTbl.setLastModifiedBy(userTbl);
//			userDetailsTbl.setLastModifiedDatetime(new Date());
//			userDetailsTbl.setActiveFlag(Constants.ACTIVE);
//			session.saveOrUpdate(userDetailsTbl);
//
//			// Create new UserRole object
//			UserSBRoleTbl userRoleTbl = new UserSBRoleTbl();
//			userRoleTbl.setUser(userTbl); // Set the UserSBTbl object
//			userRoleTbl.setRole(role); // Set the RoleTbl object
//			userRoleTbl.setLastModifiedDatetime(new Date());
//			userRoleTbl.setLastModifiedBy(userTbl); // Set the UserSBTbl object for last modified by
//			userRoleTbl.setActiveFlag(Constants.ACTIVE);
//			session.saveOrUpdate(userRoleTbl);
//
//			// Create new UserLogin object
//			UserSBLoginTbl userLoginTbl = new UserSBLoginTbl();
//			userLoginTbl.setUsername(sbDetails.getUserLoginTbl().getUsername());
//			// userLoginTbl.setPassword(passwordEncoder.encode(sbDetails.getUserLoginTbl().getPassword()));
//			userLoginTbl.setPassword(passwordEncryptor.encrypt(newPassword));
//			userLoginTbl.setUserId(userTbl);
//			userLoginTbl.setLastModifiedDatetime(new Date());
//			userLoginTbl.setLastModifiedBy(userTbl);
//			userLoginTbl.setActiveFlag(Constants.ACTIVE);
//			session.saveOrUpdate(userLoginTbl);
//
//			EntityTbl entityTbl = new EntityTbl();
//			entityTbl.setEntityName(sbDetails.getEntityTbl().getEntityName());
//			entityTbl.setSbrNumber(EntityNumberGenerator.generateSBRNumber());
//			entityTbl.setLastModifiedDatetime(new Date());
//			entityTbl.setActiveFlag(Constants.ACTIVE);
//			entityTbl.setUserId(userTbl);
//			entityTbl.setLastModifiedBy(userTbl);
//			entityTbl.setAuthInd(Constants.APPROVE);
//			entityTbl.setLogInd(Constants.LOGIN);
//			session.saveOrUpdate(entityTbl);
//
//			EntityAssociationTbl association = new EntityAssociationTbl();
//			association.setEntity(entityTbl);
//			association.setAssociationName(sbDetails.getEntityAssociationTbl().getAssociationName());
//			association.setMembershipStartDate(sbDetails.getEntityAssociationTbl().getMembershipStartDate());
//			association.setMembershipEndDate(sbDetails.getEntityAssociationTbl().getMembershipEndDate());
//			association.setMembershipLevel(sbDetails.getEntityAssociationTbl().getMembershipLevel());
//			association.setActiveFlag(Constants.ACTIVE);
//			association.setLastModifiedDatetime(new Date());
//			association.setLastModifiedBy(userTbl);
//
//			session.saveOrUpdate(association);
//
//			session.getTransaction().commit();
//
//			String firstName = userTbl.getFirstName();
//			firstName = firstName.substring(0, 1).toUpperCase() + firstName.substring(1).toLowerCase();
//			String lastName = userTbl.getLastName();
//			lastName = lastName.substring(0, 1).toUpperCase() + lastName.substring(1).toLowerCase();
//			session = getSession();
//			EmailDetails emailDetails = new EmailDetails();
//			Boolean emailStatus = true;
//			String[] recipients = { userDetailsTbl.getEmailAddress() };
//			// String decPassword =
//			// passwordEncryptor.decrypt(sbDetails.getUserLoginTbl().getPassword());
//			// String decPassword = sbDetails.getUserLoginTbl().getPassword();
//			// Update subject to indicate pending approval
//			emailDetails.setToRecipients(recipients);
//			emailDetails.setSubject("Small Business Registry (IAICC) - Your Registration is Pending Approval");
//			// Update the message content to inform the user that the account is pending
//			// approval
//			emailDetails.setMessageContent("Dear " + firstName + " " + lastName + ",\n\n"
//					+ "Thank you for registering with the Small Business Registry (IAICC). Your registration is currently **Pending for Approval**. Once approved, you will receive an email with your login credentials.\n\n"
//					+ "Here are your registration details:\n" + "Username: " + sbDetails.getUserLoginTbl().getUsername()
//					+ "\n" + "Password: " + newPassword + "\n\n"
//					+ "In the meantime, feel free to explore the resources available on our platform and reach out to our support team if you have any questions.\n\n"
//					+ "We appreciate your patience and look forward to your active participation in the Small Business Registry.\n\n"
//					+ "Best regards,\n" + "The Small Business Registry (IAICC) Team\n" + "https://iaicc.org/ \n"
//					+ "<EMAIL>");
//
//			Runnable myrunnable = new Runnable() {
//				public void run() {
//					EmailManager emailManager = EmailManager.getInstance();
//					try {
//						emailManager.send(emailDetails);
//					} catch (BviReturnsException e) {
//						e.printStackTrace();
//					}
//				}
//			};
//			new Thread(myrunnable).start();
//			if (emailStatus) {
//				return new ResponseEntity<>(
//						new WebMessage(Constants.User.REGISTRATION_DONE, Constants.User.USER_DETAILS_SENT),
//						HttpStatus.OK);
//			} else {
//				return new ResponseEntity<>(new WebMessage(Constants.User.USER_DETAILS_NOT_SENT), HttpStatus.OK);
//			}
//
//		} catch (Exception e) {
//			if (session.getTransaction() != null) {
//				session.getTransaction().rollback();
//			}
//			String errMessage = String.format("%s - Exception occurred during user registration for userId: %s", logTag,
//					(userTbl != null ? userTbl.getUserId() : "Unknown"));
//			log.error(errMessage, e);
//			throw new BviReturnsException(errMessage, e);
//		} finally {
//			closeSession(session);
//			log.info("{} - User registration process completed", logTag);
//		}
//	}
	@Override
	public ResponseEntity<?> registerNewUser(SBDetails sbDetails) throws BviReturnsException {
	    Session session = null;
	    UserSBTbl userTbl = new UserSBTbl();
	    String logTag = "registerNewUser";
	    String newPassword = RandomStringUtils.random(7, characters);
	    try {
	        session = getSession();
	        session.getTransaction().begin();

	        // Null checks for required properties
	        if (sbDetails.getUserTbl() == null) {
	            throw new BviReturnsException("User details cannot be null");
	        }

	        // Set userTbl properties
	        userTbl.setFirstName(sbDetails.getUserTbl().getFirstName().trim());
	        userTbl.setLastName(sbDetails.getUserTbl().getLastName().trim());
	        userTbl.setActiveFlag(Constants.ACTIVE);
	        userTbl.setLastModifiedDatetime(new Date());
	        session.saveOrUpdate(userTbl);

	        // Set role
	        RoleTbl role = new RoleTbl();
	        role.setRoleId(2);

	        // Create new UserDetails object
	        UserDetailsTbl userDetailsTbl = new UserDetailsTbl();
	        userDetailsTbl.setEmailAddress(sbDetails.getUserDetailsTbl().getEmailAddress());
	        userDetailsTbl.setPhone(sbDetails.getUserDetailsTbl().getPhone());
	        userDetailsTbl.setRegion(sbDetails.getUserDetailsTbl().getRegion());
	        userDetailsTbl.setUser(userTbl); // Set the entire UserSBTbl object
	        userDetailsTbl.setLastModifiedBy(userTbl);
	        userDetailsTbl.setLastModifiedDatetime(new Date());
	        userDetailsTbl.setActiveFlag(Constants.ACTIVE);
	        session.saveOrUpdate(userDetailsTbl);

	        // Create new UserRole object
	        UserSBRoleTbl userRoleTbl = new UserSBRoleTbl();
	        userRoleTbl.setUser(userTbl); // Set the UserSBTbl object
	        userRoleTbl.setRole(role); // Set the RoleTbl object
	        userRoleTbl.setLastModifiedDatetime(new Date());
	        userRoleTbl.setLastModifiedBy(userTbl); // Set the UserSBTbl object for last modified by
	        userRoleTbl.setActiveFlag(Constants.ACTIVE);
	        session.saveOrUpdate(userRoleTbl);

	        // Create new UserLogin object
	        UserSBLoginTbl userLoginTbl = new UserSBLoginTbl();
	        userLoginTbl.setUsername(sbDetails.getUserLoginTbl().getUsername());
	        userLoginTbl.setPassword(passwordEncryptor.encrypt(newPassword));
	        userLoginTbl.setUserId(userTbl);
	        userLoginTbl.setLastModifiedDatetime(new Date());
	        userLoginTbl.setLastModifiedBy(userTbl);
	        userLoginTbl.setActiveFlag(Constants.ACTIVE);
	        session.saveOrUpdate(userLoginTbl);

	        EntityTbl entityTbl = new EntityTbl();
	        entityTbl.setEntityName(sbDetails.getEntityTbl().getEntityName());
	        entityTbl.setSbrNumber(EntityNumberGenerator.generateSBRNumber());
	        entityTbl.setLastModifiedDatetime(new Date());
	        entityTbl.setActiveFlag(Constants.ACTIVE);
	        entityTbl.setUserId(userTbl);
	        entityTbl.setLastModifiedBy(userTbl);
	        entityTbl.setAuthInd(Constants.APPROVE);
	        entityTbl.setLogInd(Constants.LOGIN);
	        session.saveOrUpdate(entityTbl);

	        EntityAssociationTbl association = new EntityAssociationTbl();
	        association.setEntity(entityTbl);
	        association.setAssociationName(sbDetails.getEntityAssociationTbl().getAssociationName());
	        association.setMembershipStartDate(sbDetails.getEntityAssociationTbl().getMembershipStartDate());
	        association.setMembershipEndDate(sbDetails.getEntityAssociationTbl().getMembershipEndDate());
	        association.setMembershipLevel(sbDetails.getEntityAssociationTbl().getMembershipLevel());
	        association.setActiveFlag(Constants.ACTIVE);
	        association.setLastModifiedDatetime(new Date());
	        association.setLastModifiedBy(userTbl);
	        session.saveOrUpdate(association);

	        session.getTransaction().commit();

	        String firstName = userTbl.getFirstName();
	        firstName = firstName.substring(0, 1).toUpperCase() + firstName.substring(1).toLowerCase();
	        String lastName = userTbl.getLastName();
	        lastName = lastName.substring(0, 1).toUpperCase() + lastName.substring(1).toLowerCase();
	        session = getSession();
	        EmailDetails emailDetails = new EmailDetails();
	        Boolean emailStatus = true;
	        String[] recipients = { userDetailsTbl.getEmailAddress() };

	        // Updated email subject and content
	        emailDetails.setToRecipients(recipients);
	        emailDetails.setSubject("Action Required: Complete Your IAICC Profile for Full Access");

	        emailDetails.setMessageContent("Dear " + firstName + " " + lastName + ",\n\n"
	                + "Thank you for joining the Indian American International Chamber of Commerce (IAICC). Your membership registration is Approved.\n\n"
	                + "To access your membership benefits and stay informed about IAICC updates, please complete your profile details.\n\n"
	                + "Below are your login credentials:\n\n"
	                + "Username: " + sbDetails.getUserLoginTbl().getUsername() + "\n"
	                + "Password: " + newPassword + "\n\n"
	                + "Login URL: http://bregistry.iaicc.org:8080/stageiaicc-1.0-FE/#/\n\n"
	                + "Next Steps:\n\n"
	                + "1.Log in to your IAICC account to explore member benefits.\n"
	                + "2.Complete your profile to maximize networking opportunities.\n"
	                + "3.Stay updated on events, industry insights, and exclusive business opportunities.\n\n"
	                + "If you have any questions or need assistance, feel free to reach out to our support team.\n\n"
	                + "We look forward to supporting your success and fostering meaningful collaborations!\n\n"
	                + "Best regards,\n"
	                + "IAICC Team\n"
	                + "Tel: ************\n"
	                + "Email: <EMAIL>");

	        Runnable myrunnable = new Runnable() {
	            public void run() {
	                EmailManager emailManager = EmailManager.getInstance();
	                try {
	                    emailManager.send(emailDetails);
	                } catch (BviReturnsException e) {
	                    e.printStackTrace();
	                }
	            }
	        };
	        new Thread(myrunnable).start();

	        if (emailStatus) {
	            return new ResponseEntity<>(
	                    new WebMessage(Constants.User.REGISTRATION_DONE, Constants.User.USER_DETAILS_SENT),
	                    HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(new WebMessage(Constants.User.USER_DETAILS_NOT_SENT), HttpStatus.OK);
	        }

	    } catch (Exception e) {
	        if (session.getTransaction() != null) {
	            session.getTransaction().rollback();
	        }
	        String errMessage = String.format("%s - Exception occurred during user registration for userId: %s", logTag,
	                (userTbl != null ? userTbl.getUserId() : "Unknown"));
	        log.error(errMessage, e);
	        throw new BviReturnsException(errMessage, e);
	    } finally {
	        closeSession(session);
	        log.info("{} - User registration process completed", logTag);
	    }
	}
	@Override
	public ResponseEntity<?> updateUser(SBDetails sbDetails, Integer userId) throws BviReturnsException {
		Session session = null;

		try {
			session = getSession();
			session.getTransaction().begin();

			// Retrieve the EntityTbl record based on sbrNumber and authInd
			Criteria criteria = session.createCriteria(EntityTbl.class);
			criteria.add(Restrictions.eq("sbrNumber", sbDetails.getEntityTbl().getSbrNumber()));
			criteria.add(Restrictions.eq("authInd", Constants.APPROVE));

			EntityTbl entityTbl = (EntityTbl) criteria.uniqueResult();

			// Validate the retrieved entityTbl
			if (entityTbl == null) {
				return new ResponseEntity<>(new WebMessage("EntityTbl not found"), HttpStatus.NOT_FOUND);
			}

			// Update EntityTbl fields if sbDetails is not null
			if (sbDetails.getEntityTbl() != null) {
				EntityTbl details = sbDetails.getEntityTbl();

				entityTbl.setActiveFlag(Constants.ACTIVE);

				// Update only non-null fields
				if (details.getEntityName() != null)
					entityTbl.setEntityName(details.getEntityName());
				if (details.getEmailAddress() != null)
					entityTbl.setEmailAddress(details.getEmailAddress());
				if (details.getSbrNumber() != null)
					entityTbl.setSbrNumber(details.getSbrNumber());
				if (details.getUrl() != null)
					entityTbl.setUrl(details.getUrl());
				if (details.getPhone() != null)
					entityTbl.setPhone(details.getPhone());
				if (details.getIncorporationDate() != null)
					entityTbl.setIncorporationDate(details.getIncorporationDate());
				if (details.getNumEmployees() != null)
					entityTbl.setNumEmployees(details.getNumEmployees());
				if (details.getCompanyProfile() != null)
					entityTbl.setCompanyProfile(details.getCompanyProfile());
				if (details.getAddress() != null)
					entityTbl.setAddress(details.getAddress());
				if (details.getFederalEmployerIdentificationNumber() != null)
					entityTbl.setFederalEmployerIdentificationNumber(details.getFederalEmployerIdentificationNumber());
				if (details.getStateOfIncorporation() != null)
					entityTbl.setStateOfIncorporation(details.getStateOfIncorporation());
				if (details.getBusinessKeywords() != null)
					entityTbl.setBusinessKeywords(details.getBusinessKeywords());
				if (details.getAreasInterests() != null)
					entityTbl.setAreasInterests(details.getAreasInterests());
				if (details.getEstimatedAnnualRevenue() != null)
					entityTbl.setEstimatedAnnualRevenue(details.getEstimatedAnnualRevenue());
				if (details.getOwnerFirstName() != null)
					entityTbl.setOwnerFirstName(details.getOwnerFirstName());
				if (details.getOwnerLastName() != null)
					entityTbl.setOwnerLastName(details.getOwnerLastName());
				if (details.getOwnerPhone() != null)
					entityTbl.setOwnerPhone(details.getOwnerPhone());
				if (details.getOwnerEmail() != null)
					entityTbl.setOwnerEmail(details.getOwnerEmail());
				if (details.getSizeCompanyInd() != null)
					entityTbl.setSizeCompanyInd(details.getSizeCompanyInd());
				if (details.getUserId() != null)
					entityTbl.setLastModifiedBy(details.getUserId());
				entityTbl.setLastModifiedDatetime(new Date());

				// Set associated tables
				if (details.getBusinessPurposeId() != null) {
					BusinessPurposeTbl businessPurposeTbl = session.get(BusinessPurposeTbl.class,
							details.getBusinessPurposeId().getBusinessPurposeId());
					entityTbl.setBusinessPurposeId(businessPurposeTbl);
				}
				if (details.getCompanyTypeId() != null) {
					CompanyTypeTbl companyTypeTbl = session.get(CompanyTypeTbl.class,
							details.getCompanyTypeId().getCompanyTypeId());
					entityTbl.setCompanyTypeId(companyTypeTbl);
				}
				if (details.getBusinessOperationId() != null) {
					BusinessOperationTypeTbl businessOperationTypeTbl = session.get(BusinessOperationTypeTbl.class,
							details.getBusinessOperationId().getBusinessOperationId());
					entityTbl.setBusinessOperationId(businessOperationTypeTbl);
				}

				session.update(entityTbl);
			} else {
				return new ResponseEntity<>(new WebMessage("EntityTbl details are null"), HttpStatus.BAD_REQUEST);
			}

			// Update EntityAssociationTbl records if present
			if (sbDetails.getEntityAssociationTblList() != null) {
				for (EntityAssociationTbl association : sbDetails.getEntityAssociationTblList()) {
					if (association.getEntityAssociationId() != null) {
						EntityAssociationTbl existingAssociation = session.get(EntityAssociationTbl.class,
								association.getEntityAssociationId());
						if (existingAssociation != null) {
							existingAssociation.setEntity(entityTbl);
							existingAssociation.setAssociationName(association.getAssociationName());
							existingAssociation.setMembershipStartDate(association.getMembershipStartDate());
							existingAssociation.setMembershipEndDate(association.getMembershipEndDate());
							existingAssociation.setMembershipLevel(association.getMembershipLevel());
							existingAssociation.setActiveFlag(Constants.ACTIVE);
							existingAssociation.setLastModifiedDatetime(new Date());
							existingAssociation.setLastModifiedBy(entityTbl.getUserId());

							session.update(existingAssociation);
						}
					}
				}
			}

			// Update CertificationTbl
			if (sbDetails.getCertificationTblList() != null) {

				for (CertificationTbl certificationDetails : sbDetails.getCertificationTblList()) {
					certificationDetails.setEntity(entityTbl);
					certificationDetails.setCertificationName(certificationDetails.getCertificationName());
					certificationDetails.setCertificationDate(certificationDetails.getCertificationDate());
					certificationDetails.setCertificationExpiryDate(certificationDetails.getCertificationExpiryDate());
					certificationDetails
							.setCertificationIssuingBody(certificationDetails.getCertificationIssuingBody());
					certificationDetails.setActiveFlag(Constants.ACTIVE);
					certificationDetails.setLastModifiedDatetime(new Date());
					certificationDetails.setLastModifiedBy(entityTbl.getUserId());

					session.saveOrUpdate(certificationDetails);
				}
			}

			// Update PartnershipTbl
			if (sbDetails.getPartnershipTblList() != null) {
				for (PartnershipTbl partnershipDetails : sbDetails.getPartnershipTblList()) {
					partnershipDetails.setEntity(entityTbl);

					// Fetch the PartnershipTypeTbl from the session
					PartnershipTypeTbl partnershipType = session.get(PartnershipTypeTbl.class,
							partnershipDetails.getPartnershipType().getPartnershipTypeId());
					partnershipDetails.setPartnershipType(partnershipType);

					// Set partnership details from sbDetails.getPartnershipTbl()
					partnershipDetails.setPartnershipEndDate(partnershipDetails.getPartnershipEndDate());
					partnershipDetails.setPartnershipDescription(partnershipDetails.getPartnershipDescription());
					partnershipDetails.setActiveFlag(Constants.ACTIVE);
					partnershipDetails.setLastModifiedDatetime(new Date());
					partnershipDetails.setLastModifiedBy(entityTbl.getUserId());

					// Save or update the partnershipDetails
					session.saveOrUpdate(partnershipDetails);
				}
			}

			// Update UserSBTbl
			UserSBTbl userTbl = session.get(UserSBTbl.class, userId);
			if (userTbl != null && sbDetails.getUserTbl() != null) {
				userTbl.setFirstName(sbDetails.getUserTbl().getFirstName());
				userTbl.setLastName(sbDetails.getUserTbl().getLastName());
				userTbl.setLastModifiedDatetime(new Date());
				userTbl.setActiveFlag(Constants.ACTIVE);
				session.update(userTbl);
			} else {
				return new ResponseEntity<>(new WebMessage("UserTbl details not found"), HttpStatus.BAD_REQUEST);
			}

			// Update UserSBLoginTbl
			Criteria userSBLoginCriteria = session.createCriteria(UserSBLoginTbl.class);
			userSBLoginCriteria.add(Restrictions.eq("userId.userId", userId));

			UserSBLoginTbl userLoginTbl = (UserSBLoginTbl) userSBLoginCriteria.uniqueResult();
			if (userLoginTbl != null) {
				userLoginTbl.setLastModifiedDatetime(new Date());
				userLoginTbl.setActiveFlag(Constants.ACTIVE);
				userLoginTbl.setLastModifiedBy(userTbl);
				session.update(userLoginTbl);
			} else {
				return new ResponseEntity<>(new WebMessage("UserLoginTbl details not found"), HttpStatus.BAD_REQUEST);
			}

			// Commit transaction
			session.getTransaction().commit();
			return new ResponseEntity<>(new WebMessage("User updated successfully"), HttpStatus.OK);

		} catch (Exception e) {

			throw new BviReturnsException("Error while updating user", e);
		} finally {
			if (session != null) {
				session.close();
			}
		}
	}

	@Override
	public ResponseEntity<?> getRegistrationPendingList(String sbrNumber) throws BviReturnsException {
		Session session = null;
		List<WebEntityTemp> webEntityList = new ArrayList<>();

		try {
			session = getSession();

			// Create criteria to filter by sbrNumber in EntityTbl
			Criteria entityCriteria = session.createCriteria(EntityTempTbl.class);
			entityCriteria.add(Restrictions.eq("sbrNumber", sbrNumber));
			entityCriteria.addOrder(Order.desc("lastModifiedDatetime"));
			List<EntityTempTbl> entityTblList = entityCriteria.list();

			if (entityTblList != null && !entityTblList.isEmpty()) {
				for (EntityTempTbl entityTempTbl : entityTblList) {
					WebEntityTemp webEntityTemp = new WebEntityTemp();

					// Check for associated userId and retrieve user login details if available
					if (entityTempTbl.getUserId() != null) {
						Integer userId = entityTempTbl.getUserId().getUserId();

						Criteria userSBLoginCriteria = session.createCriteria(UserSBLoginTbl.class);
						userSBLoginCriteria.add(Restrictions.eq("userId.userId", userId)); // Assuming `userId` exists

						// Get user login details (handle multiple results if necessary)
						UserSBLoginTbl userLoginTbl = (UserSBLoginTbl) userSBLoginCriteria.uniqueResult();
						if (userLoginTbl != null) {
							webEntityTemp.setUsername(userLoginTbl.getUsername());
							webEntityTemp.setUserId(userId);

						}
						// GeT UserDetails
						Criteria userDetailsCriteria = session.createCriteria(UserDetailsTbl.class);
						userDetailsCriteria.add(Restrictions.eq("user.userId", userId));
						UserDetailsTbl userDetailsTbl = (UserDetailsTbl) userDetailsCriteria.uniqueResult();
						if (userDetailsTbl != null) {
							webEntityTemp.setUseremailAddress(userDetailsTbl.getEmailAddress());
							webEntityTemp.setUserphone(userDetailsTbl.getPhone());
						}

					}

					// Set entity details
					webEntityTemp.setEntityId(entityTempTbl.getEntityId());
					webEntityTemp.setEntityTempId(entityTempTbl.getEntityTempId());
					webEntityTemp.setFirstname(entityTempTbl.getUserId().getFirstName());
					webEntityTemp.setLastname(entityTempTbl.getUserId().getLastName());
					webEntityTemp.setSbrNumber(entityTempTbl.getSbrNumber());
					webEntityTemp.setEntityName(entityTempTbl.getEntityName());
					webEntityTemp.setEmailAddress(entityTempTbl.getEmailAddress());
					webEntityTemp.setUrl(entityTempTbl.getUrl());
					webEntityTemp.setIncorporationDate(entityTempTbl.getIncorporationDate());
					webEntityTemp.setPhone(entityTempTbl.getPhone());
					webEntityTemp.setNumEmployees(entityTempTbl.getNumEmployees());
					webEntityTemp.setCompanyProfile(entityTempTbl.getCompanyProfile());
					webEntityTemp.setAddress(entityTempTbl.getAddress());
					webEntityTemp.setSizeCompanyInd(entityTempTbl.getSizeCompanyInd());
					webEntityTemp.setOwnerFirstName(entityTempTbl.getOwnerFirstName());
					webEntityTemp.setOwnerLastName(entityTempTbl.getOwnerLastName());
					webEntityTemp.setOwnerPhone(entityTempTbl.getOwnerPhone());
					webEntityTemp.setOwnerEmail(entityTempTbl.getOwnerEmail());
					webEntityTemp.setAreasInterests(entityTempTbl.getAreasInterests());
					webEntityTemp.setStateOfIncorporation(entityTempTbl.getStateOfIncorporation());
					webEntityTemp.setBusinessKeywords(entityTempTbl.getBusinessKeywords());
					webEntityTemp.setEstimatedannualrevenue(entityTempTbl.getEstimatedAnnualRevenue());
					webEntityTemp.setFederalEmployerIdentificationNumber(
							entityTempTbl.getFederalEmployerIdentificationNumber());
					webEntityTemp.setLastModifiedDatetime(entityTempTbl.getLastModifiedDatetime());
					webEntityTemp.setSizeCompanyInd(entityTempTbl.getSizeCompanyInd());
					webEntityTemp.setEmailAddress(entityTempTbl.getEmailAddress());
					// webEntity.setTransactionName(entityTbl.getTransactionTypeId().getTransactionName());
					// Set company type
					if (entityTempTbl.getCompanyTypeId() != null) {
						CompanyTypeTbl companyTypeTbl = session.get(CompanyTypeTbl.class,
								entityTempTbl.getCompanyTypeId().getCompanyTypeId());
						webEntityTemp.setCompanyTypeId(companyTypeTbl.getCompanyTypeId());
					}

					// Retrieve and set document information if present
//					if (entityTempTbl.getDocumentTblByDocumentAddnId() != null) {
//						Criteria documentTblCriteria = session.createCriteria(DocumentTbl.class);
//						documentTblCriteria.add(Restrictions.eq("activeFlag", Constants.ACTIVE));
//						documentTblCriteria.add(Restrictions.eq("documentId",
//								entityTempTbl.getDocumentTblByDocumentAddnId().getDocumentId()));
//
//						DocumentTbl documentTbl = (DocumentTbl) documentTblCriteria.uniqueResult();
//
//						if (documentTbl != null) {
//							WebDocumentTbl webDocument = new WebDocumentTbl();
//							webDocument.setDocumentName(documentTbl.getDocumentName());
//							webDocument.setDocumentFileExtn(documentTbl.getDocumentFileExtn());
//							webDocument.setDocumentId(documentTbl.getDocumentTypeTbl().getDocumentTypeId());
//							webEntityTemp.setWebDocument(webDocument);
//						}
//					}

					if (null != entityTempTbl.getBusinessOperationId()) {
						BusinessOperationTypeTbl businessOperationTypeTbl = session.get(BusinessOperationTypeTbl.class,
								entityTempTbl.getBusinessOperationId().getBusinessOperationId());
						webEntityTemp.setBusinessOperationId(businessOperationTypeTbl.getBusinessOperationId());
					}

					if (null != entityTempTbl.getBusinessPurposeId()) {
						BusinessPurposeTbl businessPurposeTypeTbl = session.get(BusinessPurposeTbl.class,
								entityTempTbl.getBusinessPurposeId().getBusinessPurposeId());
						webEntityTemp.setBusinessPurposeId(businessPurposeTypeTbl.getBusinessPurposeId());
					}
//					if (null != entityTempTbl.getPaymentsId()) {
//						PaymentsTbl paymentsTbl = session.get(PaymentsTbl.class,
//								entityTempTbl.getPaymentsId().getPaymentsId());
//						webEntityTemp.setPaymentsId(paymentsTbl.getPaymentsId());
//						
//					}
					// Fetch Association Details
					Criteria associationCriteria = session.createCriteria(EntityAssociationTempTbl.class);
					associationCriteria
							.add(Restrictions.eq("entityTemp.entityTempId", entityTempTbl.getEntityTempId()));
					List<EntityAssociationTempTbl> associationList = associationCriteria.list();
					UserSBTbl user1 = new UserSBTbl();

					if (associationList != null && !associationList.isEmpty()) {
						for (EntityAssociationTempTbl association : associationList) {

							WebAssociationTempTbl webAssociationTbl = new WebAssociationTempTbl();
							webAssociationTbl.setWebentityAssociationTempId(association.getEntityAssociationTempId());
							webAssociationTbl.setAssociationName(association.getAssociationName());
							webAssociationTbl.setMembershipStartDate(association.getMembershipStartDate());
							webAssociationTbl.setMembershipEndDate(association.getMembershipEndDate());
							webAssociationTbl.setMembershipLevel(association.getMembershipLevel());
							webAssociationTbl.setActiveFlag(association.getActiveFlag());
							webAssociationTbl.setLastModifiedDatetime(association.getLastModifiedDatetime());
							webAssociationTbl.setLastModifiedBy(user1);
							webEntityTemp.setWebAssociationTempTbl(webAssociationTbl);

						}
					}

					// Fetch Partnership Details
					Criteria partnershipCriteria = session.createCriteria(PartnershipTempTbl.class);
					partnershipCriteria
							.add(Restrictions.eq("entityTemp.entityTempId", entityTempTbl.getEntityTempId()));
					List<PartnershipTempTbl> partnershipList = partnershipCriteria.list();
					WebUser user = new WebUser();
					// user.setUserId(userId);
					if (partnershipList != null && !partnershipList.isEmpty()) {
						for (PartnershipTempTbl partnership : partnershipList) {
							WebPartershipTbl webPartnership = new WebPartershipTbl();
							webPartnership.setEntityPartnershipId(partnership.getEntityPartnershipTempId());
							webPartnership.setPartnerCompanyName(partnership.getPartnerCompanyName());
							webPartnership.setPartnershipEndDate(partnership.getPartnershipEndDate());
							webPartnership.setPartnershipDescription(partnership.getPartnershipDescription());
							webPartnership.setActiveFlag(partnership.getActiveFlag());
							webPartnership.setLastModifiedDatetime(partnership.getLastModifiedDatetime());
							webPartnership.setPartnershipEndDate(partnership.getPartnershipEndDate());
							PartnershipTypeTbl partnershipType = partnership.getPartnershipType(); // This gets the
																									// PartnershipTypeTbl
																									// object
							if (partnershipType != null) {
								WebPartnershipTypeTbl webPartnershipType = new WebPartnershipTypeTbl();
								webPartnershipType.setPartnershipTypeId(partnershipType.getPartnershipTypeId());
								webPartnershipType.setPartnershipType(partnershipType.getPartnershipType());
								webPartnershipType.setPartnershipTypeDesc(partnershipType.getPartnershipTypeDesc());
								webPartnershipType.setActiveFlag(partnershipType.getActiveFlag());

								webPartnershipType.setLastModifiedDatetime(partnershipType.getLastModifiedDatetime());

								// Set the PartnershipType in WebPartnershipTbl
								webPartnership.setPartnershipType(webPartnershipType);
							}

//							UserDetailsTbl lastModifiedByUser = partnership.getLastModifiedByUser();
//					        if (lastModifiedByUser != null) {
//					            WebUser webUser = new WebUser();
//					            webUser.setUserId(lastModifiedByUser.getUserDetailsId());
//					             // Assuming UserSBTbl has a lastName field
//					            webUser.setEmail(lastModifiedByUser.getEmailAddress());
//					            // Set lastModifiedBy in the webPartnership object
//					            webPartnership.setLastModifiedBy(webUser);
//					        }
							// Set the partnership to the webEntityTemp
							webEntityTemp.setWebPartnership(webPartnership);
						}
					}

					// Fetch Cerfication Details

					Criteria cerficationCriteria = session.createCriteria(CertificationTempTbl.class);
					cerficationCriteria
							.add(Restrictions.eq("entityTemp.entityTempId", entityTempTbl.getEntityTempId()));
					List<CertificationTempTbl> certificationList = cerficationCriteria.list();

					if (certificationList != null && !certificationList.isEmpty()) {
						for (CertificationTempTbl certification : certificationList) {
							WebCertificationTbl webCertification = new WebCertificationTbl();
							webCertification.setEntityCertificationId(certification.getEntityCertificationTempId());
							webCertification.setCertificationName(certification.getCertificationName());
							webCertification.setCertificationExpiryDate(certification.getCertificationDate());
							webCertification.setCertificationDate(certification.getCertificationDate());
							webCertification.setCertificationIssuingBody(certification.getCertificationIssuingBody());
							webCertification.setActiveFlag(certification.getActiveFlag());
							webCertification.setLastModifiedDatetime(certification.getLastModifiedDatetime());
							// webCertification.setLastModifiedBy(certification.getLastModifiedBy().getUserId());

							// Set the partnership to the webEntityTemp
							webEntityTemp.setWebCertificationTbl(webCertification);
						}
					}

					// Add the populated webEntity to the list
					webEntityList.add(webEntityTemp);
				}
			} else {
				// If no records found for the given SBR number
				return new ResponseEntity<>(new WebMessage("No records found for SBR Number: " + sbrNumber),
						HttpStatus.OK);
			}
		} catch (Exception e) {
			log.error("Exception occurred in getRegistrationDashboardList: {}", e.getMessage(), e);
			return new ResponseEntity<>(new WebMessage("Error occurred while fetching data"),
					HttpStatus.INTERNAL_SERVER_ERROR);
		} finally {
			closeSession(session);
		}

		return new ResponseEntity<>(webEntityList, HttpStatus.OK);
	}

	@Override
	public ResponseEntity<?> getApprovedRegisterList() throws BviReturnsException {
		Session session = null;
		List<WebEntityTemp> webEntityTempList = new ArrayList<>();

		try {
			session = getSession();

			// Create criteria to fetch EntityTempTbl records with authInd as APPROVE or
			// REJECT
			Criteria entityTempCriteria = session.createCriteria(EntityTempTbl.class);
			entityTempCriteria.add(Restrictions.or(Restrictions.eq("authInd", Constants.APPROVE),
					Restrictions.eq("authInd", Constants.REJECT)));
			entityTempCriteria.addOrder(Order.desc("lastModifiedDatetime"));
			List<EntityTempTbl> entityTempTblList = entityTempCriteria.list();

			if (entityTempTblList != null && !entityTempTblList.isEmpty()) {
				for (EntityTempTbl entityTempTbl : entityTempTblList) {
					Integer userId = entityTempTbl.getUserId().getUserId();

					// Create WebEntityTemp object and populate its fields
					WebEntityTemp webEntityTemp = new WebEntityTemp();

					// Fetch UserSBLoginTbl for the user
					Criteria userSBLoginCriteria = session.createCriteria(UserSBLoginTbl.class);
					userSBLoginCriteria.add(Restrictions.eq("userId.userId", userId));
					UserSBLoginTbl userLoginTbl = (UserSBLoginTbl) userSBLoginCriteria.uniqueResult();

					// Set basic details
					webEntityTemp.setEntityTempId(entityTempTbl.getEntityTempId());
					webEntityTemp.setUsername(userLoginTbl.getUsername());
					webEntityTemp.setFirstname(entityTempTbl.getUserId().getFirstName());
					webEntityTemp.setLastname(entityTempTbl.getUserId().getLastName());
					webEntityTemp.setSbrNumber(entityTempTbl.getSbrNumber());
					webEntityTemp.setEntityName(entityTempTbl.getEntityName());

					// Set optional fields if they are not null
					if (entityTempTbl.getUrl() != null) {
						webEntityTemp.setUrl(entityTempTbl.getUrl());
					}
					if (entityTempTbl.getIncorporationDate() != null) {
						webEntityTemp.setIncorporationDate(entityTempTbl.getIncorporationDate());
					}
					if (entityTempTbl.getPhone() != null) {
						webEntityTemp.setPhone(entityTempTbl.getPhone());
					}
					if (entityTempTbl.getNumEmployees() != null) {
						webEntityTemp.setNumEmployees(entityTempTbl.getNumEmployees());
					}
					if (entityTempTbl.getCompanyProfile() != null) {
						webEntityTemp.setCompanyProfile(entityTempTbl.getCompanyProfile());
					}
					if (entityTempTbl.getAddress() != null) {
						webEntityTemp.setAddress(entityTempTbl.getAddress());
					}

					// Set status and other flags
					webEntityTemp.setActiveFlag(entityTempTbl.getActiveFlag());
					webEntityTemp.setAuthInd(entityTempTbl.getAuthInd());
					webEntityTemp.setLastModifiedDatetime(new Date());
					webEntityTemp.setLastModifiedBy(userId);

					if (entityTempTbl.getAuthInd() == Constants.APPROVE) {
						webEntityTemp.setStatus("Approved");
					} else {
						webEntityTemp.setStatus("Rejected");
					}

					webEntityTemp.setAuthBy(userId);
					webEntityTemp.setUserId(userId);

					if (entityTempTbl.getActiveFlag() == Constants.ACTIVE) {
						webEntityTemp.setTransactionName("Amendments");
					} else {
						webEntityTemp.setTransactionName("Registration");
					}

					// Set CompanyTypeTbl if available
					if (entityTempTbl.getCompanyTypeId() != null) {
						CompanyTypeTbl companyTypeTbl = session.get(CompanyTypeTbl.class,
								entityTempTbl.getCompanyTypeId().getCompanyTypeId());
						webEntityTemp.setCompanyTypeId(companyTypeTbl.getCompanyTypeId());
					}

					// Set BusinessOperationTypeTbl if available
					if (entityTempTbl.getBusinessOperationId() != null) {
						BusinessOperationTypeTbl businessOperationTypeTbl = session.get(BusinessOperationTypeTbl.class,
								entityTempTbl.getBusinessOperationId().getBusinessOperationId());
						webEntityTemp.setBusinessOperationId(businessOperationTypeTbl.getBusinessOperationId());
					}

					// Add to the list if transaction name is not "Amendments"
					if (!"Amendments".equals(webEntityTemp.getTransactionName())) {
						webEntityTempList.add(webEntityTemp);
					}
				}
			} else {
				return new ResponseEntity<>(new WebMessage("No records found"), HttpStatus.OK);
			}
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred in : getDashboardList" + e;
			log.error(errMessage, e);
		} finally {
			closeSession(session);
		}
		return new ResponseEntity<>(webEntityTempList, HttpStatus.OK);
	}

	public ResponseEntity<?> getDetailsByUser(Integer lastModifiedBy) throws BviReturnsException {
		Session session = null;
		try {
			session = getSession();

			// Fetch data from EntityTempTbl
			Criteria criteria1 = session.createCriteria(EntityTempTbl.class);
			criteria1.add(Restrictions.eq("lastModifiedBy.userId", lastModifiedBy));
			List<EntityTempTbl> entityTempTblList = criteria1.list();
			// Fetch data from EntityTbl
			Criteria criteria2 = session.createCriteria(EntityTbl.class);
			criteria2.add(Restrictions.eq("lastModifiedBy.userId", lastModifiedBy));
			List<EntityTbl> entityTblList = criteria2.list();

			// Create a new WebEntityTemp object to hold the combined results
			WebEntityTemp webEntityTemp = new WebEntityTemp();

			// Check and set values from EntityTbl (use the first result if multiple exist)
			if (!entityTblList.isEmpty()) {
				EntityTbl entityTbl = entityTblList.get(0); // Use the first result
				if (entityTbl.getSbrNumber() != null) {
					webEntityTemp.setSbrNumber(entityTbl.getSbrNumber());
				}
				if (entityTbl.getEntityId() != null) {
					webEntityTemp.setEntityId(entityTbl.getEntityId());
				}
				if (entityTbl.getEntityName() != null) {
					webEntityTemp.setEntityName(entityTbl.getEntityName());
				}
				if (entityTbl.getUrl() != null) {
					webEntityTemp.setUrl(entityTbl.getUrl());
				}

				if (entityTbl.getIncorporationDate() != null) {
					webEntityTemp.setIncorporationDate(entityTbl.getIncorporationDate());
				}
				if (entityTbl.getCompanyProfile() != null) {
					webEntityTemp.setCompanyProfile(entityTbl.getCompanyProfile());
				}
				if (entityTbl.getNumEmployees() != null) {
					webEntityTemp.setNumEmployees(entityTbl.getNumEmployees());
				}
				if (entityTbl.getAddress() != null) {
					webEntityTemp.setAddress(entityTbl.getAddress());
				}

				webEntityTemp.setActiveFlag(entityTbl.getActiveFlag());

			}

			// Check and set values from EntityTempTbl (use the first result if multiple
			// exist)
			if (!entityTempTblList.isEmpty()) {
				EntityTempTbl entityTempTbl = entityTempTblList.get(0); // Use the first result
				if (entityTempTbl.getSbrNumber() != null) {
					webEntityTemp.setSbrNumber(entityTempTbl.getSbrNumber());
				}
				webEntityTemp.setEntityName(entityTempTbl.getEntityName());
				if (entityTempTbl.getEntityId() != null) {
					webEntityTemp.setEntityId(entityTempTbl.getEntityId());
				}
				if (entityTempTbl.getEntityName() != null) {
					webEntityTemp.setEntityName(entityTempTbl.getEntityName());
				}
				if (entityTempTbl.getUrl() != null) {
					webEntityTemp.setUrl(entityTempTbl.getUrl());
				}

				if (entityTempTbl.getIncorporationDate() != null) {
					webEntityTemp.setIncorporationDate(entityTempTbl.getIncorporationDate());
				}
				if (entityTempTbl.getCompanyProfile() != null) {
					webEntityTemp.setCompanyProfile(entityTempTbl.getCompanyProfile());
				}
				if (entityTempTbl.getNumEmployees() != null) {
					webEntityTemp.setNumEmployees(entityTempTbl.getNumEmployees());
				}
				if (entityTempTbl.getAddress() != null) {
					webEntityTemp.setAddress(entityTempTbl.getAddress());
				}

				webEntityTemp.setActiveFlag(entityTempTbl.getActiveFlag());

			}

			// Return the combined result if either entityTempTblList or entityTblList is
			// not empty
			if (!entityTempTblList.isEmpty() || !entityTblList.isEmpty()) {
				return new ResponseEntity<>(webEntityTemp, HttpStatus.OK);
			} else {
				return new ResponseEntity<>("No details found for the given user", HttpStatus.NOT_FOUND);
			}
		} catch (Exception e) {
			throw new BviReturnsException("Error while fetching details by user", e);
		} finally {
			if (session != null) {
				session.close();
			}
		}
	}

}
