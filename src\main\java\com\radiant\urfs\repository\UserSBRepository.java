package com.radiant.urfs.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import com.radiant.urfs.domain.UserLoginTbl;
import com.radiant.urfs.domain.UserSBLoginTbl;

@Repository
public interface UserSBRepository extends CrudRepository<UserSBLoginTbl, Integer> {

	//public User findByUserNameforLogin(String username) throws UsernameNotFoundException;

	public Optional<UserSBLoginTbl> findByUsername(String username);


	public Optional<UserSBLoginTbl> findByUserLoginId(Integer userLoginId);

	public String existsByUsername(String username);
	
//	public User findByUserName(String username);

}
