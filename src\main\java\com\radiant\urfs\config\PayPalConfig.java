package com.radiant.urfs.config;

import com.paypal.base.rest.APIContext;
import com.paypal.base.rest.OAuthTokenCredential;
import com.paypal.base.rest.PayPalRESTException;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class PayPalConfig {

//	@Value("${paypal.client.id}")
//	private String clientId;
//
//	@Value("${paypal.client.secret}")
//	private String clientSecret;
//
//	@Value("${paypal.mode}")
//	private String mode;
	
	
//	 @Value("${paypal.client-id}")
//	    private String clientId;
//	    @Value("${paypal.client-secret}")
//	    private String clientSecret;
//	    @Value("${paypal.mode}")
//	    private String mode;
	
	
	private String clientId ="Aa_VTfV_nhIoaPeVHy_XkaTD3B00mBYRYaL8_68qPbnIdDd5ZWXvInFFVpO6zjZBgSQUHSLsqZ9XHanp";
	 private String clientSecret ="EF7hrucbwito4sF2G0EVF9_GDwEqDvMuux5G9HYotnBpXyILwsgX7eUdGyRxMJCvS16s_pbm3BW7Op4k";
//	private String clientId = "Af8dkzgPSL35MMLHTBTmK_HXxgnnfbY0Rk-QL1dmPabFszLuut_0GCRQmHR4SLUrDIg8Y2jKbz_IRycu";
 //   private String clientSecret = "ELF-ftoVg05L6Dpl2DuWk20gypvKsErO1KqjS-aIADpytymiIjIVPltxLKafqHuqdJ9XwmSvufX-1_ce";
    private String mode = "sandbox"; 


	@Bean
	public Map<String, String> paypalSdkConfig() {
		Map<String, String> configMap = new HashMap<>();
		configMap.put("mode", mode);
		return configMap;
	}

	@Bean
	public OAuthTokenCredential authTokenCredential() {
		return new OAuthTokenCredential(clientId, clientSecret, paypalSdkConfig());
	}

	@Bean
	public APIContext apiContext() throws PayPalRESTException {
		APIContext context = new APIContext(authTokenCredential().getAccessToken());
		context.setConfigurationMap(paypalSdkConfig());
		return context;
	}
}
