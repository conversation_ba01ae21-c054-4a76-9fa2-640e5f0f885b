package com.radiant.urfs.web.model;

import java.math.BigDecimal;
import java.util.Date;

import com.radiant.urfs.domain.UserSBTbl;

public class WebPaypalTransaction {
	
	
	private Integer paypalTransactionsId;

   
    private String paypalConfirmationNumber;

   
    private BigDecimal paypalTransactionAmt;

  
    private String  paypalTransactionDatetime;

   
    private String paypalMessage;

   
    private String  lastModifiedDatetime;

   
    private UserSBTbl lastModifiedBy;
    
    private String companyName;



	public Integer getPaypalTransactionsId() {
		return paypalTransactionsId;
	}


	public void setPaypalTransactionsId(Integer paypalTransactionsId) {
		this.paypalTransactionsId = paypalTransactionsId;
	}


	public String getPaypalConfirmationNumber() {
		return paypalConfirmationNumber;
	}


	public void setPaypalConfirmationNumber(String paypalConfirmationNumber) {
		this.paypalConfirmationNumber = paypalConfirmationNumber;
	}


	public BigDecimal getPaypalTransactionAmt() {
		return paypalTransactionAmt;
	}


	public void setPaypalTransactionAmt(BigDecimal paypalTransactionAmt) {
		this.paypalTransactionAmt = paypalTransactionAmt;
	}


	

	public String getPaypalMessage() {
		return paypalMessage;
	}


	public void setPaypalMessage(String paypalMessage) {
		this.paypalMessage = paypalMessage;
	}


	

	public UserSBTbl getLastModifiedBy() {
		return lastModifiedBy;
	}


	public void setLastModifiedBy(UserSBTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}


	public String getCompanyName() {
		return companyName;
	}


	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}


	public String getPaypalTransactionDatetime() {
		return paypalTransactionDatetime;
	}


	public void setPaypalTransactionDatetime(String paypalTransactionDatetime) {
		this.paypalTransactionDatetime = paypalTransactionDatetime;
	}


	public String getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}


	public void setLastModifiedDatetime(String lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}
    
    
    


}
