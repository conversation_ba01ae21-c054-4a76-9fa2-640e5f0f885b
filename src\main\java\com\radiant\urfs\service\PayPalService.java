package com.radiant.urfs.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.paypal.api.payments.Amount;
import com.paypal.api.payments.Payer;
import com.paypal.api.payments.Payment;
import com.paypal.api.payments.PaymentExecution;
import com.paypal.api.payments.RedirectUrls;
import com.paypal.api.payments.Transaction;
import com.paypal.base.rest.APIContext;
import com.paypal.base.rest.PayPalRESTException;

//	@Service
//	public class PayPalService {
//
//	    @Autowired
//	    private APIContext apiContext;
//
//	    public Payment createPayment(
//	            Double total, 
//	            String currency, 
//	            String method, 
//	            String intent, 
//	            String description, 
//	            String cancelUrl, 
//	            String successUrl) throws PayPalRESTException {
//
//	        Amount amount = new Amount();
//	        amount.setCurrency(currency);
//	        amount.setTotal(String.format("%.2f", total));
//
//	        Transaction transaction = new Transaction();
//	        transaction.setDescription(description);
//	        transaction.setAmount(amount);
//
//	        List<Transaction> transactions = new ArrayList<>();
//	        transactions.add(transaction);
//
//	        Payer payer = new Payer();
//	        payer.setPaymentMethod(method.toString());
//
//	        Payment payment = new Payment();
//	        payment.setIntent(intent);
//	        payment.setPayer(payer);
//	        payment.setTransactions(transactions);
//
//	        RedirectUrls redirectUrls = new RedirectUrls();
//	        redirectUrls.setCancelUrl(cancelUrl);
//	        redirectUrls.setReturnUrl(successUrl);
//	        payment.setRedirectUrls(redirectUrls);
//
//	        return payment.create(apiContext);
//	    }
//
//	    public Payment executePayment(String paymentId, String payerId) throws PayPalRESTException {
//	        Payment payment = new Payment();
//	        payment.setId(paymentId);
//	        PaymentExecution paymentExecute = new PaymentExecution();
//	        paymentExecute.setPayerId(payerId);
//	        return payment.execute(apiContext, paymentExecute);
//	    }
//	}
//
//}

@Service
public class PayPalService {

    @Autowired
    private APIContext apiContext;

    public Payment createPayment(
            Double total,
            String currency,
            String method,
            String intent,
            String description,
            String cancelUrl, 
            String returnUrl) throws PayPalRESTException {

        Amount amount = new Amount();
        amount.setCurrency(currency);
        amount.setTotal(String.format("%.2f", total));

        Transaction transaction = new Transaction();
        transaction.setDescription(description);
        transaction.setAmount(amount);

        List<Transaction> transactions = new ArrayList<>();
        transactions.add(transaction);

        Payer payer = new Payer();
        payer.setPaymentMethod(method); // No need to call toString()

        Payment payment = new Payment();
        payment.setIntent(intent);
        payment.setPayer(payer);
        payment.setTransactions(transactions);

        // Correctly set the redirect URLs
        RedirectUrls redirectUrls = new RedirectUrls();
        redirectUrls.setCancelUrl(cancelUrl);   // Set cancel URL
        redirectUrls.setReturnUrl(returnUrl);   // Set return URL
        payment.setRedirectUrls(redirectUrls);  // Attach to payment

        // Execute the payment creation
        return payment.create(apiContext);
    }

    public Payment executePayment(String paymentId, String payerId) throws PayPalRESTException {
        Payment payment = new Payment();
        payment.setId(paymentId);
        PaymentExecution paymentExecute = new PaymentExecution();
        paymentExecute.setPayerId(payerId);
        return payment.execute(apiContext, paymentExecute);
    }
}

