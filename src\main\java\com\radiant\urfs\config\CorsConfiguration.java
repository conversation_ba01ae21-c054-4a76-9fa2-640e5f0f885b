package com.radiant.urfs.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public abstract class CorsConfiguration implements WebMvcConfigurer {
	
	public static String GET = "GET";
	public static String POST = "POST";
	public static String PUT = "PUT";
	public static String DELETE = "DELETE";
	public static String OPTIONS = "OPTIONS";
	
	@Override
	public void addCorsMappings(CorsRegistry registry) {
		registry.addMapping("*").allowedMethods(OPTIONS, GET, PUT, POST, DELETE).allowedOrigins("*")
				.allowedHeaders("*");
	}
}