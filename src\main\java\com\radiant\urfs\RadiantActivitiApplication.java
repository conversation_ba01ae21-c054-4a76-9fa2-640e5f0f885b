package com.radiant.urfs;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;


@SpringBootApplication
@EntityScan(basePackages = { "com.radiant.urfs.*" }) 
@EnableScheduling
public class RadiantActivitiApplication extends SpringBootServletInitializer {

	public static void main(String[] args) {
		SpringApplication.run(RadiantActivitiApplication.class, args);
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
		return builder.sources(RadiantActivitiApplication.class);
	}

	
	  @Bean public WebMvcConfigurer corsConfigurer() { return new
	  WebMvcConfigurerAdapter() {
	  
		  @Override 
		  public void addCorsMappings(CorsRegistry registry) {
				registry.addMapping("/**").allowedMethods("GET", "POST", "PUT", "DELETE").allowedOrigins("*")
						.allowedHeaders("*");
			}
	  };
	}
	
	  
	
	  
	  
	  
	  

}
