package com.radiant.urfs.domain;

public class UserInfoDTO {

	private Integer userLoginId;
	private String username;
	private String firstName;
	private String lastName;
	private Integer age;
	private String email;
	private String executationId;
	private String procInstId;

	public UserInfoDTO() {
		// TODO Auto-generated constructor stub
	}

	public UserInfoDTO(Integer userLoginId, String username, String firstName, String lastName, Integer age,
			String email, String executationId, String procInstId) {
		super();
		this.userLoginId = userLoginId;
		this.username = username;
		this.firstName = firstName;
		this.lastName = lastName;
		this.age = age;
		this.email = email;
		this.executationId = executationId;
		this.procInstId = procInstId;
	}

	public Integer getUserLoginId() {
		return userLoginId;
	}

	public void setUserLoginId(Integer userLoginId) {
		this.userLoginId = userLoginId;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public Integer getAge() {
		return age;
	}

	public void setAge(Integer age) {
		this.age = age;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getExecutationId() {
		return executationId;
	}

	public void setExecutationId(String executationId) {
		this.executationId = executationId;
	}

	public String getProcInstId() {
		return procInstId;
	}

	public void setProcInstId(String procInstId) {
		this.procInstId = procInstId;
	}

}
