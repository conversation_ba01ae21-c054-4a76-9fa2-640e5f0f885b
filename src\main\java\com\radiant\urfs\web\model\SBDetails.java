package com.radiant.urfs.web.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

import com.radiant.urfs.domain.BusinessOperationTypeTbl;
import com.radiant.urfs.domain.BusinessPurposeTbl;
import com.radiant.urfs.domain.CertificationTbl;
import com.radiant.urfs.domain.CertificationTempTbl;
import com.radiant.urfs.domain.CompanyTypeTbl;
import com.radiant.urfs.domain.EntityAssociationTbl;
import com.radiant.urfs.domain.EntityAssociationTempTbl;
import com.radiant.urfs.domain.EntityTbl;
import com.radiant.urfs.domain.EntityTempTbl;
import com.radiant.urfs.domain.PartnershipTbl;
import com.radiant.urfs.domain.PartnershipTempTbl;
import com.radiant.urfs.domain.PaymentStatusTbl;
import com.radiant.urfs.domain.PaymentsTbl;
import com.radiant.urfs.domain.PaypalTransaction;
import com.radiant.urfs.domain.PaypalTransactionsTbl;
import com.radiant.urfs.domain.ProductPricingTbl;
import com.radiant.urfs.domain.RoleTbl;
import com.radiant.urfs.domain.TransactionTypeTbl;
import com.radiant.urfs.domain.UserDetailsTbl;
import com.radiant.urfs.domain.UserLoginTbl;
import com.radiant.urfs.domain.UserSBRoleTbl;
import com.radiant.urfs.domain.UserSBTbl;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class SBDetails {
    private BaseData baseData;
    private BusinessOperationTypeTbl businessOperationTypeTbl;
    private BusinessPurposeTbl businessPurposeTbl;
    private CompanyTypeTbl companyTypeTbl;
    private EntityTbl entityTbl;
    private EntityTempTbl entityTempTbl;
    private PaymentStatusTbl paymentStatusTbl;
    private PaymentsTbl paymentsTbl;
    private PaypalTransactionsTbl paypalTransactionsTbl;
    private ProductPricingTbl productPricingTbl;
    private TransactionTypeTbl transactionTypeTbl;
    private RoleTbl roleTbl;
    private UserDetailsTbl userDetailsTbl;
    private UserLoginTbl userLoginTbl;
    private UserSBRoleTbl userRoleTbl;
    private UserSBTbl userTbl;
    private List<SBDetails> sbDetailsList;  // in case you need a list of these details
    private PartnershipTempTbl partnershipTempTbl;
    private PartnershipTbl partnershipTbl;
    private CertificationTempTbl certificationTempTbl;
    private CertificationTbl certificationTbl;
    private EntityAssociationTempTbl entityAssociationTempTbl;
    private EntityAssociationTbl entityAssociationTbl;

    
    
	// If these tables contain lists of entities, you might want to include them as well:
    private List<BusinessOperationTypeTbl> businessOperationTypeTblList;
    private List<BusinessPurposeTbl> businessPurposeTblList;
    private List<CompanyTypeTbl> companyTypeTblList;
    private List<EntityTbl> entityTblList;
    private List<EntityTempTbl> entityTempTblList;
    private List<PaymentStatusTbl> paymentStatusTblList;
    private List<PaymentsTbl> paymentsTblList;
    private List<PaypalTransactionsTbl> paypalTransactionsTblList;
    private List<ProductPricingTbl> productPricingTblList;
    private List<RoleTbl> roleTblList;
    private List<UserDetailsTbl> userDetailsTblList;
    private List<UserLoginTbl> userLoginTblList;
    private List<UserSBRoleTbl> userRoleTblList;
    private List<UserSBTbl> userTblList;
    private List<PartnershipTempTbl> partnershipTempList;
    private List<PartnershipTbl> partnershipTblList;
    private List<CertificationTempTbl> certificationTempList;
    private List<CertificationTbl> certificationTblList;
    private List<EntityAssociationTempTbl> entityAssociationTempTblList;
    private List<PaypalTransaction> paypalTransactionList;
    private List<EntityAssociationTbl> entityAssociationTblList;
   
    public PartnershipTempTbl getPartnershipTempTbl() {
		return partnershipTempTbl;
	}
	public void setPartnershipTempTbl(PartnershipTempTbl partnershipTempTbl) {
		this.partnershipTempTbl = partnershipTempTbl;
	}
	public PartnershipTbl getPartnershipTbl() {
		return partnershipTbl;
	}
	public void setPartnershipTbl(PartnershipTbl partnershipTbl) {
		this.partnershipTbl = partnershipTbl;
	}
	public CertificationTempTbl getCertificationTempTbl() {
		return certificationTempTbl;
	}
	public void setCertificationTempTbl(CertificationTempTbl certificationTempTbl) {
		this.certificationTempTbl = certificationTempTbl;
	}
	public CertificationTbl getCertificationTbl() {
		return certificationTbl;
	}
	public void setCertificationTbl(CertificationTbl certificationTbl) {
		this.certificationTbl = certificationTbl;
	}
	public BaseData getBaseData() {
		return baseData;
	}
	public void setBaseData(BaseData baseData) {
		this.baseData = baseData;
	}
	public BusinessOperationTypeTbl getBusinessOperationTypeTbl() {
		return businessOperationTypeTbl;
	}
	public void setBusinessOperationTypeTbl(BusinessOperationTypeTbl businessOperationTypeTbl) {
		this.businessOperationTypeTbl = businessOperationTypeTbl;
	}
	public BusinessPurposeTbl getBusinessPurposeTbl() {
		return businessPurposeTbl;
	}
	public void setBusinessPurposeTbl(BusinessPurposeTbl businessPurposeTbl) {
		this.businessPurposeTbl = businessPurposeTbl;
	}
	
	public CompanyTypeTbl getCompanyTypeTbl() {
		return companyTypeTbl;
	}
	public void setCompanyTypeTbl(CompanyTypeTbl companyTypeTbl) {
		this.companyTypeTbl = companyTypeTbl;
	}
	public EntityTbl getEntityTbl() {
		return entityTbl;
	}
	public void setEntityTbl(EntityTbl entityTbl) {
		this.entityTbl = entityTbl;
	}
	public EntityTempTbl getEntityTempTbl() {
		return entityTempTbl;
	}
	public void setEntityTempTbl(EntityTempTbl entityTempTbl) {
		this.entityTempTbl = entityTempTbl;
	}
	public PaymentStatusTbl getPaymentStatusTbl() {
		return paymentStatusTbl;
	}
	public void setPaymentStatusTbl(PaymentStatusTbl paymentStatusTbl) {
		this.paymentStatusTbl = paymentStatusTbl;
	}
	public PaymentsTbl getPaymentsTbl() {
		return paymentsTbl;
	}
	public void setPaymentsTbl(PaymentsTbl paymentsTbl) {
		this.paymentsTbl = paymentsTbl;
	}
	public PaypalTransactionsTbl getPaypalTransactionsTbl() {
		return paypalTransactionsTbl;
	}
	public void setPaypalTransactionsTbl(PaypalTransactionsTbl paypalTransactionsTbl) {
		this.paypalTransactionsTbl = paypalTransactionsTbl;
	}
	public ProductPricingTbl getProductPricingTbl() {
		return productPricingTbl;
	}
	public void setProductPricingTbl(ProductPricingTbl productPricingTbl) {
		this.productPricingTbl = productPricingTbl;
	}
	public RoleTbl getRoleTbl() {
		return roleTbl;
	}
	public void setRoleTbl(RoleTbl roleTbl) {
		this.roleTbl = roleTbl;
	}
	public UserDetailsTbl getUserDetailsTbl() {
		return userDetailsTbl;
	}
	public void setUserDetailsTbl(UserDetailsTbl userDetailsTbl) {
		this.userDetailsTbl = userDetailsTbl;
	}
	public UserLoginTbl getUserLoginTbl() {
		return userLoginTbl;
	}
	public void setUserLoginTbl(UserLoginTbl userLoginTbl) {
		this.userLoginTbl = userLoginTbl;
	}
	public UserSBRoleTbl getUserRoleTbl() {
		return userRoleTbl;
	}
	public void setUserRoleTbl(UserSBRoleTbl userRoleTbl) {
		this.userRoleTbl = userRoleTbl;
	}
	public UserSBTbl getUserTbl() {
		return userTbl;
	}
	public void setUserTbl(UserSBTbl userTbl) {
		this.userTbl = userTbl;
	}
	public List<SBDetails> getSbDetailsList() {
		return sbDetailsList;
	}
	
	
	public EntityAssociationTempTbl getEntityAssociationTempTbl() {
		return entityAssociationTempTbl;
	}
	public void setEntityAssociationTempTbl(EntityAssociationTempTbl entityAssociationTempTbl) {
		this.entityAssociationTempTbl = entityAssociationTempTbl;
	}
	public void setSbDetailsList(List<SBDetails> sbDetailsList) {
		this.sbDetailsList = sbDetailsList;
	}
	public List<BusinessOperationTypeTbl> getBusinessOperationTypeTblList() {
		return businessOperationTypeTblList;
	}
	public void setBusinessOperationTypeTblList(List<BusinessOperationTypeTbl> businessOperationTypeTblList) {
		this.businessOperationTypeTblList = businessOperationTypeTblList;
	}
	public List<BusinessPurposeTbl> getBusinessPurposeTblList() {
		return businessPurposeTblList;
	}
	public void setBusinessPurposeTblList(List<BusinessPurposeTbl> businessPurposeTblList) {
		this.businessPurposeTblList = businessPurposeTblList;
	}

	public List<CompanyTypeTbl> getCompanyTypeTblList() {
		return companyTypeTblList;
	}
	public void setCompanyTypeTblList(List<CompanyTypeTbl> companyTypeTblList) {
		this.companyTypeTblList = companyTypeTblList;
	}
	public List<EntityTbl> getEntityTblList() {
		return entityTblList;
	}
	public void setEntityTblList(List<EntityTbl> entityTblList) {
		this.entityTblList = entityTblList;
	}
	public List<EntityTempTbl> getEntityTempTblList() {
		return entityTempTblList;
	}
	public void setEntityTempTblList(List<EntityTempTbl> entityTempTblList) {
		this.entityTempTblList = entityTempTblList;
	}
	public List<PaymentStatusTbl> getPaymentStatusTblList() {
		return paymentStatusTblList;
	}
	public void setPaymentStatusTblList(List<PaymentStatusTbl> paymentStatusTblList) {
		this.paymentStatusTblList = paymentStatusTblList;
	}
	public List<PaymentsTbl> getPaymentsTblList() {
		return paymentsTblList;
	}
	public void setPaymentsTblList(List<PaymentsTbl> paymentsTblList) {
		this.paymentsTblList = paymentsTblList;
	}
	public List<PaypalTransactionsTbl> getPaypalTransactionsTblList() {
		return paypalTransactionsTblList;
	}
	public void setPaypalTransactionsTblList(List<PaypalTransactionsTbl> paypalTransactionsTblList) {
		this.paypalTransactionsTblList = paypalTransactionsTblList;
	}
	public List<ProductPricingTbl> getProductPricingTblList() {
		return productPricingTblList;
	}
	public void setProductPricingTblList(List<ProductPricingTbl> productPricingTblList) {
		this.productPricingTblList = productPricingTblList;
	}
	public List<RoleTbl> getRoleTblList() {
		return roleTblList;
	}
	public void setRoleTblList(List<RoleTbl> roleTblList) {
		this.roleTblList = roleTblList;
	}
	public List<UserDetailsTbl> getUserDetailsTblList() {
		return userDetailsTblList;
	}
	public void setUserDetailsTblList(List<UserDetailsTbl> userDetailsTblList) {
		this.userDetailsTblList = userDetailsTblList;
	}
	public List<UserLoginTbl> getUserLoginTblList() {
		return userLoginTblList;
	}
	public void setUserLoginTblList(List<UserLoginTbl> userLoginTblList) {
		this.userLoginTblList = userLoginTblList;
	}
	public List<UserSBRoleTbl> getUserRoleTblList() {
		return userRoleTblList;
	}
	public void setUserRoleTblList(List<UserSBRoleTbl> userRoleTblList) {
		this.userRoleTblList = userRoleTblList;
	}
	public List<UserSBTbl> getUserTblList() {
		return userTblList;
	}
	public void setUserTblList(List<UserSBTbl> userTblList) {
		this.userTblList = userTblList;
	}
	public TransactionTypeTbl getTransactionTypeTbl() {
		return transactionTypeTbl;
	}
	public void setTransactionTypeTbl(TransactionTypeTbl transactionTypeTbl) {
		this.transactionTypeTbl = transactionTypeTbl;
	}
	public List<PartnershipTempTbl> getPartnershipTempList() {
		return partnershipTempList;
	}
	public void setPartnershipTempList(List<PartnershipTempTbl> partnershipTempList) {
		this.partnershipTempList = partnershipTempList;
	}
	public List<PartnershipTbl> getPartnershipTblList() {
		return partnershipTblList;
	}
	public void setPartnershipTblList(List<PartnershipTbl> partnershipTblList) {
		this.partnershipTblList = partnershipTblList;
	}
	public List<CertificationTempTbl> getCertificationTempList() {
		return certificationTempList;
	}
	public void setCertificationTempList(List<CertificationTempTbl> certificationTempList) {
		this.certificationTempList = certificationTempList;
	}
	public List<CertificationTbl> getCertificationTblList() {
		return certificationTblList;
	}
	public void setCertificationTblList(List<CertificationTbl> certificationTblList) {
		this.certificationTblList = certificationTblList;
	}
	public List<EntityAssociationTempTbl> getEntityAssociationTempTblList() {
		return entityAssociationTempTblList;
	}
	public void setEntityAssociationTempTblList(List<EntityAssociationTempTbl> entityAssociationTempTblList) {
		this.entityAssociationTempTblList = entityAssociationTempTblList;
	}
	public List<PaypalTransaction> getPaypalTransactionList() {
		return paypalTransactionList;
	}
	public void setPaypalTransactionList(List<PaypalTransaction> paypalTransactionList) {
		this.paypalTransactionList = paypalTransactionList;
	}
	public EntityAssociationTbl getEntityAssociationTbl() {
		return entityAssociationTbl;
	}
	public void setEntityAssociationTbl(EntityAssociationTbl entityAssociationTbl) {
		this.entityAssociationTbl = entityAssociationTbl;
	}
	public List<EntityAssociationTbl> getEntityAssociationTblList() {
		return entityAssociationTblList;
	}
	public void setEntityAssociationTblList(List<EntityAssociationTbl> entityAssociationTblList) {
		this.entityAssociationTblList = entityAssociationTblList;
	}

    
}
