package com.radiant.urfs.dao.impl;

import java.util.Date;
import com.radiant.urfs.domain.UserSBTbl;

public class WebCertificationTbl {

    private int entityCertificationId;

    private String certificationName;

    private Date certificationDate;

    private Date certificationExpiryDate;

    private String certificationIssuingBody;

    private char activeFlag;

    private Date lastModifiedDatetime;

    private UserSBTbl lastModifiedBy;
    

	public int getEntityCertificationId() {
		return entityCertificationId;
	}

	public void setEntityCertificationId(int entityCertificationId) {
		this.entityCertificationId = entityCertificationId;
	}

	public String getCertificationName() {
		return certificationName;
	}

	public void setCertificationName(String certificationName) {
		this.certificationName = certificationName;
	}

	public Date getCertificationDate() {
		return certificationDate;
	}

	public void setCertificationDate(Date certificationDate) {
		this.certificationDate = certificationDate;
	}

	public Date getCertificationExpiryDate() {
		return certificationExpiryDate;
	}

	public void setCertificationExpiryDate(Date certificationExpiryDate) {
		this.certificationExpiryDate = certificationExpiryDate;
	}

	public String getCertificationIssuingBody() {
		return certificationIssuingBody;
	}

	public void setCertificationIssuingBody(String certificationIssuingBody) {
		this.certificationIssuingBody = certificationIssuingBody;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public UserSBTbl getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(UserSBTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}
    
    
    

}
