package com.radiant.urfs.service;

import java.util.Optional;

import org.springframework.stereotype.Component;

import com.radiant.urfs.domain.UserLoginTbl;
import com.radiant.urfs.domain.UserDTO;

@Component
public class UserMapperConverter {

	public UserDTO entityToDto(Optional<UserLoginTbl> userInfo) {
		return null;
		/*
		 * 
		 * UserDTO userDTO = new UserDTO(); if (userInfo.isPresent()) { User user =
		 * userInfo.get(); userDTO.setUserLoginId(user.getUserLoginId());
		 * userDTO.setUsername(user.getUsername());
		 * userDTO.setFirstName(user.getFirstName());
		 * userDTO.setLastName(user.getLastName()); userDTO.setAge(user.getAge());
		 * userDTO.setEmail(user.getEmail()); } return userDTO;
		 */}
}
