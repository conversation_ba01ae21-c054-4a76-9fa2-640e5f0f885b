package com.radiant.urfs.web.model;

import java.util.Date;



public class WebPartnershipTypeTbl {

	private int partnershipTypeId;

	private String partnershipType;

	private String partnershipTypeDesc; // Corrected to match SQL definition

	private char activeFlag;

	private Date lastModifiedDatetime;
	
	

	public int getPartnershipTypeId() {
		return partnershipTypeId;
	}

	public void setPartnershipTypeId(int partnershipTypeId) {
		this.partnershipTypeId = partnershipTypeId;
	}

	public String getPartnershipType() {
		return partnershipType;
	}

	public void setPartnershipType(String partnershipType) {
		this.partnershipType = partnershipType;
	}

	public String getPartnershipTypeDesc() {
		return partnershipTypeDesc;
	}

	public void setPartnershipTypeDesc(String partnershipTypeDesc) {
		this.partnershipTypeDesc = partnershipTypeDesc;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}
	
	
	

}
