package com.radiant.urfs.web.model;

import java.util.Date;

public class WebUserDetails {

	private int userId;
	private String firstName;
	private String lastName;
	private char activeFlag;
	private String userRoleName;
	private String userRoleDescription;
	private String email;
	private String userName;
	private String password;
	private Date lastModifiedDatetime;
	private String status;
	private String createdDate;
	private Integer lastModifiedBy;


	
	public int getUserId() {
		return userId;
	}
	public void setUserId(int userId) {
		this.userId = userId;
	}
	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	public String getLastName() {
		return lastName;
	}
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	public char getActiveFlag() {
		return activeFlag;
	}
	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}
	public String getUserRoleName() {
		return userRoleName;
	}
	public void setUserRoleName(String userRoleName) {
		this.userRoleName = userRoleName;
	}
	public String getUserRoleDescription() {
		return userRoleDescription;
	}
	public void setUserRoleDescription(String userRoleDescription) {
		this.userRoleDescription = userRoleDescription;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}
	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	
	public Integer getLastModifiedBy() {
	    return lastModifiedBy;
	}
	
	public void setLastModifiedBy(Integer lastModifiedBy) {
	    this.lastModifiedBy = lastModifiedBy;
	}
	
	public String getCreatedDate() {
	   return createdDate;
	}
	
	public void setCreatedDate(String createdDate) {
		this.createdDate = createdDate;
	}
		 	
	
}
