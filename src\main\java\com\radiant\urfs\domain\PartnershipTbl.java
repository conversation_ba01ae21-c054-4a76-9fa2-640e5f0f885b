package com.radiant.urfs.domain;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "ENTITY_PARTNERSHIP_TBL")
public class PartnershipTbl implements Serializable {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ENTITY_PARTNERSHIP_ID")
	private int entityPartnershipId;

	@ManyToOne
	@JoinColumn(name = "ENTITY_ID", referencedColumnName = "ENTITY_ID")
	private EntityTbl entity;

	@Column(name = "PARTNER_COMPANY_NAME", length = 40)
	private String partnerCompanyName;

	@ManyToOne
	@JoinColumn(name = "PARTNERSHIP_TYPE_ID", referencedColumnName = "PARTNERSHIP_TYPE_ID")
	private PartnershipTypeTbl partnershipType;

	 @Column(name = "PARTNERSHIP_END_DATE")
	    @Temporal(TemporalType.TIMESTAMP)
	    private Date partnershipEndDate;

	@Column(name = "PARTNERSHIP_DESCRIPTION", length = 400)
	private String partnershipDescription;
	

	@Column(name = "ACTIVE_FLAG", length = 1)
	private char activeFlag;

	@Column(name = "LAST_MODIFIED_DATETIME")
	@Temporal(TemporalType.TIMESTAMP)
	private Date lastModifiedDatetime;

	@ManyToOne
	@JoinColumn(name = "LAST_MODIFIED_BY", referencedColumnName = "USER_ID")
	private UserSBTbl lastModifiedBy;

	// Getters and Setters
	public int getEntityPartnershipId() {
		return entityPartnershipId;
	}

	public void setEntityPartnershipId(int entityPartnershipId) {
		this.entityPartnershipId = entityPartnershipId;
	}

	public EntityTbl getEntity() {
		return entity;
	}

	public void setEntity(EntityTbl entity) {
		this.entity = entity;
	}

	public String getPartnerCompanyName() {
		return partnerCompanyName;
	}

	public void setPartnerCompanyName(String partnerCompanyName) {
		this.partnerCompanyName = partnerCompanyName;
	}

	public PartnershipTypeTbl getPartnershipType() {
		return partnershipType;
	}

	public void setPartnershipType(PartnershipTypeTbl partnershipType) {
		this.partnershipType = partnershipType;
	}

	

	public Date getPartnershipEndDate() {
		return partnershipEndDate;
	}

	public void setPartnershipEndDate(Date partnershipEndDate) {
		this.partnershipEndDate = partnershipEndDate;
	}

	public String getPartnershipDescription() {
		return partnershipDescription;
	}

	public void setPartnershipDescription(String partnershipDescription) {
		this.partnershipDescription = partnershipDescription;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public UserSBTbl getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(UserSBTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}



	
}
