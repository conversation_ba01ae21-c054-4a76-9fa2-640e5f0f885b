package com.radiant.urfs.controller;
//
//import com.paypal.api.payments.Links;
//import com.paypal.api.payments.Payment;
//import com.paypal.base.rest.PayPalRESTException;
//import com.radiant.urfs.service.PayPalService;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.Map;
//
//@RestController
//@RequestMapping("/paypal")
//public class PayPalController {
//
//    @Autowired
//    private PayPalService payPalService;
//
//    private static final String SUCCESS_URL = "http://localhost:8080/paypal/success";
//    private static final String CANCEL_URL = "http://localhost:8080/paypal/cancel";
//
//    @PostMapping("/pay")
//    public String payment(@RequestParam("total") double total) {
//        try {
//            Payment payment = payPalService.createPayment(
//                    total, 
//                    "USD", 
//                    "paypal", 
//                    "sale", 
//                    "Payment description", 
//                    CANCEL_URL, 
//                    SUCCESS_URL);
//            for (Links link : payment.getLinks()) {
//                if (link.getRel().equals("approval_url")) {
//                    return "redirect:" + link.getHref();
//                }
//            }
//        } catch (PayPalRESTException e) {
//            e.printStackTrace();
//        }
//        return "redirect:/";
//    }
//
//    @GetMapping("/success")
//    public String successPayment(@RequestParam("paymentId") String paymentId, 
//                                 @RequestParam("PayerID") String payerId) {
//        try {
//            Payment payment = payPalService.executePayment(paymentId, payerId);
//            if (payment.getState().equals("approved")) {
//                return "Payment successful!";
//            }
//        } catch (PayPalRESTException e) {
//            e.printStackTrace();
//        }
//        return "Payment failed.";
//    }
//
//    @GetMapping("/cancel")
//    public String cancelPayment() {
//        return "Payment cancelled.";
//    }
//}
//
//
//

import com.paypal.api.payments.Links;
import com.paypal.api.payments.Payment;
import com.paypal.base.rest.PayPalRESTException;
import com.radiant.urfs.common.UrlConstants;
import com.radiant.urfs.service.PayPalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Value;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping(value = UrlConstants.User.API_BASE)
public class PayPalController {

    @Autowired
    private PayPalService payPalService;
//
//    @Value("${paypal.final_redirect_url}")
//    private String finalRedirectUrl; 
////    
//    @Value("${paypal.cancel_url}")
//    private String cancelUrl; 
    
    private String cancelUrl = "http://localhost:8080/paypal/cancel";
   private String finalRedirectUrl = "https://www.sandbox.paypal.com/ncp/payment/ZTATDLLSV66NW/6D6278082H603383S";
   
   
   
//    
    @PostMapping(value = UrlConstants.User.PAY)
    public ResponseEntity<Map<String, String>> payment(@RequestParam("total") double total) {
        Map<String, String> response = new HashMap<>();
        try {
            // Create a payment with the given details
            Payment payment = payPalService.createPayment(
                    total,
                    "USD",
                    "paypal",
                    "sale",
                    "Payment description",
                    cancelUrl,
                    finalRedirectUrl
            );
            // Loop through the links to find the approval URL
            for (Links link : payment.getLinks()) {
                if (link.getRel().equals("approval_url")) {
                    response.put("approval_url", link.getHref()); 
                    return new ResponseEntity<>(response, HttpStatus.OK);
                }
            }
        } catch (PayPalRESTException e) {
            e.printStackTrace();
            response.put("error", "Payment creation failed. Please try again.");
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR); // Error in JSON format
        }
        response.put("error", "Approval URL not found.");
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST); // Fallback error if URL is missing
    }

//    @GetMapping("/success")
//    public String successPayment(@RequestParam("paymentId") String paymentId,
//                                 @RequestParam("PayerID") String payerId) {
//        try {
//            Payment payment = payPalService.executePayment(paymentId, payerId);
//            if (payment.getState().equals("approved")) {
//                return "redirect:" + FINAL_REDIRECT_URL; // Redirect to final URL on success
//            }
//        } catch (PayPalRESTException e) {
//            e.printStackTrace();
//        }
//        return "Payment failed."; // Payment failure message
//    }
//
    @GetMapping("/cancel")
    public String cancelPayment() {
        return "Payment cancelled."; // Cancel message
    }
}


