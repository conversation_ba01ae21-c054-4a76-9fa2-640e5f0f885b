package com.radiant.urfs.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "PAYMENTS_TBL")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentsTbl {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PAYMENTS_ID")
    private Integer paymentsId;

    @ManyToOne
    @JoinColumn(name = "PAYMENT_STATUS_ID", nullable = false)
    private PaymentStatusTbl paymentStatus;

    @ManyToOne
    @JoinColumn(name = "PRODUCT_PRICING_ID", nullable = false)
    private ProductPricingTbl productPricing;

    @Column(name = "INVOICE_NUMBER", nullable = false, length = 17, unique = true)
    private String invoiceNumber;

    @Column(name = "INVOICE_DATETIME", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date invoiceDatetime;

    @Column(name = "INVOICE_BATCH_NUMBER", nullable = false, length = 15)
    private String invoiceBatchNumber;

    @ManyToOne
    @JoinColumn(name = "ENTITY_ID", nullable = false)
    private EntityTbl entity;

    @Column(name = "LAST_MODIFIED_DATETIME", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastModifiedDatetime;

    @ManyToOne
    @JoinColumn(name = "LAST_MODIFIED_BY", nullable = false)
    private UserSBTbl lastModifiedByUser;

	public Integer getPaymentsId() {
		return paymentsId;
	}

	public void setPaymentsId(Integer paymentsId) {
		this.paymentsId = paymentsId;
	}

	public PaymentStatusTbl getPaymentStatus() {
		return paymentStatus;
	}

	public void setPaymentStatus(PaymentStatusTbl paymentStatus) {
		this.paymentStatus = paymentStatus;
	}

	public ProductPricingTbl getProductPricing() {
		return productPricing;
	}

	public void setProductPricing(ProductPricingTbl productPricing) {
		this.productPricing = productPricing;
	}

	public String getInvoiceNumber() {
		return invoiceNumber;
	}

	public void setInvoiceNumber(String invoiceNumber) {
		this.invoiceNumber = invoiceNumber;
	}

	public Date getInvoiceDatetime() {
		return invoiceDatetime;
	}

	public void setInvoiceDatetime(Date invoiceDatetime) {
		this.invoiceDatetime = invoiceDatetime;
	}

	public String getInvoiceBatchNumber() {
		return invoiceBatchNumber;
	}

	public void setInvoiceBatchNumber(String invoiceBatchNumber) {
		this.invoiceBatchNumber = invoiceBatchNumber;
	}

	public EntityTbl getEntity() {
		return entity;
	}

	public void setEntity(EntityTbl entity) {
		this.entity = entity;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public UserSBTbl getLastModifiedByUser() {
		return lastModifiedByUser;
	}

	public void setLastModifiedByUser(UserSBTbl lastModifiedByUser) {
		this.lastModifiedByUser = lastModifiedByUser;
	}

    
}