
package com.radiant.urfs.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;
import org.apache.commons.io.FilenameUtils;
import java.util.HashMap;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import com.radiant.urfs.common.Constants;
import com.radiant.urfs.dao.UserDao;
import com.radiant.urfs.domain.ActivitiResponse;
import com.radiant.urfs.domain.DocumentTbl;
import com.radiant.urfs.domain.UserLoginTbl;
import com.radiant.urfs.domain.UserSBLoginTbl;
import com.radiant.urfs.exception.BviReturnsException;
import com.radiant.urfs.repository.UserSBLoginRepository;
import com.radiant.urfs.service.BusinessRegistryService;
import com.radiant.urfs.service.UserService;
import com.radiant.urfs.util.PasswordEncy;
import com.radiant.urfs.web.model.SBDetails;
import com.radiant.urfs.web.model.WebMessage;
import com.radiant.urfs.web.model.WebPassword;
import com.radiant.urfs.web.model.WebUser;
import com.radiant.urfs.web.model.WebUserData;

@Service
public class UserServiceImpl implements UserService {

	private static final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);
	PasswordEncy passwordEncryptor = PasswordEncy.getInstance();

	String logTag = "UserServiceImpl: ";

	@Autowired
	UserDao userDao;

	@Autowired
	private PasswordEncoder passwordEncoder;

	@Autowired
	private UserSBLoginRepository userSBRepository;
	

    @Autowired
    private BusinessRegistryService businessRegistryService;

//	public Optional<UserSBLoginTbl> authenticate(String username, String password) {
//		
//		Optional<UserSBLoginTbl> user = userSBRepository.findByUsername(username);
//		if (user.isPresent()) {
//			return user;
//		}
//
//		 return Optional.empty();
//	}

	public Optional<UserSBLoginTbl> authenticate(String username, String password) {

		Optional<UserSBLoginTbl> user = userSBRepository.findByUsername(username);
		if (user.isPresent()) {
			return user;
		}

		return Optional.empty();
	}

	@Override
	public boolean checkUsername(String userName) throws BviReturnsException {
		boolean userNmaeExist = false;
		try {
			userNmaeExist = userDao.checkUsername(userName);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred in checkUsername(): userName : " + userName + " " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
		return userNmaeExist;
	}

	@Override
	public ResponseEntity<?> changePassword(WebPassword webPassword) throws BviReturnsException {
		logTag = "changePassword()";
		log.info("Entering into" + logTag + "with user details.");
		ActivitiResponse response = new ActivitiResponse();
		try {
			if (webPassword.getOldPassword().equals(webPassword.getNewPassword())) {
				response.setStatus("failure");
				response.setMessage(Constants.User.INVALID_PASSWORD);
				return ResponseEntity.ok(response);
			}
			webPassword.setOldPassword(passwordEncoder.encode(webPassword.getOldPassword()));
			webPassword.setNewPassword(passwordEncoder.encode(webPassword.getNewPassword()));

			return userDao.changePassword(webPassword);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while changing password. Email:" + webPassword.getEmail()
					+ " " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> forgotPassword(String userName, String emailAddress) throws BviReturnsException {
		logTag = "forgotPassword()";
		log.info("Entering into " + logTag);

		try {

			return userDao.forgotPassword(userName, emailAddress);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occured while forgot password. " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	/*
	 * @Override public List<String> checkCompanyNumber(WebUserRegistrationInfo
	 * webUserRegistrationInfo) throws BviReturnsException {
	 * log.info("Entering into UserServiceImpl : checkCompanyNumber() : Start ");
	 * try { return userDao.checkCompanyNumber(webUserRegistrationInfo); } catch
	 * (Exception e) { String errMessage = logTag +
	 * "Exception occurred while checkCompanyNumber" + e; log.error(errMessage, e);
	 * throw new BviReturnsException(errMessage, e); }
	 * 
	 * }
	 */

	@Override
	public ResponseEntity<?> validateOldPassword(WebUserData webUserData) throws BviReturnsException {
		log.info("Entering into UserServiceImpl : validateOldPassword() : Start ");
		boolean oldPassword = false;
		try {
			oldPassword = userDao.validateOldPassword(webUserData);

			if (oldPassword) {
				return new ResponseEntity<>(new WebMessage(Constants.User.OLD_PASSWORD_MATCH, Constants.SUCCESS),
						HttpStatus.OK);
			} else {
				return new ResponseEntity<>(new WebMessage(Constants.User.OLD_PASSWORD_NOT_MATCH, Constants.FAIL),
						HttpStatus.OK);
			}
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while validateOldPassword: userName: "
					+ webUserData.getUserName() + " " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

//	@Override
//	public ResponseEntity<?> addAmlcftnewLicensee(WebUserRegistrationInfo webUserRegistrationInfo)
//			throws BviReturnsException {
//		log.info("Entering into UserServiceImpl : addAmlcftnewLicensee() : Start ");
//		try {
//			return userDao.addAmlcftnewLicensee(webUserRegistrationInfo);
//
//		} catch (Exception e) {
//			String errMessage = logTag + "Exception occurred while addAmlcftnewLicensee" + e;
//			log.error(errMessage, e);
//			throw new BviReturnsException(errMessage, e);
//		}
//	}

	@Override
	public ResponseEntity<?> resetPassword(String token, String newPassword) throws BviReturnsException {
		log.info("Entering into UserServiceImpl : resetPassword() : Start ");
		try {
			return userDao.resetPassword(token, newPassword);

		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while resetPassword" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

//	@Override
//	public ResponseEntity<?> unlinkAmlcftLicensee(List<WebUserRegistrationInfo> webUserRegistrationInfo,
//			Integer loginUserId) throws BviReturnsException {
//		log.info("Entering into UserServiceImpl : unlinkAmlcftLicensee() : Start ");
//		try {
//			return userDao.unlinkAmlcftLicensee(webUserRegistrationInfo, loginUserId);
//
//		} catch (Exception e) {
//			String errMessage = logTag + "Exception occurred while unlinkAmlcftLicensee" + e;
//			log.error(errMessage, e);
//			throw new BviReturnsException(errMessage, e);
//		}
//	}

//	@Override
//	public ResponseEntity<?> linkAmlcftLicensee(WebUserRegistrationInfo webUserRegistrationInfo) throws BviReturnsException {
//		log.info("Entering into UserServiceImpl : linkAmlcftLicensee() : Start ");
//		try {
//			return userDao.linkAmlcftLicensee(webUserRegistrationInfo);
//
//		} catch (Exception e) {
//			String errMessage = logTag + "Exception occurred while linkAmlcftLicensee" + e;
//			log.error(errMessage, e);
//			throw new BviReturnsException(errMessage, e);
//		}
//	}

//	@Override
//	public ResponseEntity<?> bulkRegistrationFromExcel(InputStream targetStream, Integer loginUserId)
//			throws BviReturnsException, IOException {
//		List<WebLicenceDetailsInfo> licenseeList = new ArrayList<WebLicenceDetailsInfo>();
//		ResponseEntity reponse;
//		try {
//			Workbook workbook = WorkbookFactory.create(targetStream);
//			Sheet sheet = workbook.getSheetAt(0);
//			int firstRowNum = sheet.getFirstRowNum();
//			int lastRowNum = sheet.getLastRowNum();
//			for (int i = firstRowNum + 1; i < lastRowNum + 1; i++) {
//				Row row = sheet.getRow(i);
//				if (row != null && !isRowEmpty(row)) {
//					// int firstCellNum = row.getFirstCellNum();
//					// int lastCellNum = row.getLastCellNum();
//
//					WebLicenceDetailsInfo webLicenceDetailsInfo = new WebLicenceDetailsInfo();
//					for (int j = 0; j < 7; j++) {
//						Cell cell = row.getCell(j);
//						if (cell != null) {
//							String cellValue = null;
//							FormulaEvaluator formulaEvaluator = workbook.getCreationHelper().createFormulaEvaluator();
//							switch (formulaEvaluator.evaluateInCell(cell).getCellType()) {
//							case NUMERIC:
//								int numberValue = (int) cell.getNumericCellValue();
//								cellValue = Integer.toString(numberValue);
//								break;
//							case STRING:
//								cellValue = cell.getStringCellValue();
//								break;
//							}
//							if (j == 0) {
//								webLicenceDetailsInfo.setLicenceName(cellValue);
//							} else if (j == 1) {
//								webLicenceDetailsInfo.setLicenceNumber(cellValue);
//							} else if (j == 2) {
//								webLicenceDetailsInfo.setCompanyNumber(cellValue);
//							} else if (j == 3) {
//								webLicenceDetailsInfo.setRegisteredAgentName(cellValue);
//							} else if (j == 4) {
//								if (cellValue == null) {
//									webLicenceDetailsInfo.setRegisteredOfficeAddress("N/A");
//								} else {
//									webLicenceDetailsInfo.setRegisteredOfficeAddress(cellValue);
//								}
//							}
//
//							else if (j == 5) {
//								if (cellValue == null) {
//									webLicenceDetailsInfo.setLicenceType(10);
//								} else {
//									LicenseTypeTbl licenseTypeTbl = amlCftAnnualReturnDao
//											.getLicenseTypeTblByName(cellValue);
//									if (licenseTypeTbl != null) {
//										log.info(logTag + "LicenseTypeId: " + licenseTypeTbl.getLicenseTypeId());
//										webLicenceDetailsInfo.setLicenceType(licenseTypeTbl.getLicenseTypeId());
//									} else {
//										webLicenceDetailsInfo.setLicenceType(10);
//									}
//								}
//							}
//
//							else if (j == 6) {
//								LicenseeRelationTbl licenseeRelationTbl = amlCftAnnualReturnDao
//										.getLicenseRelationTblByName(cellValue);
//								if (licenseeRelationTbl != null) {
//									log.info(logTag + "LicenseeRelationId: "
//											+ licenseeRelationTbl.getLicenseeRelationId());
//									webLicenceDetailsInfo
//											.setRelationToLicensee(licenseeRelationTbl.getLicenseeRelationId());
//									if (licenseeRelationTbl.getLicenseeRelationId() == 8) {
//										webLicenceDetailsInfo.setOtherDescription(Constants.NA);
//									}
//
//								} else {
//									licenseeRelationTbl = amlCftAnnualReturnDao
//											.getLicenseRelationTblByName(Constants.NA);
//									webLicenceDetailsInfo
//											.setRelationToLicensee(licenseeRelationTbl.getLicenseeRelationId());
//								}
//							}
//						}
//					}
//					licenseeList.add(webLicenceDetailsInfo);
//				}
//			}
//			WebUserRegistrationInfo webUserRegistrationInfo = new WebUserRegistrationInfo();
//			webUserRegistrationInfo.setWebLicenceDetailsList(licenseeList);
//			reponse = userDao.bulkRegistrationFromExcel(webUserRegistrationInfo, loginUserId);
//		} catch (Exception e) {
//			String errMessage = logTag + "Exception occurred while checkLicenceNumber" + e;
//			log.error(errMessage, e);
//			throw new BviReturnsException(errMessage, e);
//		}
//		return reponse;
//	}

	public static boolean isRowEmpty(Row row) {
		for (int c = row.getFirstCellNum(); c < 5; c++) {
			Cell cell = row.getCell(c);
			if (cell != null && cell.getCellType() != CellType.BLANK)
				return false;
		}
		return true;
	}

//	@Override
//	public ResponseEntity<?> saveNewAdminRegistration(WebUserRegistrationInfo webUserRegistrationInfo,
//			Integer loginUserId) throws BviReturnsException {
//		Properties props = new Properties();
//		ActivitiResponse activitiResponse = new ActivitiResponse();
//		ResponseEntity reponse;
//		try {
//			reponse = userDao.saveNewAdminRegistration(webUserRegistrationInfo, loginUserId);
//
//			WebMessage webMessage = (WebMessage) reponse.getBody();
//			if (webMessage.getStatus().equalsIgnoreCase("SUCCESS")) {
//				//props=ResourceUtil.getEnvSpecificProperties();
//
//				EmailDetails emailDetails = new EmailDetails();
//				String userMailId = null;
//
//				if (webUserRegistrationInfo != null) {
//					userMailId = webUserRegistrationInfo.getEmail();
//
//				}
//				String[] recipients = { userMailId };
//				emailDetails.setToRecipients(recipients);
//				emailDetails.setSubject("ACKNOWLEDGEMENT OF APPLICATION FOR ADMIN REGISTRATION");
//				emailDetails.setMessageContent(
//						"Thank you for your application to be a registered admin of VIRRGIN Returns, the BVI Financial Services Commission’s returns "
//								+ "filing system.Your Password is : " + webMessage.getMessage());
//
//				Runnable myrunnable = new Runnable() {
//					public void run() {
//						EmailManager emailManager = EmailManager.getInstance();
//						try {
//							emailManager.send(emailDetails);
//						} catch (BviReturnsException e) {
//							e.printStackTrace();
//						}
//					}
//				};
//				new Thread(myrunnable).start();
//				activitiResponse.setStatus("SUCCESS");
//				activitiResponse.setMessage(Constants.User.REGISTRATION_SUCCESS);
//				return ResponseEntity.ok(activitiResponse);
//			} else {
//				return reponse;
//			}
//		} catch (Exception e) {
//			String errMessage = logTag + "Exception occurred in saveUserRegistration(): email : "
//					+ webUserRegistrationInfo.getEmail() + e;
//			log.error(errMessage, e);
//			throw new BviReturnsException(errMessage, e);
//		}
//	}

	@Override
	public ResponseEntity<?> getDetailsByUserName(String userName) throws BviReturnsException {
		log.info("Entering into " + logTag + " getDetailsByUserName() Start ");
		try {
			return userDao.getDetailsByUserName(userName);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while getDetailsByUserName() :" + userName + " " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

//	public ResponseEntity<?> updateDetails(WebUserRegistrationInfo webUserRegistrationInfo, String username)
//			throws BviReturnsException {
//		String logTag = "updateDetails(): ";
//		log.info("Entering into " + logTag);
//		try {
//			return userDao.updateDetails(webUserRegistrationInfo, username);
//		} catch (Exception e) {
//			String errMessage = logTag + "Exception occurred while getting getDetailsByUsername() :" + e;
//			log.error(errMessage, e);
//			throw new BviReturnsException(errMessage, e);
//
//		}
//
//	}

	@Override
	public ResponseEntity<?> getAllUsers() throws BviReturnsException {
		log.info("Entering into UserServiceImpl : getAllUsers() : Start ");
		try {
			return userDao.getAllUsers();
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while getting getAllUsers() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	// =========================================================
	String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789~`!@#$%^&*/?";

//	@Override
//	public ResponseEntity<?> registerUser(WebUser userSignupTbl) throws BviReturnsException {
//		logTag = "userRegistration()";
//		log.info("Entering into  userRegistration ServiceImpl" + logTag);
//		// ActivitiResponse activitiResponse = new ActivitiResponse();
//		ActivitiResponse activitiResponse = null;
//		// ValidationResponse validationResponse = null;
//		Integer userId = null;
//		String newPassword = RandomStringUtils.random(7, characters);
//		try {
//
//			/*
//			 * validationResponse = ValidationUtils.validatePassword(userSignupTbl); if
//			 * (!validationResponse.isSuccess()) { return new
//			 * ResponseEntity<>(validationResponse.getErrorMessage(),
//			 * HttpStatus.BAD_REQUEST); } validationResponse =
//			 * ValidationUtils.validateUser(userSignupTbl); if
//			 * (!validationResponse.isSuccess()) { return new
//			 * ResponseEntity<>(validationResponse.getErrorMessage(),
//			 * HttpStatus.BAD_REQUEST); }
//			 */
//			// verifying user name exists in database
//			// UserLoginTbl userLoginTbl =
//			// userDao.userRegistration(userSignupTbl.getUsername());
//
//			UserLoginTbl userLoginTbl = userDao.verifyUserName(userSignupTbl.getUsername());
//
//			// UserLoginTbl userLoginTbl = new UserLoginTbl();
//
//			if (userLoginTbl != null && userLoginTbl.getActiveFlag() == Constants.ACTIVE) {
//				return new ResponseEntity<>(new WebMessage(userSignupTbl.getUserId(), Constants.User.USER_NAME_EXISTS),
//						HttpStatus.OK);
//
//			}
//
////		if (userLoginTbl != null)
////			userId = userLoginTbl.getUserTblByUserId().getUserId();
//			// UserLoginTbl userLoginTbl =
//			// userDao.userRegistration(userSignupTbl.getUsername());
//			if (userLoginTbl != null) {
//				if (userLoginTbl.getActiveFlag() == Constants.ACTIVE) {
//					return new ResponseEntity<>(
//							new WebMessage(userSignupTbl.getUserId(), Constants.User.USER_NAME_EXISTS), HttpStatus.OK);
//				}
//				userId = userLoginTbl.getUserTblByUserId().getUserId();
//			}
//
//			userSignupTbl.setPassword(passwordEncryptor.encrypt(newPassword));
//			userSignupTbl.setUserId(userId);
//			return userDao.userRegistration(userSignupTbl);
//		} catch (Exception e) {
//			String errMessage = logTag + "Exception occurred while saving  the userDatailsDetails." + e;
//			log.error(errMessage, e);
//			throw new BviReturnsException(errMessage, e);
//		}
//	}
	
	@Override
    public ResponseEntity<?> registerUser(WebUser userSignupTbl) throws BviReturnsException {
        String logTag = "userRegistration()";
        log.info("Entering into userRegistration ServiceImpl {}", logTag);

        try {
            UserLoginTbl userLoginTbl = userDao.verifyUserName(userSignupTbl.getUsername());

            if (userLoginTbl != null && userLoginTbl.getActiveFlag() == Constants.ACTIVE) {
                return new ResponseEntity<>(
                        new WebMessage(userSignupTbl.getUserId(), Constants.User.USER_NAME_EXISTS),
                        HttpStatus.OK);
            }

            String newPassword = RandomStringUtils.random(7, characters);
            userSignupTbl.setPassword(passwordEncryptor.encrypt(newPassword));

            ResponseEntity<?> response = userDao.userRegistration(userSignupTbl);

            // ✅ Sync with Business Registry after successful registration
            if (response.getStatusCode() == HttpStatus.OK) {
                businessRegistryService.syncCompany(userSignupTbl);
            }

            return response;
        } catch (Exception e) {
            String errMessage = logTag + " Exception occurred while saving user details: " + e;
            log.error(errMessage, e);
            throw new BviReturnsException(errMessage, e);
        }
    }


//	public ResponseEntity<?> addInternalUsers(WebUserRegistrationInfo webUserRegistrationInfo)
//			throws BviReturnsException {
//		String logTag = "addInternalUsers(): ";
//		log.info("Entering into " + logTag);
//		try {
//			return userDao.addInternalUsers(webUserRegistrationInfo);
//		} catch (Exception e) {
//			String errMessage = logTag + "Exception occurred while getting addInternalUsers() :" + e;
//			log.error(errMessage, e);
//			throw new BviReturnsException(errMessage, e);
//
//		}
//	}

	@Override
	public UserLoginTbl verifyUserName(String username) throws BviReturnsException {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ResponseEntity<?> saveNewUserRegistration(SBDetails sbDetails, MultipartFile addndoc)
			throws BviReturnsException {
		Properties props = new Properties();
		ActivitiResponse activitiResponse = new ActivitiResponse();
		ResponseEntity reponse;
		try {
			Map<Integer, DocumentTbl> documentMap = getDocumentsFromFiles(addndoc);
			return userDao.saveNewUserRegistration(sbDetails, documentMap.get(1));

		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred in saveUserRegistration(): email : "
					+ sbDetails.getUserDetailsTbl().getEmailAddress() + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> ammendments(SBDetails sbDetails, MultipartFile addndoc, Integer userId)
			throws BviReturnsException {
		Properties props = new Properties();
		ActivitiResponse activitiResponse = new ActivitiResponse();
		ResponseEntity reponse;
		try {

			Map<Integer, DocumentTbl> documentMap = getDocumentsFromFiles(addndoc);
			return userDao.ammendments(sbDetails, documentMap.get(1), userId);

		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred in ammendments(): email : "
					+ sbDetails.getUserDetailsTbl().getEmailAddress() + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> getPaymentSummaryDetails(String paymentCode) throws BviReturnsException {
		log.info("UserServiceImpl" + "getPaymentSummaryDetails" + "Start");

		return userDao.getPaymentSummaryDetails(paymentCode);
	}

	@Override
	public ResponseEntity<?> getDashboardList(char authInd) throws BviReturnsException {
		log.info("Entering into UserServiceImpl : getDashboardList() : Start ");
		try {
			return userDao.getDashboardList(authInd);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while getting getDashboardList() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}

	}

	@Override
	public ResponseEntity<?> getApprovedDashboardList() throws BviReturnsException {
		log.info("Entering into UserServiceImpl : getApprovedDashboardList() : Start ");
		try {
			return userDao.getApprovedDashboardList();
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while getting getApprovedDashboardList() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}

	}

	@Override
	public ResponseEntity<?> approve(List<Integer> entityTempIds) throws BviReturnsException {
		log.info("Entering into UserServiceImpl : approveUser() : Start ");
		try {
			return userDao.approve(entityTempIds);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while getting approveUser() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}

	}

	@Override
	public ResponseEntity<?> reject(String comments, List<Integer> entityTempIds) throws BviReturnsException {
		log.info("Entering into UserServiceImpl : rejectUser() : Start ");
		try {
			return userDao.reject(comments, entityTempIds);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while getting rejectUser() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}

	}

	@Override
	public Map<Integer, DocumentTbl> getDocumentsFromFiles(MultipartFile addndoc) throws Exception {
		try {
			Map<Integer, DocumentTbl> map = new HashMap();
			DocumentTbl addndocument = new DocumentTbl();

			if (addndoc != null) {
				addndocument.setDocument(addndoc.getBytes());
				addndocument.setDocumentName(addndoc.getOriginalFilename());
				addndocument.setDocumentFileExtn(FilenameUtils.getExtension(addndoc.getOriginalFilename()));
				addndocument.setActiveFlag(Constants.ACTIVE);
				map.put(1, addndocument);
			}
			return map;
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while getting the getDocumentsFromFiles" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> getAmmendmentsDashboardList(Integer userId) throws BviReturnsException {
		log.info("Entering into UserServiceImpl : getAmmendmentsDashboardList() : Start ");
		try {
			return userDao.getAmmendmentsDashboardList(userId);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while getting getAmmendmentsDashboardList() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}

	}

	@Override
	public ResponseEntity<?> getSearchDetails(String sbNumber, String startDate, String endDate, String entityName,
			Integer businessOperationId, String companyHead, String url, String status, Integer companyTypeId,
			String AreasInterests, String BusinessKeywords) throws BviReturnsException {
		log.info("Entering into UserServiceImpl : getAmmendmentsDashboardList() : Start ");
		try {
			return userDao.getSearchDetails(sbNumber, startDate, endDate, entityName, businessOperationId, companyHead,
					url, status, companyTypeId, AreasInterests, BusinessKeywords);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while getting getAmmendmentsDashboardList() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}

	}

	@Override
	public ResponseEntity<?> getBussinessPurposeDetails() throws BviReturnsException {
		log.info("Entering into UserServiceImpl : getBussinessPurposeDetails() : Start ");
		try {
			return userDao.getBussinessPurposeDetails();
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while getting getBussinessPurposeDetails() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> getPartnershipDetails() throws BviReturnsException {
		try {
			return userDao.getPartnershipDetails();
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while getting getPartnershipPurposeDetails() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> getBussinessOperationDetails() throws BviReturnsException {
		try {
			return userDao.getBussinessOperationDetails();
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while  getBussinessOperationDetails() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> getCompanyTypeDetails() throws BviReturnsException {
		try {
			return userDao.getCompanyTypeDetails();
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while  getCompanyTypeDetails() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> getRegistrationDashboardList(String sbrNumber) throws BviReturnsException {
		try {
			return userDao.getRegistrationDashboardList(sbrNumber);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while  getRegistrationDashboardList() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> getTransactionsDetails() throws BviReturnsException {
		try {

			return userDao.getTransactionsDetails();

		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while  getTransactionsDetails() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> sendEmailDetails(String comments,String entityName,String subject) throws BviReturnsException {
		try {

			return userDao.sendEmailDetails(comments,entityName,subject);

		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while  sendEmailDetails() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> enableDisableUser(String activeFlag, Integer userId, String entityName)
			throws BviReturnsException {
		try {

			return userDao.enableDisableUser(activeFlag, userId, entityName);

		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while  sendEmailDetails() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> activeInactiveUsers() throws BviReturnsException {
		try {

			return userDao.activeInactiveUsers();

		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while  activeInactiveUsers() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> getTransactionsDetailsByUser(Integer lastModifiedBy) throws BviReturnsException {
		try {

			return userDao.getTransactionsDetailsByUser(lastModifiedBy);

		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while  TransactionsDetailsByUser() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> registerNewUser(SBDetails sbDetails) throws BviReturnsException {
		try {

			return userDao.registerNewUser(sbDetails);

		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred in registerNewUser(): email : "
					+ sbDetails.getUserDetailsTbl().getEmailAddress() + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> updateUser(SBDetails sbDetails, Integer userId) throws BviReturnsException {
		try {

			return userDao.updateUser(sbDetails, userId);

		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred in updateUser(): email : "
					+ sbDetails.getUserDetailsTbl().getEmailAddress() + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> getRegistrationPendingList(String sbrNumber) throws BviReturnsException {
		try {

			return userDao.getRegistrationPendingList(sbrNumber);

		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while  getRegistrationDashboardList() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> getApprovedRegisterList() throws BviReturnsException {
		log.info("Entering into UserServiceImpl : getApprovedRegisterList() : Start ");
		try {
			return userDao.getApprovedRegisterList();
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while getting getApprovedRegisterList() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@Override
	public ResponseEntity<?> getDetailsByUser(Integer lastModifiedBy) throws BviReturnsException {
		log.info("Entering into UserServiceImpl : getDetailsByUser() : Start ");
		try {
			return userDao.getDetailsByUser(lastModifiedBy);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while  getDetailsByUser() :" + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

}
