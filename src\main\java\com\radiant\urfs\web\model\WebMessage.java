package com.radiant.urfs.web.model;

public class WebMessage {
	private Integer id;
	private String message;
	private String status;
	
	
	public WebMessage(Integer id, String message,String status) {
		super();
		this.id = id;
		this.message = message;
		this.status= status;
	}

	public WebMessage(String message, String status) {
		super();
		this.message = message;
		this.status = status;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	
	public WebMessage() {
	}
	
	public WebMessage(Integer id, String message) {
		super();
		this.id = id;
		this.message = message;
	}

	public WebMessage(String message) {
		this.message = message;
	}

	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}

}
