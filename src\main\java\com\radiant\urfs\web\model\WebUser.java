package com.radiant.urfs.web.model;

public class WebUser {
	private Integer userId;
	//login user details
	//private WebUserRole role;
	//private WebUserPermission permission;
	private String username;
	private String password;
	private String firstName;
	private String lastName;	
	private String email;
	private String entityName;
	
	public Integer getUserId() {
		return userId;
	}
	public void setUserId(Integer userId) {
		this.userId = userId;
	}
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	public String getLastName() {
		return lastName;
	}
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

//	public WebUserRole getRole() {
//		return role;
//	}
//	public void setRole(WebUserRole role) {
//		this.role = role;
//	}
//	public WebUserPermission getPermission() {
//		return permission;
//	}
//	public void setPermission(WebUserPermission permission) {
//		this.permission = permission;
//	}
	public String getEmail() {
		return email;
	}
	public String getEntityName() {
		return entityName;
	}
	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}
	public void setEmail(String email) {
		this.email = email;
	}
}



