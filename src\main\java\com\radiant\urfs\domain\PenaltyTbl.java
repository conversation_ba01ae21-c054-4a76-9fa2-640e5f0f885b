package com.radiant.urfs.domain;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "PENALTY_TBL", schema = "AML_CFT")
public class PenaltyTbl {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "PENALTY_ID", unique = true, nullable = false)
	private int penaltyId;

	@Column(name = "LICENSE_NUMBER", nullable = false)
	private String licenseNumber;

	@Column(name = "COMPANY_NUMBER", nullable = false)
	private String companyNumber;

	@Column(name = "PENALTY_AMOUNT")
	private Integer penaltyAmount;

	@Column(name = "REPORTING_PERIOD")
	private Integer reportingPeriod;

	@Column(name = "PERSON_COMPLETING_FORM_EMAIL", nullable = false)
	private String personCompletingFormEmail;

	@Column(name = "FORM_COMPLETING_DATE")
	private String formCompletingDate;

	@Column(name = "PENALTY_TYPE_IND", nullable = false)
	private String penaltyTypeInd;


	@Column(name = "ACTIVE_FLAG", nullable = false)
	private char activeFlag;

	@Column(name = "LAST_MODIFIED_DATETIME", nullable = false)
	private Date lastModifiedDatetime;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LAST_MODIFIED_BY", nullable = false)
	private UserTbl userTblByLastModifiedBy;
	
	@Column(name = "LICENSEE_NAME", nullable = false)
	private String licenseName;
	
	public PenaltyTbl() {
	}

	public int getPenaltyId() {
		return penaltyId;
	}

	public void setPenaltyId(int penaltyId) {
		this.penaltyId = penaltyId;
	}

	public String getLicenseNumber() {
		return licenseNumber;
	}

	public void setLicenseNumber(String licenseNumber) {
		this.licenseNumber = licenseNumber;
	}

	public String getCompanyNumber() {
		return companyNumber;
	}

	public void setCompanyNumber(String companyNumber) {
		this.companyNumber = companyNumber;
	}

	public Integer getPenaltyAmount() {
		return penaltyAmount;
	}

	public void setPenaltyAmount(Integer penaltyAmount) {
		this.penaltyAmount = penaltyAmount;
	}

	public Integer getReportingPeriod() {
		return reportingPeriod;
	}

	public void setReportingPeriod(Integer reportingPeriod) {
		this.reportingPeriod = reportingPeriod;
	}

	public String getPersonCompletingFormEmail() {
		return personCompletingFormEmail;
	}

	public void setPersonCompletingFormEmail(String personCompletingFormEmail) {
		this.personCompletingFormEmail = personCompletingFormEmail;
	}

	public String getFormCompletingDate() {
		return formCompletingDate;
	}

	public void setFormCompletingDate(String formCompletingDate) {
		this.formCompletingDate = formCompletingDate;
	}

	public String getPenaltyTypeInd() {
		return penaltyTypeInd;
	}

	public void setPenaltyTypeInd(String penaltyTypeInd) {
		this.penaltyTypeInd = penaltyTypeInd;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public UserTbl getUserTblByLastModifiedBy() {
		return userTblByLastModifiedBy;
	}

	public void setUserTblByLastModifiedBy(UserTbl userTblByLastModifiedBy) {
		this.userTblByLastModifiedBy = userTblByLastModifiedBy;
	}

	public String getLicenseName() {
		return licenseName;
	}

	public void setLicenseName(String licenseName) {
		this.licenseName = licenseName;
	}
	
	
}
