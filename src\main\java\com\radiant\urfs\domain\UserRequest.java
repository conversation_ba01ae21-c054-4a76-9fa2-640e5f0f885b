package com.radiant.urfs.domain;

import java.io.Serializable;
import java.util.List;
import com.radiant.urfs.web.model.WebRegRejectCompanyDetails;

public class UserRequest implements Serializable {

	/**
	 * 
	 */
	private static long serialVersionUID = 1L;
	private String taskId;
	private Boolean approve;
	private String executionId;
	private String procInstId;
	private String userComments;
	private String userType;
	private String userId;
	private List<WebRegRejectCompanyDetails> rejectedCompanyDetails;

	public UserRequest() {
		super();
	}

	public UserRequest(String taskId, Boolean approve) {
		super();
		this.taskId = taskId;
		this.approve = approve;

	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public Boolean getApprove() {
		return approve;
	}

	public void setApprove(Boolean approve) {
		this.approve = approve;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getExecutionId() {
		return executionId;
	}

	public String getProcInstId() {
		return procInstId;
	}

	public String getUserComments() {
		return userComments;
	}

	public String getUserType() {
		return userType;
	}

	public static void setSerialversionuid(long serialversionuid) {
		serialVersionUID = serialversionuid;
	}

	public void setExecutionId(String executionId) {
		this.executionId = executionId;
	}

	public void setProcInstId(String procInstId) {
		this.procInstId = procInstId;
	}

	public void setUserComments(String userComments) {
		this.userComments = userComments;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}
	
	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	

	public List<WebRegRejectCompanyDetails> getRejectedCompanyDetails() {
		return rejectedCompanyDetails;
	}

	public void setRejectedCompanyDetails(List<WebRegRejectCompanyDetails> rejectedCompanyDetails) {
		this.rejectedCompanyDetails = rejectedCompanyDetails;
	}

	@Override
	public String toString() {
		return "UserRequest [taskId=" + taskId + ", approve=" + approve + ", executionId=" + executionId
				+ ", procInstId=" + procInstId + ", userComments=" + userComments + ", userType=" + userType + "]";
	}

}
