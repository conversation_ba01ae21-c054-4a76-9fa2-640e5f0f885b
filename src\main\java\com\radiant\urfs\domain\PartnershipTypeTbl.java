package com.radiant.urfs.domain;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "PARTNERSHIP_TYPE_TBL")
public class PartnershipTypeTbl implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PARTNERSHIP_TYPE_ID", nullable = false)
    private int partnershipTypeId;

    @Column(name = "PARTNERSHIP_TYPE", length = 600, nullable = false)
    private String partnershipType;

    @Column(name = "PARTNERSHIP_TYPE_DESC", length = 600, nullable = true)
    private String partnershipTypeDesc;  // Corrected to match SQL definition

    @Column(name = "ACTIVE_FLAG", length = 1, nullable = false)
    private char activeFlag;
    
  
    @Column(name = "LAST_MODIFIED_DATETIME", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastModifiedDatetime;

    
    @ManyToOne
    @JoinColumn(name = "LAST_MODIFIED_BY", referencedColumnName = "USER_ID", nullable = false)
    private UserSBTbl lastModifiedBy;


	public int getPartnershipTypeId() {
		return partnershipTypeId;
	}


	public void setPartnershipTypeId(int partnershipTypeId) {
		this.partnershipTypeId = partnershipTypeId;
	}


	public String getPartnershipType() {
		return partnershipType;
	}


	public void setPartnershipType(String partnershipType) {
		this.partnershipType = partnershipType;
	}


	public String getPartnershipTypeDesc() {
		return partnershipTypeDesc;
	}


	public void setPartnershipTypeDesc(String partnershipTypeDesc) {
		this.partnershipTypeDesc = partnershipTypeDesc;
	}


	public char getActiveFlag() {
		return activeFlag;
	}


	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}


	


	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}


	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}


	public UserSBTbl getLastModifiedBy() {
		return lastModifiedBy;
	}


	public void setLastModifiedBy(UserSBTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}

    
 
}
