package com.radiant.urfs.domain;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "EXTENSION_DAYS_TBL", catalog = "dbo")
public class ExtensionDaysTbl {

	private int extensionDaysId;
	private int numExtensionDays;
	private char activeFlag;
	private Date lastModifiedDatetime;
	private UserTbl userTblByLastModifiedBy;
	
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "EXTENSION_DAYS_ID", unique = true, nullable = true)
	public int getExtensionDaysId() {
		return extensionDaysId;
	}
	public void setExtensionDaysId(int extensionDaysId) {
		this.extensionDaysId = extensionDaysId;
	}
	@Column(name = "NUM_EXTENSION_DAYS")
	public int getNumExtensionDays() {
		return numExtensionDays;
	}
	public void setNumExtensionDays(int numExtensionDays) {
		this.numExtensionDays = numExtensionDays;
	}
	
	@Column(name = "ACTIVE_FLAG", nullable = false, length = 1)
	public char getActiveFlag() {
		return this.activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_MODIFIED_DATETIME", nullable = false, length = 23)
	public Date getLastModifiedDatetime() {
		return this.lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LAST_MODIFIED_BY")
	public UserTbl getUserTblByLastModifiedBy() {
		return userTblByLastModifiedBy;
	}

	public void setUserTblByLastModifiedBy(UserTbl userTblByLastModifiedBy) {
		this.userTblByLastModifiedBy = userTblByLastModifiedBy;
	}

}
