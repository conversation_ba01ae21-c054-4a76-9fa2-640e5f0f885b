/*
 *<AUTHOR> 
 */
package com.radiant.urfs.util;

import java.security.spec.AlgorithmParameterSpec;
import java.security.spec.KeySpec;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.PBEParameterSpec;

import org.apache.log4j.Logger;

public class PasswordEncy {

	Logger log = Logger.getLogger(PasswordEncy.class);
	// private static final String passPhrase = "CheckMETest1234!@#$";
	private static final String passPhrase = "JPSDSEJBLBSY@3256!@#$";
	Cipher ecipher;
	Cipher dcipher;
	private static PasswordEncy instance = null;

	// 8-byte Salt
	byte[] salt = { (byte) 0xA9, (byte) 0x9B, (byte) 0xC8, (byte) 0x32, (byte) 0x56, (byte) 0x35, (byte) 0xE3,
			(byte) 0x03 };

	// Iteration count
	int iterationCount = 19;

	private PasswordEncy() {
		try {
			// Create the key
			KeySpec keySpec = new PBEKeySpec(passPhrase.toCharArray(), salt, iterationCount);
			// KeySpec keySpec = new PBEKeySpec(passPhrase.toCharArray());
			SecretKey key = SecretKeyFactory.getInstance("PBEWithMD5AndDES").generateSecret(keySpec);
			ecipher = Cipher.getInstance(key.getAlgorithm());
			dcipher = Cipher.getInstance(key.getAlgorithm());

			// Prepare the parameter to the ciphers
			AlgorithmParameterSpec paramSpec = new PBEParameterSpec(salt, iterationCount);

			// Create the ciphers
			ecipher.init(Cipher.ENCRYPT_MODE, key, paramSpec);
			dcipher.init(Cipher.DECRYPT_MODE, key, paramSpec);
		} catch (Exception e) {
			log.error("Error creating PasswordEncry:", e);
		}
	}

	public String encrypt(String plainText) {
		try {
			// Encrypt
			Base64.Encoder encoder = Base64.getEncoder();
			
			return encoder.encodeToString(plainText.getBytes());
		} catch (Exception e) {
			log.error("Error encrypt password:", e);
		}
		return null;
	}

	public String decrypt(String encText) {
		String dStr = null;
		try {
			Base64.Decoder decoder = Base64.getDecoder();
			dStr = new String(decoder.decode(encText));  
		} catch (Exception e) {
			log.error("Error decrypt password:", e);
		}
		 return dStr;
		
	}

	public static synchronized PasswordEncy getInstance() {

		if (instance == null) {
			instance = new PasswordEncy();
		}
		return instance;
	}
	
	
	

	
	/*
	 * public static void main(String args[]) {
	 * System.out.println(PasswordEncy.getInstance().encrypt("Bviadmin!123"));
	 * System.out.println(PasswordEncy.getInstance().decrypt("QnZpYWRtaW4hMTIz")); }
	 */
	 
	
	
//	 public static void main(String[] args) {
//	  //SpringApplication.run(SpringBootSecurityPasswordEncoderApplication.class,
//	 // args);
//	 
//	 BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder(); String
//	 password = "prasanth@123"; String encodedPassword =
//	 passwordEncoder.encode(password); System.out.println();
//	  System.out.println("Password is         : " + password);
//	  System.out.println("Encoded Password is : " + encodedPassword);
//	  System.out.println();
//	  
//	  boolean isPasswordMatch = passwordEncoder.matches(password, encodedPassword);
//	  System.out.println("Password : " + password + "   isPasswordMatch    : " +
//	  isPasswordMatch);
//	  
//	  password = "prasanth@123"; isPasswordMatch = passwordEncoder.matches(password,
//	  encodedPassword); System.out.println("Password : " + password +
//	  "           isPasswordMatch    : " + isPasswordMatch); }
	 
	
	 
}