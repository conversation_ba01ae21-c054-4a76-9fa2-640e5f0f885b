/**
* <AUTHOR> EU231
* <AUTHOR> E-mail:<EMAIL>
* @version Creation time: May 13, 2021 7:16:00 PM
* Class Description: UserLicenceDetailsTbl.java
*/
package com.radiant.urfs.domain;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity

@Table(name = "USER_LICENCE_DETAILS_TBL", catalog = "dbo")
public class UserLicenceDetailsTbl {

	private int userLicenceDetailsId;
	private UserTbl userTbl;	
	private String licenceName;
	
	private String licenceNumber;
	private String registeredAgentName;
	private String companyNumber;
	private LicenseeRelationTbl licenseeRelationId;
	private char activeFlag;
	private Date lastModifiedDatetime;
	private UserTbl lastModifiedBy;
	private String comments;
	private Character delink;
	private String registeredOfficeAddress;
	private String otherDescription;

//	private String relationToLicenseeName;
	
	public UserLicenceDetailsTbl() {
		
	}
	public UserLicenceDetailsTbl(int userLicenceDetailsId, UserTbl userTbl, String licenceName,
			String licenceNumber, String registeredAgentName, String companyNumber, LicenseeRelationTbl licenseeRelationId,
			char activeFlag, Date lastModifiedDatetime, UserTbl lastModifiedBy, String comments,String registeredOfficeAddress, String otherDescription ) {
		super();
		this.userLicenceDetailsId = userLicenceDetailsId;
		this.userTbl = userTbl;
		this.licenceName = licenceName;
		this.licenceNumber = licenceNumber;
		this.registeredAgentName = registeredAgentName;
		this.companyNumber = companyNumber;
		this.licenseeRelationId = licenseeRelationId;
		this.activeFlag = activeFlag;
		this.lastModifiedDatetime = lastModifiedDatetime;
		this.lastModifiedBy = lastModifiedBy;
		this.comments = comments;
		this.registeredOfficeAddress=registeredOfficeAddress;
		this.otherDescription=otherDescription;
//		this.relationToLicenseeName=relationToLicenseeName;
	}
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "USER_LICENCE_DETAILS_ID", unique = true, nullable = false)
	public int getUserLicenceDetailsId() {
		return userLicenceDetailsId;
	}
	public void setUserLicenceDetailsId(int userLicenceDetailsId) {
		this.userLicenceDetailsId = userLicenceDetailsId;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "USER_ID", nullable = false)
	public UserTbl getUserTbl() {
		return userTbl;
	}
	public void setUserTbl(UserTbl userTbl) {
		this.userTbl = userTbl;
	}
	
	@Column(name = "LICENCE_NAME", length = 100)
	public String getLicenceName() {
		return licenceName;
	}
	public void setLicenceName(String licenceName) {
		this.licenceName = licenceName;
	}
	
	
	
	@Column(name = "LICENCE_NUMBER", length = 50)
	public String getLicenceNumber() {
		return licenceNumber;
	}
	public void setLicenceNumber(String licenceNumber) {
		this.licenceNumber = licenceNumber;
	}
	
	@Column(name = "REGISTERED_AGENT_NAME", length = 100)
	public String getRegisteredAgentName() {
		return registeredAgentName;
	}
	public void setRegisteredAgentName(String registeredAgentName) {
		this.registeredAgentName = registeredAgentName;
	}
	
	@Column(name = "COMPANY_NUMBER", length = 100)
	public String getCompanyNumber() {
		return companyNumber;
	}
	public void setCompanyNumber(String companyNumber) {
		this.companyNumber = companyNumber;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LICENSEE_RELATION_ID", nullable = false)
	public LicenseeRelationTbl getLicenseeRelationId() {
		return licenseeRelationId;
	}
	public void setLicenseeRelationId(LicenseeRelationTbl licenseeRelationId) {
		this.licenseeRelationId = licenseeRelationId;
	}
	
	@Column(name = "ACTIVE_FLAG", length = 1)
	public char getActiveFlag() {
		return activeFlag;
	}
	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_MODIFIED_DATETIME", nullable = false, length = 23)
	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}
	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LAST_MODIFIED_BY", nullable = false)
	public UserTbl getLastModifiedBy() {
		return lastModifiedBy;
	}
	public void setLastModifiedBy(UserTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}
	
	@Column(name = "COMMENTS", length = 600)
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	
	@Column(name = "DELINK", length = 1)
	public Character getDelink() {
		return delink;
	}
	public void setDelink(Character delink) {
		this.delink = delink;
	}
	@Column(name="REGISTERED_OFFICE_ADDRESS",length=1000)
	public String getRegisteredOfficeAddress() {
		return registeredOfficeAddress;
	}
	public void setRegisteredOfficeAddress(String registeredOfficeAddress) {
		this.registeredOfficeAddress = registeredOfficeAddress;
	}

	@Column(name = "OTHER_DESCRIPTION", length = 100)
	public String getOtherDescription() {
		return otherDescription;
	}

	public void setOtherDescription(String otherDescription) {
		this.otherDescription = otherDescription;
	}

//	@Column(name="RELATION_TO_LICENSEE_NAME", length=50)
//	public String getRelationToLicenseeName() {
//		return relationToLicenseeName;
//	}
//	public void setRelationToLicenseeName(String relationToLicenseeName) {
//		this.relationToLicenseeName = relationToLicenseeName;
//	}
	
	
}
