package com.radiant.urfs.web.model;

import java.util.Date;

import com.radiant.urfs.domain.UserTbl;

public class WebExtensionDays {

	private int extensionDaysId;
	private int numExtensionDays;
	private char activeFlag;
	private Date lastModifiedDatetime;
	private UserTbl userTblByLastModifiedBy;

	public int getExtensionDaysId() {
		return extensionDaysId;
	}

	public void setExtensionDaysId(int extensionDaysId) {
		this.extensionDaysId = extensionDaysId;
	}

	public int getNumExtensionDays() {
		return numExtensionDays;
	}

	public void setNumExtensionDays(int numExtensionDays) {
		this.numExtensionDays = numExtensionDays;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public UserTbl getUserTblByLastModifiedBy() {
		return userTblByLastModifiedBy;
	}

	public void setUserTblByLastModifiedBy(UserTbl userTblByLastModifiedBy) {
		this.userTblByLastModifiedBy = userTblByLastModifiedBy;
	}

}
