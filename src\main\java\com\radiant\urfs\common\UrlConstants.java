package com.radiant.urfs.common;

public class UrlConstants {

	public class User {
		public static final String API_BASE = "api/user";
		public static final String LOGIN = "/login";
		public static final String GET_USER = "/getuser/{userId}";
		public static final String DELETE_USER = "/delete/{userId}";
		public static final String USER_REGISTRATION = "/userregistration";
		public static final String GET_ALL_ROLES="/getpermissions";
		public static final String GET_ROLE_BY_ID="/getpermission/{roleId}";
		public static final String GET_MENU_BY_ID="/getmenuitem/{menuId}";
		public static final String GET_ALL_ACCESS_LEVELS="/getroles";
		public static final String GET_ALL_USERS="/getusers";
		public static final String CHANGE_PASSWORD="/changepassword";
		public static final String FORGOT_PASSWORD="/forgotpassword";
		public static final String GET_ACCESS_TOKEN = "/getaccesstoken";
		public static final String SAVE_USER = "/save";
		public static final String SAVE_NEW_USER = "/saveuser";
		public static final String  REGISTER_NEW_USER= "/registerNewUser";
		public static final String UPDATE_USER = "/update";
		public static final String AMMENDMENTS = "/ammendments";
		public static final String GET_PAYMENT_SUMMARY_DETAILS = "/get/paymentsummary";
		public static final String GET_COMPANY_NUMBERS_LIST = "/getCompanyNumbersList";
		public static final String CHECK_USERNAME = "/checkusername/{userName}";
		public static final String GET_RELATION_LICENSEE = "/getrelationtolicensee";
		public static final String GET_REGISTRATION_DETAILS_BY_USERNAME = "/getRegistrationDetails";
		public static final String CHECK_COMPANY_NUMBER = "/checkcompanynumber/{companyNumber}";
		public static final String CHECK_LICENSEE_NAME = "/checklicenseename";
		public static final String VALIDATE_OLD_PASSWORD = "/validateoldpassword";
		public static final String GET_DASHBOARD_COUNTS = "/getdashboardcounts";
		public static final String GET_APPROVED_REJECTED_COMPANY_LIST = "/getApprovedRejectedCompanyList";
		public static final String GET_APPROVED_COMPANY_LIST = "/getApprovedCompanyList";
		public static final String ADD_NEW_LICENSEE = "/addnewlicensee";
		public static final String RESET_PASSWORD="/resetpassword";
		public static final String UNLINK_AML_CFT_LICENSEE="/unlinkamlcftlicensee/{loginUserId}";
		public static final String LINK_AML_CFT_LICENSEE="/linkamlcftlicensee";
		public static final String BULK_REGISTRATION = "/bulkregistration/{loginUserId}";
		public static final String CHECK_UPLOAD_RETURNS = "/checkUploadReturns";
		public static final String UPDATE_UPLOAD_RETURNS = "/updateUploadReturns";
		public static final String GET_LICENSE_NUMBERS_LIST = "/getLicenseNumbersList";
		public static final String UPDATE_COMPANY_DETAILS= "/updateCompnyDetails";
		public static final String GET_ALL_LICENSE_NUMBERS_LIST = "/getAllLicenseNumbersList";
		public static final String SAVE_NEW_ADMIN = "/saveadmin/{loginUserId}";
		public static final String APPROVED_AND_REJECTED_USER_DASHBOARD_LIST = "/approvedandrejectedlist";
		public static final String GET_DETAILS_BY_USERNAME = "/getDetails";
		public static final String GET_ALL_LICENSE_NUMBERS = "/getAllLicenseNumbers";
		public static final String GET_DETAILS_BY_NAME = "/getDetailsByName";
		public static final String UPDATE_DETAILS = "/updateDetails";
		public static final String ADD_EXTENSION_DAYS = "/addextensiondays";
        public static final String GET_EXTENSION_DAYS = "/getextensiondays";
		public static final String ADD_INTERNAL_USERS = "/addinternalusers";
        public static final String GET_ROLE_DETAILS_BY_ROLE_ID= "/getRoleDetailsByRoleId";	
        public static final String GET_SUBMITTED_RETURNS = "/getsubmittedreturns";
        public static final String GET_LICENCE_NUMBER_BY_COMPANY_NUMBER = "/getlicencenumberbycompanynumber";
        public static final String GET_PENDING_USERS = "/getpendingusers";
        public static final String GET_DASHBOARD_LIST = "/getdashboardlist";
        public static final String GET_APPROVED_DASHBOARD_LIST = "/getapproveddashboardlist";
        public static final String GET_APPROVED_REGISTER_LIST = "/getapprovedregisterlist";
        public static final String GET_AMMENDMENTS_DASHBOARD_LIST = "/getammendmentsdashboardlist";
        public static final String GET_REGISTRATION_DASHBOARD_LIST = "/getregistrationdashboardlist";
        public static final String GET_REGISTRATION_PENDING_LIST = "/getregistrationpendinglist";
        public static final String GET_SEARCH_DETAILS = "/getsearchdetails";
        public static final String GET_BUSINESS_PURPOSE_DETAILS = "/getbusinesspurposedetails";
        public static final String GET_PARTNERSHIP_DETAILS = "/getpartnershipdetails";
        public static final String GET_BUSSINESS_OPERATION_DETAILS = "/getbussinessoperationdetails";
        public static final String GET_COMPANY_TYPE_DETAILS = "/getcompanytypedetails";
        public static final String GET_APPROVED_USERS = "/getapprovedusers";
        public static final String APPROVE_USER = "/approve";
        public static final String APPROVE = "/approveuser";
        public static final String REJECT_USER = "/reject";
        public static final String REJECT = "/rejectuser";
        public static final String GET_CHARTER_PDF = "/generatePdf";
        public static final String APPROVED_AND_REJECTED_DASHBOARD_LIST = "/approvedrejectedlist";
        public static final String CHECK_REPORTING_PERIOD_STATUS = "/checkreportingperiodstatus";
        public static final String CHECK_PENDING_USER = "/checkpendinguser";
        public static final String PAY = "/pay";
		public static final String GET_TRANSACTIONS_DETAILS = "/gettransactions";
		public static final String GET_TRANSACTIONS_DETAILS_USER = "/gettransactionsbyuser";
		public static final String GET_USER_DETAILS = "/getuserdetails";
		public static final String SEND_EMAIL_DETAILS = "/sendemaildetails";
		public static final String ENABLE_DISABLE = "/enable/disable";
		public static final String ACTIVE_INACTIVE = "/active/inactive";





	}
	
	
	public class CompanyBackground{
		public static final String API_BASE = "api/companybackground";
		public static final String ADD_COMPANY = "/addcompany";
		public static final String GET_ALL_COMPANY = "/getallcompanies";
		public static final String DELETE_COMPANY = "/deletecompany";
		public static final String EDIT_COMPANY = "/editcompany";
		public static final String VIEW_COMPANY = "/viewcompany";	
	}
	
	
	
	public class SecurityConstants {
	    public static final long JWT_EXPIRATION = 70000;
	}

}
