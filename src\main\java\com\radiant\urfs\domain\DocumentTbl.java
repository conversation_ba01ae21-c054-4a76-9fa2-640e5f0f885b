package com.radiant.urfs.domain;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * DocumentTbl generated by hbm2java
 */
@Entity
@Table(name = "DOCUMENT_TBL")
public class DocumentTbl implements java.io.Serializable {

	private Integer documentId;
	private DocumentTypeTbl documentTypeTbl;
	private UserSBTbl userTbl;
	private byte[] document;
	private String documentName;
	private String documentFileExtn;
	private char activeFlag;
	private Date lastModifiedDatetime;
	public DocumentTbl() {
	}

	public DocumentTbl(DocumentTypeTbl documentTypeTbl, UserSBTbl userTbl, byte[] document, String documentName,
			String documentFileExtn, char activeFlag, Date lastModifiedDatetime) {
		this.documentTypeTbl = documentTypeTbl;
		this.userTbl = userTbl;
		this.document = document;
		this.documentName = documentName;
		this.documentFileExtn = documentFileExtn;
		this.activeFlag = activeFlag;
		this.lastModifiedDatetime = lastModifiedDatetime;
	}


	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "DOCUMENT_ID", unique = true, nullable = false)
	public Integer getDocumentId() {
		return this.documentId;
	}

	public void setDocumentId(Integer documentId) {
		this.documentId = documentId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "DOCUMENT_TYPE_ID", nullable = false)
	public DocumentTypeTbl getDocumentTypeTbl() {
		return this.documentTypeTbl;
	}

	public void setDocumentTypeTbl(DocumentTypeTbl documentTypeTbl) {
		this.documentTypeTbl = documentTypeTbl;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LAST_MODIFIED_BY", nullable = false)
	public UserSBTbl getUserTbl() {
		return this.userTbl;
	}

	public void setUserTbl(UserSBTbl userTbl) {
		this.userTbl = userTbl;
	}

	@Column(name = "DOCUMENT", nullable = false)
	public byte[] getDocument() {
		return this.document;
	}

	public void setDocument(byte[] document) {
		this.document = document;
	}

	@Column(name = "DOCUMENT_NAME", nullable = false)
	public String getDocumentName() {
		return this.documentName;
	}

	public void setDocumentName(String documentName) {
		this.documentName = documentName;
	}

	@Column(name = "DOCUMENT_FILE_EXTN", nullable = false, length = 10)
	public String getDocumentFileExtn() {
		return this.documentFileExtn;
	}

	public void setDocumentFileExtn(String documentFileExtn) {
		this.documentFileExtn = documentFileExtn;
	}

	@Column(name = "ACTIVE_FLAG", nullable = false, length = 1)
	public char getActiveFlag() {
		return this.activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_MODIFIED_DATETIME", nullable = false, length = 23)
	public Date getLastModifiedDatetime() {
		return this.lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	

}