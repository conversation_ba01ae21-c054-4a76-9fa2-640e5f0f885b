/**
* <AUTHOR> EU231
* <AUTHOR> E-mail:<EMAIL>
* @version Creation time: May 13, 2021 8:20:05 PM
* Class Description: UserDao.java
*/
package com.radiant.urfs.dao;

import java.util.List;
import org.hibernate.Session;
import org.springframework.http.ResponseEntity;
import com.radiant.urfs.domain.DocumentTbl;
import com.radiant.urfs.domain.LicenseeRelationTbl;
import com.radiant.urfs.domain.UserLicenceDetailsTbl;
import com.radiant.urfs.domain.UserLoginTbl;
import com.radiant.urfs.domain.UserSBTbl;
import com.radiant.urfs.domain.UserTbl;
import com.radiant.urfs.exception.BviReturnsException;
import com.radiant.urfs.web.model.WebPassword;
import com.radiant.urfs.web.model.WebUser;
import com.radiant.urfs.web.model.WebUserData;

import com.radiant.urfs.web.model.SBDetails;

public interface UserDao {

	public ResponseEntity<?> saveNewUserRegistration(SBDetails sbDetails, DocumentTbl addndocument)
			throws BviReturnsException;

	public ResponseEntity<?> ammendments(SBDetails sbDetails, DocumentTbl addndocument, Integer userId)
			throws BviReturnsException;

	public ResponseEntity<?> getPaymentSummaryDetails(String paymentCode) throws BviReturnsException;

	public boolean checkUsername(String userName) throws BviReturnsException;

	public UserLoginTbl getUserLoginTbl(String userName) throws BviReturnsException;

	public UserLoginTbl getUserLoginTblByUserId(Integer userId) throws BviReturnsException;

	public UserTbl getUserTbl(int userId) throws BviReturnsException;

	public LicenseeRelationTbl getRelationToLicenseeById(int licenseeRelationId) throws BviReturnsException;

	public void saveUserLicenceDetails(UserLicenceDetailsTbl userLicenceDetailsTbl) throws BviReturnsException;

	public void saveUserLoginTbl(UserLoginTbl userLoginTbl) throws BviReturnsException;

	public void saveUserTbl(UserTbl userTbl) throws BviReturnsException;

	public ResponseEntity<?> changePassword(WebPassword webPassword) throws BviReturnsException;

	public ResponseEntity<?> forgotPassword(String userName, String email) throws BviReturnsException;

	public boolean validateOldPassword(WebUserData webUserData) throws BviReturnsException;

	public ResponseEntity<?> resetPassword(String token, String newPassword) throws BviReturnsException;

	public List<String> getRegistrationPendingUsers() throws BviReturnsException;

	public List<String> getFiledAmlReturns() throws BviReturnsException;

	public ResponseEntity<?> getDetailsByUserName(String userName) throws BviReturnsException;

	public ResponseEntity<?> getAllUsers() throws BviReturnsException;

	public UserTbl getLoggedInUserDetails(Session session);

	public UserSBTbl getLoggedInUser(Session session);

	public ResponseEntity<?> userRegistration(WebUser signupTbl) throws BviReturnsException;

	public UserLoginTbl verifyUserName(String username) throws BviReturnsException;

	public ResponseEntity<?> getDashboardList(char authInd) throws BviReturnsException;

	public ResponseEntity<?> getApprovedDashboardList() throws BviReturnsException;

	public ResponseEntity<?> getAmmendmentsDashboardList(Integer userId) throws BviReturnsException;

	public ResponseEntity<?> getSearchDetails(String sbNumber, String startDate, String endDate, String entityName,
			Integer businessOperationId, String companyHead, String url, String status, Integer companyTypeId,
			String AreasInterests, String BusinessKeywords) throws BviReturnsException;

	public ResponseEntity<?> approve(List<Integer> entityTempIds) throws BviReturnsException;

	public ResponseEntity<?> reject(String comments, List<Integer> entityTempIds) throws BviReturnsException;


	public ResponseEntity<?> getBussinessPurposeDetails() throws BviReturnsException;

	public ResponseEntity<?> getPartnershipDetails() throws BviReturnsException;

	public ResponseEntity<?> getBussinessOperationDetails() throws BviReturnsException;

	public ResponseEntity<?> getCompanyTypeDetails() throws BviReturnsException;

	public ResponseEntity<?> getRegistrationDashboardList(String sbrNumber) throws BviReturnsException;

	public ResponseEntity<?> getTransactionsDetails() throws BviReturnsException;

	public ResponseEntity<?> sendEmailDetails(String comments ,String entityName,String subject)throws BviReturnsException;

	public ResponseEntity<?> enableDisableUser(String activeFlag, Integer userId, String entityName)
			throws BviReturnsException;

	public ResponseEntity<?> activeInactiveUsers() throws BviReturnsException;

	public ResponseEntity<?> getTransactionsDetailsByUser(Integer lastModifiedBy) throws BviReturnsException;

	public ResponseEntity<?> registerNewUser(SBDetails sbDetails) throws BviReturnsException;

	public ResponseEntity<?> updateUser(SBDetails sbDetails, Integer userId) throws BviReturnsException;

	public ResponseEntity<?> getRegistrationPendingList(String sbrNumber) throws BviReturnsException;

	public ResponseEntity<?> getApprovedRegisterList() throws BviReturnsException;

	public ResponseEntity<?> getDetailsByUser(Integer lastModifiedBy) throws BviReturnsException;

}
