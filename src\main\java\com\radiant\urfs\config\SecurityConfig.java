package com.radiant.urfs.config;

import java.util.LinkedHashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import com.radiant.urfs.domain.UserTbl;
import com.radiant.urfs.repository.UserLoginRepository;
import com.radiant.urfs.repository.UserTblRepository;
import com.radiant.urfs.service.CustomUserDetailsService;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig extends WebSecurityConfigurerAdapter {


	 @Autowired
	    private CustomAuthenticationProvider customAuthenticationProvider;

	    @Autowired
	    private JwtUtil jwtUtil; // Inject JwtUtil here

	    @Autowired
	    private CustomUserDetailsService userDetailsService;
	
	
//	    @Override
//	    protected void configure(HttpSecurity http) throws Exception {
//	        http.csrf().disable()
//	            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and()
//	            .authorizeRequests()
//	            .antMatchers("/api/user/login", "/api/user/saveuser","/api/user/getpartnershipdetails","/api/user/getbussinessoperationdetails","/api/user/getcompanytypedetails","/api/user/getbusinesspurposedetails","/api/user/get/paymentsummary","/api/user/pay").permitAll() // Public endpoints
//	            .anyRequest().authenticated() // Secure all other endpoints
//	            .and()
//	            .cors();
//
//	        // Pass the jwtUtil and userDetailsService to JwtAuthenticationFilter
//	        http.addFilterBefore(new JwtAuthenticationFilter(jwtUtil, userDetailsService), 
//	                             UsernamePasswordAuthenticationFilter.class);
//	    }
	    
	    
	    @Override
	    protected void configure(HttpSecurity http) throws Exception {
	        http.csrf().disable()
	            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and()
	            .authorizeRequests()
	            .antMatchers(
	                "/api/user/login", 
	                "/api/user/saveuser",
	                "/api/user/getpartnershipdetails",
	                "/api/user/getbussinessoperationdetails",
	                "/api/user/getcompanytypedetails",
	                "/api/user/getbusinesspurposedetails",
	                "/api/user/get/paymentsummary",
	                "/api/user/forgotpassword",
	                "/api/user/resetpassword",
	                "/api/user/pay",
	                "/api/user/checkusername/**",
	                "/api/member/register", // Allow Drupal webhook
	                "/api/member/ping"
	            ).permitAll() // Public endpoints
	            .anyRequest().authenticated() // Secure all other endpoints
	            .and()
	            .cors();

	        // Add the JWT filter
	        http.addFilterBefore(new JwtAuthenticationFilter(jwtUtil, userDetailsService), 
	                             UsernamePasswordAuthenticationFilter.class);
	    }


	    @Override
	    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
	        auth.authenticationProvider(customAuthenticationProvider);
	    }

	    @Bean
	    public BCryptPasswordEncoder passwordEncoder() {
	        return new BCryptPasswordEncoder();
	    }

	    @Override
	    @Bean
	    public AuthenticationManager authenticationManagerBean() throws Exception {
	        return super.authenticationManagerBean();
	    }

}
