package com.radiant.urfs.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;

import com.radiant.urfs.domain.UserTbl;

import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "ENTITY_TEMP_TBL")
@Data // Generates getters, setters, toString, equals, and hashCode methods
@NoArgsConstructor // Generates a no-args constructor
@AllArgsConstructor // Generates a constructor with all fields
public class EntityTempTbl implements Serializable{

	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ENTITY_TEMP_ID")
    private Integer entityTempId;

    @Column(name = "ENTITY_ID")
    private Integer entityId;

    @Column(name = "ENTITY_NAME", length = 255)
    private String entityName;
  
    @Column(name = "PHONE", length = 15)
    private String phone;

    @Column(name = "URL", length = 255)
    private String url;

    @Column(name = "INCORPORATION_DATE")
    @Temporal(TemporalType.DATE)
    private Date incorporationDate;

    @Column(name = "SIZE_COMPANY_IND", length = 1)
    private Character sizeCompanyInd;
    
    @Column(name = "EIN", length = 40)
    private String federalEmployerIdentificationNumber;

    @Column(name = "NUM_EMPLOYEES", length = 40)
    private String numEmployees;

    @Column(name = "ADDRESS", length = 100)
    private String address;

    @Column(name = "COMPANY_PROFILE", length = 300)
    private String companyProfile;
    
   

    @Column(name = "SBR_NUMBER", length = 20)
    private String sbrNumber;

    @Column(name = "EMAIL_ADDRESS", length = 255)
    private String emailAddress;
    
  
    
   
    
    @Column(name = "BUSINESS_KEYWORDS",length = 255)
    private String BusinessKeywords;
    
    @Column(name = "AREAS_INTERESTS", length = 80)
    private String areasInterests;

    
    @Column(name = "REVENUE", length = 40)
    private String estimatedAnnualRevenue;

    @Column(name = "OWNER_FIRST_NAME", length = 40)
    private String ownerFirstName;

    @Column(name = "OWNER_LAST_NAME", length = 40)
    private String ownerLastName;

    @Column(name = "OWNER_PHONE", length = 10)
    private String ownerPhone;

    @Column(name = "OWNER_EMAIL", length = 40)
    private String ownerEmail;

    
    @Column(name = "INCORPORATION_STATE", length = 40)
    private String stateOfIncorporation;
    
   


    @Column(name = "ACTIVE_FLAG", length = 1)
    private char activeFlag;

    @Column(name = "LAST_MODIFIED_DATETIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastModifiedDatetime;

   
    @Column(name = "AUTH_IND", length = 1)
    private char authInd;


    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "AUTH_BY")
    private UserSBTbl authBy;
    
    @ManyToOne(fetch = FetchType.LAZY)
   	@JoinColumn(name = "COMPANY_TYPE_ID")
    private CompanyTypeTbl companyTypeId;

    @ManyToOne(fetch = FetchType.LAZY)
   	@JoinColumn(name = "BUSINESS_PURPOSE_ID")
    private BusinessPurposeTbl businessPurposeId;

    
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BUSINESS_OPERATION_ID")
    private BusinessOperationTypeTbl businessOperationId;
    
    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "USER_ID")
    private UserSBTbl userId;
    
    
//    @ManyToOne(fetch = FetchType.LAZY)
//	@JoinColumn(name = "DOCUMENT_ID")
//    private DocumentTbl documentTblByDocumentAddnId;

    
   
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "LAST_MODIFIED_BY")
    private UserSBTbl lastModifiedBy;



	public Integer getEntityTempId() {
		return entityTempId;
	}


	public void setEntityTempId(Integer entityTempId) {
		this.entityTempId = entityTempId;
	}


	public Integer getEntityId() {
		return entityId;
	}


	public void setEntityId(Integer entityId) {
		this.entityId = entityId;
	}



	public String getPhone() {
		return phone;
	}


	public void setPhone(String phone) {
		this.phone = phone;
	}


	public String getUrl() {
		return url;
	}


	public void setUrl(String url) {
		this.url = url;
	}


	public Date getIncorporationDate() {
		return incorporationDate;
	}


	public void setIncorporationDate(Date incorporationDate) {
		this.incorporationDate = incorporationDate;
	}


	
	public String getEntityName() {
		return entityName;
	}


	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}


	public Character getSizeCompanyInd() {
		return sizeCompanyInd;
	}


	public void setSizeCompanyInd(Character sizeCompanyInd) {
		this.sizeCompanyInd = sizeCompanyInd;
	}


	public String getFederalEmployerIdentificationNumber() {
		return federalEmployerIdentificationNumber;
	}


	public void setFederalEmployerIdentificationNumber(String federalEmployerIdentificationNumber) {
		this.federalEmployerIdentificationNumber = federalEmployerIdentificationNumber;
	}


	public String getNumEmployees() {
		return numEmployees;
	}


	public void setNumEmployees(String numEmployees) {
		this.numEmployees = numEmployees;
	}


	public String getAddress() {
		return address;
	}


	public void setAddress(String address) {
		this.address = address;
	}


	public String getCompanyProfile() {
		return companyProfile;
	}


	public void setCompanyProfile(String companyProfile) {
		this.companyProfile = companyProfile;
	}


	public String getSbrNumber() {
		return sbrNumber;
	}


	public void setSbrNumber(String sbrNumber) {
		this.sbrNumber = sbrNumber;
	}


	public String getEmailAddress() {
		return emailAddress;
	}


	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}





	public String getAreasInterests() {
		return areasInterests;
	}


	public void setAreasInterests(String areasInterests) {
		this.areasInterests = areasInterests;
	}


	public String getEstimatedAnnualRevenue() {
		return estimatedAnnualRevenue;
	}


	public void setEstimatedAnnualRevenue(String estimatedAnnualRevenue) {
		this.estimatedAnnualRevenue = estimatedAnnualRevenue;
	}


	public String getOwnerFirstName() {
		return ownerFirstName;
	}


	public void setOwnerFirstName(String ownerFirstName) {
		this.ownerFirstName = ownerFirstName;
	}


	public String getOwnerLastName() {
		return ownerLastName;
	}


	public void setOwnerLastName(String ownerLastName) {
		this.ownerLastName = ownerLastName;
	}


	public String getOwnerPhone() {
		return ownerPhone;
	}


	public void setOwnerPhone(String ownerPhone) {
		this.ownerPhone = ownerPhone;
	}


	public String getOwnerEmail() {
		return ownerEmail;
	}


	public void setOwnerEmail(String ownerEmail) {
		this.ownerEmail = ownerEmail;
	}


	public String getStateOfIncorporation() {
		return stateOfIncorporation;
	}


	public void setStateOfIncorporation(String stateOfIncorporation) {
		this.stateOfIncorporation = stateOfIncorporation;
	}


	


	public String getBusinessKeywords() {
		return BusinessKeywords;
	}


	public void setBusinessKeywords(String businessKeywords) {
		BusinessKeywords = businessKeywords;
	}


	public char getActiveFlag() {
		return activeFlag;
	}


	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}


	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}


	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}


	public char getAuthInd() {
		return authInd;
	}


	public void setAuthInd(char authInd) {
		this.authInd = authInd;
	}


	public UserSBTbl getAuthBy() {
		return authBy;
	}


	public void setAuthBy(UserSBTbl authBy) {
		this.authBy = authBy;
	}


	public CompanyTypeTbl getCompanyTypeId() {
		return companyTypeId;
	}


	public void setCompanyTypeId(CompanyTypeTbl companyTypeId) {
		this.companyTypeId = companyTypeId;
	}


	public BusinessPurposeTbl getBusinessPurposeId() {
		return businessPurposeId;
	}


	public void setBusinessPurposeId(BusinessPurposeTbl businessPurposeId) {
		this.businessPurposeId = businessPurposeId;
	}


	


	public BusinessOperationTypeTbl getBusinessOperationId() {
		return businessOperationId;
	}


	public void setBusinessOperationId(BusinessOperationTypeTbl businessOperationId) {
		this.businessOperationId = businessOperationId;
	}


	public UserSBTbl getUserId() {
		return userId;
	}


	public void setUserId(UserSBTbl userId) {
		this.userId = userId;
	}


//	public DocumentTbl getDocumentTblByDocumentAddnId() {
//		return documentTblByDocumentAddnId;
//	}
//
//
//	public void setDocumentTblByDocumentAddnId(DocumentTbl documentTblByDocumentAddnId) {
//		this.documentTblByDocumentAddnId = documentTblByDocumentAddnId;
//	}


	public UserSBTbl getLastModifiedBy() {
		return lastModifiedBy;
	}


	public void setLastModifiedBy(UserSBTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}


	public static long getSerialversionuid() {
		return serialVersionUID;
	}


	
	

    
	

	

	
	
}