package com.radiant.urfs.domain;

import java.util.Date;

public class WebAssociationTempTbl {

	private Integer WebentityAssociationTempId;

	private String associationName;

	private Date membershipStartDate;

	private Date membershipEndDate;

	private String membershipLevel;

	private char activeFlag;

	private Date lastModifiedDatetime;

	private UserSBTbl lastModifiedBy;

	// Getters and Setters

	public Integer getWebentityAssociationTempId() {
		return WebentityAssociationTempId;
	}

	public void setWebentityAssociationTempId(Integer webentityAssociationTempId) {
		WebentityAssociationTempId = webentityAssociationTempId;
	}

//    public WebTempTbl getWebTemp() {
//        return webTemp;
//    }
//
//    public void setWebTemp(WebTempTbl webTemp) {
//        this.webTemp = webTemp;
//    }

	public String getAssociationName() {
		return associationName;
	}

	public void setAssociationName(String associationName) {
		this.associationName = associationName;
	}

	public Date getMembershipStartDate() {
		return membershipStartDate;
	}

	public void setMembershipStartDate(Date membershipStartDate) {
		this.membershipStartDate = membershipStartDate;
	}

	public Date getMembershipEndDate() {
		return membershipEndDate;
	}

	public void setMembershipEndDate(Date membershipEndDate) {
		this.membershipEndDate = membershipEndDate;
	}

	public String getMembershipLevel() {
		return membershipLevel;
	}

	public void setMembershipLevel(String membershipLevel) {
		this.membershipLevel = membershipLevel;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public UserSBTbl getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(UserSBTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}
}
