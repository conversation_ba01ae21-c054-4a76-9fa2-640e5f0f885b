package com.radiant.urfs.repository;

import com.radiant.urfs.domain.UserSBLoginTbl;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserSBLoginRepository extends JpaRepository<UserSBLoginTbl, Integer> {

	//UserSBLoginTbl findByusername(String username);

	Optional<UserSBLoginTbl> findByUsername(String username);

	UserSBLoginTbl findByusername(String username);
    
}