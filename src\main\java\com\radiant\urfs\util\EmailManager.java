package com.radiant.urfs.util;

import java.io.File;
import java.util.Properties;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.Address;
import javax.mail.Authenticator;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import org.apache.log4j.Logger;

import com.radiant.urfs.exception.BviReturnsException;
import com.radiant.urfs.web.model.EmailDetails;

public class EmailManager {

	Logger log = Logger.getLogger(EmailManager.class);
	private static Session session = null;

	private static final String MAIL_SERVER = "smtp";
	private static final String SMTP_HOST_NAME = "smtp.office365.com";
	private static final int SMTP_HOST_PORT = 587;

	private static String mailBoxUserName = "<EMAIL>";
	private static String mailBoxPassword = "Hay09967";

	// ========================================================================

	private static EmailManager instance = null;

	// ========================================================================

	private EmailManager() {
	}

	// ========================================================================
	public static synchronized EmailManager getInstance() {
		if (instance == null) {
			instance = new EmailManager();
		}
		return instance;
	}

	// ========================================================================

	public boolean send(EmailDetails emailDetails) throws BviReturnsException {
		String logTag = "send(): ";
		log.info("entering into " + logTag);
		boolean isMailSent = false;
		Address[] toAddresses = null;
		Address toAddress = null;
		int toAddressCount = 0;
		Address[] ccAddresses = null;
		Address ccAddress = null;
		int ccAddressCount = 0;

		try {
			if (emailDetails.getToRecipients() != null
					&& emailDetails.getSubject() != null/* && emailDetails.getMessageContent() != null */) {
				Properties props = ResourceUtil.getEnvSpecificProperties();
				log.info("entering into Props : " + props);
				String userName = props.getProperty("mail.username");
				String password = props.getProperty("mail.password");

				Authenticator auth = new Authenticator() {
					public PasswordAuthentication getPasswordAuthentication() {
						return new PasswordAuthentication(userName, password);
					}
				};

				// Dev mail properties
				props.put("mail.smtp.host", props.getProperty("mail.host"));
				props.put("mail.smtp.socketFactory.port", "465");
				props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
				props.put("mail.smtp.port", props.getProperty("mail.port"));
				props.put("mail.smtp.auth", props.getProperty("mail.smtp.auth"));
				props.put("mail.smtp.starttls.enable", props.getProperty("mail.smtp.starttls.enable"));

				log.info("entering into EmailManager : send() with userName : " + userName);
				log.info("entering into EmailManager : send() with password : " + "xxxxxxxxx");
				//log.info("entering into EmailManager : send() with SMTP Host Name : " + props.getProperty("mail.smtp.host"));
				//log.info("entering into EmailManager : send() with SMTP Port: " + props.getProperty("mail.smtp.port"));
				session = Session.getInstance(props, new javax.mail.Authenticator() {
					protected PasswordAuthentication getPasswordAuthentication() {
						return new PasswordAuthentication(userName, password);
					}
				});

				log.info("entering into EmailManager : session Established :  ");

				MimeMessage message = new MimeMessage(session);

				message.setFrom(new InternetAddress(userName));

				toAddresses = new Address[emailDetails.getToRecipients().length];
				for (int i = 0; i < emailDetails.getToRecipients().length; i++) {
					if (emailDetails.getToRecipients()[i] != null) {
						toAddress = new InternetAddress(emailDetails.getToRecipients()[i].trim());
						toAddresses[toAddressCount] = toAddress;
						toAddressCount++;
					}
				}
				message.setRecipients(Message.RecipientType.TO, toAddresses);
				// Setting CC
				if (emailDetails.getCcRecipients() != null && emailDetails.getCcRecipients().length > 0) {
					ccAddresses = new Address[emailDetails.getCcRecipients().length];

					for (int i = 0; i < emailDetails.getCcRecipients().length; i++) {
						if (emailDetails.getCcRecipients()[i] != null) {
							ccAddress = new InternetAddress(emailDetails.getCcRecipients()[i].trim());
							ccAddresses[ccAddressCount] = ccAddress;
							ccAddressCount++;
						}
					}
					message.setRecipients(Message.RecipientType.CC, ccAddresses);
				}
				// Setting Subject
				message.setSubject(emailDetails.getSubject());
				// create MimeBodyPart object and set your message text
				BodyPart messageBodyPart = new MimeBodyPart();
				messageBodyPart.setText(emailDetails.getMessageContent());
				Multipart multiPart = new MimeMultipart();
				multiPart.addBodyPart(messageBodyPart);
				if (emailDetails.getAttachmentFilePath() != null) {
					// create new MimeBodyPart object and set DataHandler object to this object
					MimeBodyPart attachmentMimeBodyPart = new MimeBodyPart();
					DataSource source = new FileDataSource(emailDetails.getAttachmentFilePath());
					attachmentMimeBodyPart.setDataHandler(new DataHandler(source));
					attachmentMimeBodyPart.setFileName(getFileName(emailDetails.getAttachmentFilePath()));
					// create Multipart object and add MimeBodyPart objects to this object
					multiPart.addBodyPart(attachmentMimeBodyPart);
				}
				// Set the multiplart object to the message object
				message.setContent(multiPart);
				// send message
				Transport.send(message);
				isMailSent = true;
				log.info("message sent successfully");
			} else {
				log.error("Invalid Input");
			}
		} catch (MessagingException e) {
			log.error("Email Messaging Error" + e);
			throw new BviReturnsException(logTag + "Problem while sending the mail, " + e);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("Email  Error" + e);
			throw new BviReturnsException(logTag + "Problem while sending the mail, " + e);
		}
		return isMailSent;
	}

	// ========================================================================

	private String getFileName(String attachementFilePath) {
		File file = new File(attachementFilePath);
		// return attachementFilePath.substring(attachementFilePath.lastIndexOf("/")+ 1, attachementFilePath.length());
		return file.getName();
	}

	// ========================================================================
}
