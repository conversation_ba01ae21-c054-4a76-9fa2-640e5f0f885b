spring.application.name=ssb
spring.datasource.url=******************************************************************
spring.datasource.username=ssbuser
spring.datasource.password=Busine$$@007
spring.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.SQLServer2012Dialect
spring.jpa.show-sql=true
spring.session.jdbc.schema=IAICC_DEV
server.port=8443

logging.file=logs/ssbbe.log
logging.level.root=INFO

spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=500
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.leak-detection-threshold=60000

spring.activiti.database-schema-update=false

spring.servlet.multipart.enabled=true
spring.jpa.properties.hibernate.format_sql=true
spring.servlet.multipart.file-size-threshold=2MB


jwt.client_id=sciits-fiduciary
jwt.client_secret=sciits-secret
jwt.grant_type=password
jwt.refresh_token=refresh_token
jwt.score_read=read
jwt.score_write=write

AbstractUserDetailsAuthenticationProvider.badCredentials=Invalid username or password

security.oauth2.resource.filter-order=3
spring.main.allow-bean-definition-overriding=true
spring.jpa.properties.hibernate.current_session_context_class=org.springframework.orm.hibernate4.SpringSessionContext

#############
#Mailing deails
mail.host=smtp.gmail.com
mail.port=587
mail.username=<EMAIL>
mail.password=kzaefyobjyvanzwb
mail.ccRecipients=<EMAIL>


# Other properties
mail.smtp.auth=true
mail.smtp.starttls.enable=true
#############

bvi_username=<EMAIL>

#Forgot password
#reset.password = http://sbregistry.radiant.digital:8080/Sbr-FE/#/resetpassword?token=
reset.password =http://sbregistry.radiant.digital:8080/Sbr-FE/#/resetpassword?token=


#uplod File Size
spring.servlet.multipart.max-file-size=1024MB
spring.servlet.multipart.max-request-size=1024MB
spring.mvc.hiddenmethod.filter.enabled = true

reportingPeriodYears = 2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030