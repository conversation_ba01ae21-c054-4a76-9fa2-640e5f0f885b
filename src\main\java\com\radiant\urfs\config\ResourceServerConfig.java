package com.radiant.urfs.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.ResourceServerTokenServices;

//@Configuration
//@EnableResourceServer
//public class ResourceServerConfig extends ResourceServerConfigurerAdapter {
//	@Autowired
//	private ResourceServerTokenServices tokenServices;
//
//
//
//	@Autowired
//	private CustomAuthenticationProvider customAuthenticationProvider;
//
//	@Override
//	public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
//		resources.resourceId("radiant-source").tokenServices(tokenServices);
//	}
//
//	@Override
//	public void configure(HttpSecurity http) throws Exception {
//		http.requestMatchers().and().authorizeRequests().antMatchers("/api/v1/user/login", "/user/registration").permitAll()
//				.antMatchers("/**").authenticated();
//		http.formLogin().loginPage("/api/v1/user/login").failureUrl("/login?error=true").usernameParameter("username")
//				.passwordParameter("password").loginProcessingUrl("/security_check");
//		http.logout().logoutUrl("/logout").invalidateHttpSession(true).logoutSuccessUrl("/");
//	}
//
//	protected void configure(AuthenticationManagerBuilder auth) throws Exception {
//		auth.authenticationProvider(customAuthenticationProvider);
//	}
//}
