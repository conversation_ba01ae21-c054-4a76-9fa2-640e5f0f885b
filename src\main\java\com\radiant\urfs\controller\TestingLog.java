package com.radiant.urfs.controller;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.apache.log4j.Logger;

@RestController
@RequestMapping("/logsTest")
public class TestingLog {

	private static final Logger log = Logger.getLogger(TestingLog.class);
	
	@RequestMapping(value ="/check", method = RequestMethod.POST)
	public String changePassword(@RequestBody String changePassword) {
			log.info("info message" + changePassword);
			log.debug("debug message"+changePassword);
			log.warn("warn message"+changePassword);
			log.fatal("fatal message"+changePassword);
			log.error("error message" + changePassword);
		return "checking Logs";
	}
}
