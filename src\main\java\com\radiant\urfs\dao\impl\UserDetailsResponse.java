package com.radiant.urfs.dao.impl;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


public class UserDetailsResponse {

    private Integer userDetailsId;
    private String emailAddress;
    private String phone;
    private char activeFlagUserDetails;
    private Date lastModifiedDatetimeUserDetails;

    private Integer userId;
    private String firstName;
    private String lastName;
    private char activeFlagUser;
    private Date lastModifiedDatetimeUser;

    private Integer userLoginId;
    private String username;
    private Date lastLoginDate;
    private Date lastPwdChangeDate;
    private String ipAddress;
    private Date lastModifiedDatetimeUserLogin;
    private char activeFlagUserLogin;
    
    
    
	public UserDetailsResponse() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	
	
	public UserDetailsResponse(Integer userDetailsId, String emailAddress, String phone, char activeFlagUserDetails,
			Date lastModifiedDatetimeUserDetails, Integer userId, String firstName, String lastName,
			char activeFlagUser, Date lastModifiedDatetimeUser, Integer userLoginId, String username,
			Date lastLoginDate, Date lastPwdChangeDate, String ipAddress, Date lastModifiedDatetimeUserLogin,
			char activeFlagUserLogin) {
		super();
		this.userDetailsId = userDetailsId;
		this.emailAddress = emailAddress;
		this.phone = phone;
		this.activeFlagUserDetails = activeFlagUserDetails;
		this.lastModifiedDatetimeUserDetails = lastModifiedDatetimeUserDetails;
		this.userId = userId;
		this.firstName = firstName;
		this.lastName = lastName;
		this.activeFlagUser = activeFlagUser;
		this.lastModifiedDatetimeUser = lastModifiedDatetimeUser;
		this.userLoginId = userLoginId;
		this.username = username;
		this.lastLoginDate = lastLoginDate;
		this.lastPwdChangeDate = lastPwdChangeDate;
		this.ipAddress = ipAddress;
		this.lastModifiedDatetimeUserLogin = lastModifiedDatetimeUserLogin;
		this.activeFlagUserLogin = activeFlagUserLogin;
	}



	public Integer getUserDetailsId() {
		return userDetailsId;
	}
	public void setUserDetailsId(Integer userDetailsId) {
		this.userDetailsId = userDetailsId;
	}
	public String getEmailAddress() {
		return emailAddress;
	}
	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public char getActiveFlagUserDetails() {
		return activeFlagUserDetails;
	}
	public void setActiveFlagUserDetails(char activeFlagUserDetails) {
		this.activeFlagUserDetails = activeFlagUserDetails;
	}
	public Date getLastModifiedDatetimeUserDetails() {
		return lastModifiedDatetimeUserDetails;
	}
	public void setLastModifiedDatetimeUserDetails(Date lastModifiedDatetimeUserDetails) {
		this.lastModifiedDatetimeUserDetails = lastModifiedDatetimeUserDetails;
	}
	public Integer getUserId() {
		return userId;
	}
	public void setUserId(Integer userId) {
		this.userId = userId;
	}
	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	public String getLastName() {
		return lastName;
	}
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	public char getActiveFlagUser() {
		return activeFlagUser;
	}
	public void setActiveFlagUser(char activeFlagUser) {
		this.activeFlagUser = activeFlagUser;
	}
	public Date getLastModifiedDatetimeUser() {
		return lastModifiedDatetimeUser;
	}
	public void setLastModifiedDatetimeUser(Date lastModifiedDatetimeUser) {
		this.lastModifiedDatetimeUser = lastModifiedDatetimeUser;
	}
	public Integer getUserLoginId() {
		return userLoginId;
	}
	public void setUserLoginId(Integer userLoginId) {
		this.userLoginId = userLoginId;
	}
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}
	public Date getLastLoginDate() {
		return lastLoginDate;
	}
	public void setLastLoginDate(Date lastLoginDate) {
		this.lastLoginDate = lastLoginDate;
	}
	public Date getLastPwdChangeDate() {
		return lastPwdChangeDate;
	}
	public void setLastPwdChangeDate(Date lastPwdChangeDate) {
		this.lastPwdChangeDate = lastPwdChangeDate;
	}
	public String getIpAddress() {
		return ipAddress;
	}
	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
	public Date getLastModifiedDatetimeUserLogin() {
		return lastModifiedDatetimeUserLogin;
	}
	public void setLastModifiedDatetimeUserLogin(Date lastModifiedDatetimeUserLogin) {
		this.lastModifiedDatetimeUserLogin = lastModifiedDatetimeUserLogin;
	}
	public char getActiveFlagUserLogin() {
		return activeFlagUserLogin;
	}
	public void setActiveFlagUserLogin(char activeFlagUserLogin) {
		this.activeFlagUserLogin = activeFlagUserLogin;
	}
    
    
    
}
