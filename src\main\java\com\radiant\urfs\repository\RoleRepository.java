package com.radiant.urfs.repository;

import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import com.radiant.urfs.domain.Role;



@Repository
public interface RoleRepository extends CrudRepository<Role, Integer> {
	
	public Role findByRoleDesc(String roleDesc);
	
	//public Optional<Role> findById(Long id);
	
	public Role findByRoleName(String roleName);
	
}
