package com.radiant.urfs.domain;
// Generated Feb 23, 2021 12:15:49 PM by Hibernate Tools 4.3.1.Final

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * UserAuthCodeTbl generated by hbm2java
 */
@Entity
@Table(name = "USER_AUTH_CODE_TBL", catalog = "dbo")
public class UserAuthCodeTbl implements java.io.Serializable {

	private int userAuthCodeId;
	private UserTbl userTbl;
	private String authCode;
	private Date loginTimestamp;
	private Date logoutTimestamp;
	private char status;

	public UserAuthCodeTbl() {
	}

	public UserAuthCodeTbl(int userAuthCodeId, UserTbl userTbl, String authCode, Date loginTimestamp,
			Date logoutTimestamp, char status) {
		this.userAuthCodeId = userAuthCodeId;
		this.userTbl = userTbl;
		this.authCode = authCode;
		this.loginTimestamp = loginTimestamp;
		this.logoutTimestamp = logoutTimestamp;
		this.status = status;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "USER_AUTH_CODE_ID", unique = true, nullable = false)
	public int getUserAuthCodeId() {
		return this.userAuthCodeId;
	}

	public void setUserAuthCodeId(int userAuthCodeId) {
		this.userAuthCodeId = userAuthCodeId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "USER_ID", nullable = false)
	public UserTbl getUserTbl() {
		return this.userTbl;
	}

	public void setUserTbl(UserTbl userTbl) {
		this.userTbl = userTbl;
	}

	@Column(name = "AUTH_CODE", nullable = false, length = 32)
	public String getAuthCode() {
		return this.authCode;
	}

	public void setAuthCode(String authCode) {
		this.authCode = authCode;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LOGIN_TIMESTAMP", nullable = false, length = 23)
	public Date getLoginTimestamp() {
		return this.loginTimestamp;
	}

	public void setLoginTimestamp(Date loginTimestamp) {
		this.loginTimestamp = loginTimestamp;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LOGOUT_TIMESTAMP", nullable = false, length = 23)
	public Date getLogoutTimestamp() {
		return this.logoutTimestamp;
	}

	public void setLogoutTimestamp(Date logoutTimestamp) {
		this.logoutTimestamp = logoutTimestamp;
	}

	@Column(name = "STATUS", nullable = false, length = 1)
	public char getStatus() {
		return this.status;
	}

	public void setStatus(char status) {
		this.status = status;
	}

}
