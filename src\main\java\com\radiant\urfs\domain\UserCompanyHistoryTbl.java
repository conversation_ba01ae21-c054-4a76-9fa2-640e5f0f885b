package com.radiant.urfs.domain;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "USER_COMPANY_HISTORY_TBL")
public class UserCompanyHistoryTbl  implements java.io.Serializable {

	private int userCompanyHistoryId;
	private UserTbl userTbl;
	private UserLicenceDetailsTbl userLicenceDetailsTbl;
	private Integer deactivatedYear;
	private char activeFlag;
	private Date lastModifiedDatetime;
	private UserTbl lastModifiedBy;
	
	public UserCompanyHistoryTbl() {
		
	}
	
	public UserCompanyHistoryTbl(int userCompanyHistoryId, UserTbl userTbl, UserLicenceDetailsTbl userLicenceDetailsTbl,
			Integer deactivatedYear, char activeFlag, Date lastModifiedDatetime, UserTbl lastModifiedBy) {
		super();
		this.userCompanyHistoryId = userCompanyHistoryId;
		this.userTbl = userTbl;
		this.userLicenceDetailsTbl = userLicenceDetailsTbl;
		this.deactivatedYear = deactivatedYear;
		this.activeFlag = activeFlag;
		this.lastModifiedDatetime = lastModifiedDatetime;
		this.lastModifiedBy = lastModifiedBy;
	}
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "USER_COMPANY_HISTORY_ID", unique = true, nullable = false)
	public int getUserCompanyHistoryId() {
		return userCompanyHistoryId;
	}
	public void setUserCompanyHistoryId(int userCompanyHistoryId) {
		this.userCompanyHistoryId = userCompanyHistoryId;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "USER_ID", nullable = false)
	public UserTbl getUserTbl() {
		return userTbl;
	}
	public void setUserTbl(UserTbl userTbl) {
		this.userTbl = userTbl;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "USER_LICENCE_DETAILS_ID", nullable = false)
	public UserLicenceDetailsTbl getUserLicenceDetailsTbl() {
		return userLicenceDetailsTbl;
	}
	public void setUserLicenceDetailsTbl(UserLicenceDetailsTbl userLicenceDetailsTbl) {
		this.userLicenceDetailsTbl = userLicenceDetailsTbl;
	}
	
	@Column(name = "DEACTIVATED_YEAR")
	public Integer getDeactivatedYear() {
		return deactivatedYear;
	}
	public void setDeactivatedYear(Integer deactivatedYear) {
		this.deactivatedYear = deactivatedYear;
	}
	
	@Column(name = "ACTIVE_FLAG", nullable = false, length = 1)
	public char getActiveFlag() {
		return activeFlag;
	}
	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_MODIFIED_DATETIME", nullable = false, length = 23)
	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}
	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LAST_MODIFIED_BY", nullable = false)
	public UserTbl getLastModifiedBy() {
		return lastModifiedBy;
	}
	public void setLastModifiedBy(UserTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}
}
