package com.radiant.urfs.service.impl;


import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.radiant.urfs.domain.UserSBRoleTbl;
import com.radiant.urfs.repository.UserRoleRepository;
import com.radiant.urfs.service.UserRoleService;

@Service
public class UserRoleServiceImpl implements UserRoleService {

    private final UserRoleRepository userRoleRepository;

    @Autowired
    public UserRoleServiceImpl(UserRoleRepository userRoleRepository) {
        this.userRoleRepository = userRoleRepository;
    }

    @Override
    public List<UserSBRoleTbl> getRolesByUserId(Integer userId) {
        return userRoleRepository.findByUser_UserId(userId);
    }
}

