package com.radiant.urfs.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;

import com.radiant.urfs.domain.UserTbl;

import java.util.Date;

@Entity
@Table(name = "USER_LOGIN_TBL")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserSBLoginTbl {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "USER_LOGIN_ID")
	private Integer userLoginId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "USER_ID")
	private UserSBTbl userId;

	@Column(name = "USERNAME", nullable = false, length = 20, unique = true)
	private String username;

	@Column(name = "PASSWORD", length = 60)
	private String password;

	@Column(name = "LAST_LOGIN_DATE")
	@Temporal(TemporalType.TIMESTAMP)
	private Date lastLoginDate;

	@Column(name = "LAST_PWD_CHANGE_DATE")
	@Temporal(TemporalType.TIMESTAMP)
	private Date lastPwdChangeDate;

	@Column(name = "IP_ADDRESS", length = 20)
	private String ipAddress;

	@Column(name = "LAST_MODIFIED_DATETIME", nullable = false)
	@Temporal(TemporalType.TIMESTAMP)
	private Date lastModifiedDatetime;
//
//    @Column(name = "LAST_MODIFIED_BY", nullable = false)
//    private Integer lastModifiedBy;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LAST_MODIFIED_BY", nullable = false)
	private UserSBTbl lastModifiedBy;
	
	

	@Column(name = "ACTIVE_FLAG", nullable = false, length = 1)
	private char activeFlag;

	@Column(name = "RESET_PASSWORD_TOKEN", length = 255)
	private String resetPasswordToken;
	

	public Integer getUserLoginId() {
		return userLoginId;
	}

	public void setUserLoginId(Integer userLoginId) {
		this.userLoginId = userLoginId;
	}

	public UserSBTbl getUserId() {
		return userId;
	}

	public void setUserId(UserSBTbl userId) {
		this.userId = userId;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public Date getLastLoginDate() {
		return lastLoginDate;
	}

	public void setLastLoginDate(Date lastLoginDate) {
		this.lastLoginDate = lastLoginDate;
	}

	public Date getLastPwdChangeDate() {
		return lastPwdChangeDate;
	}

	public void setLastPwdChangeDate(Date lastPwdChangeDate) {
		this.lastPwdChangeDate = lastPwdChangeDate;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public UserSBTbl getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(UserSBTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public String getResetPasswordToken() {
		return resetPasswordToken;
	}

	public void setResetPasswordToken(String resetPasswordToken) {
		this.resetPasswordToken = resetPasswordToken;
	}

}