package com.radiant.urfs.dao;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Order;
import org.hibernate.jpa.HibernateEntityManagerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;

import com.radiant.urfs.exception.BviReturnsException;

public abstract class PrudentialReturnsDao<PK extends Serializable, T> {

	@Autowired
	SessionFactory sessionFactory;
	private final Class<T> persistentClass;

	//========================================================================
	
	@SuppressWarnings("unchecked")
	public PrudentialReturnsDao() {
		this.persistentClass = (Class<T>) ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments()[1];
	}

	//========================================================================
	
	protected Session getSession() {
		Session session = null;
		try {
			session = sessionFactory.getCurrentSession();
		} catch (HibernateException he) {
			session = sessionFactory.openSession();
		}
		return session;
		 
	}
	
	@Bean  
	public SessionFactory sessionFactory(HibernateEntityManagerFactory hemf){  
	    return hemf.getSessionFactory();  
	}
	//========================================================================
	
	@SuppressWarnings("unchecked")
	public T getByKey(PK key) {
		return (T) getSession().get(persistentClass, key);
	}

	//========================================================================
	
	public T saveOrUpdate(T entity) throws BviReturnsException {
		Session session=null;
		try{
			session = getSession();
			session.getTransaction().begin();
			session.saveOrUpdate(entity);
			session.getTransaction().commit();
			session.flush();
		}
		catch(Exception e) {

			throw new BviReturnsException(e);
		}finally{
			if(session != null)
				session.close();
		}
		return entity;
	}

	
	
	//========================================================================
	
	public void delete(T entity) throws BviReturnsException {
		try {
			getSession().delete(entity);
		} catch (Exception e) {
			throw new BviReturnsException(e);
		}
	}

	//========================================================================
	
	protected Criteria createEntityCriteria() {
		return getSession().createCriteria(persistentClass);
	}

	//========================================================================
	
	@SuppressWarnings("unchecked")
	public List<T> findAll() {
		return createEntityCriteria().list();
	}

	//========================================================================
	
	@SuppressWarnings("unchecked")
	public List<T> getAllBySortingByCreatedDate() {
		return createEntityCriteria().addOrder(Order.desc("createdDate")).list();

	}

	//========================================================================
	
	public void closeSession(Session session) {
		if(session != null)
			session.close();
	}
	
	//========================================================================


}
