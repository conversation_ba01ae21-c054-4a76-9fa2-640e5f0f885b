<?xml version="1.0" encoding="UTF-8"?>
<definitions
	xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:activiti="http://activiti.org/bpmn" targetNamespace="Examples">

	<process id="radiantAppProcess" name="Radiant App Process">
		<startEvent id="theStart" />
		<sequenceFlow sourceRef="theStart" targetRef="userTask" />

		<userTask id="userTask" name="User Registration" activiti:assignee="${user.admin}">
			<documentation>
				Once user submit the form admin needs to approve it.
			</documentation>
		</userTask>
		
		  <sequenceFlow id='inspectorExclusiveFlow' sourceRef='userTask' targetRef='inspectorExclusiveGw' />
		<serviceTask id="inspectorServiceTask" name="Inspector Execution Check" activiti:class="com.radiant.urfs.service.InspectorServiceTask" />

		<exclusiveGateway id="inspectorExclusiveGw" name="Exclusive Gateway approval" />
		<sequenceFlow id="inspectorApprove" sourceRef="inspectorExclusiveGw" targetRef="inspectorServiceTask">
			<conditionExpression xsi:type="tFormalExpression">${userRequest.approve}
			</conditionExpression>
		</sequenceFlow>
		
		<sequenceFlow id="inspectorReject" sourceRef="inspectorExclusiveGw" targetRef="theEnd">
			<conditionExpression xsi:type="tFormalExpression">${!userRequest.approve}
			</conditionExpression>
		</sequenceFlow>

		<sequenceFlow sourceRef="inspectorServiceTask" targetRef="inspectorTask" />
		
		<userTask id="inspectorTask" name="Inspector Task">
			<documentation>
				Admin approved user request form.
			</documentation>
		</userTask>

		<!-- <userTask id="inspectorTask" name="Inspector Task" activiti:assignee="${user.manager}">
			<documentation>
				Manager has to review the user application which is approved by admin.
			</documentation>
		</userTask>
		
        <sequenceFlow id='managerExclusiveFlow' sourceRef='inspectorTask' targetRef='managerExclusiveGw' />
        
		<serviceTask id="managerServiceTask" name="Manager Execution Check" activiti:class="com.radiant.urfs.service.ManagerServiceTask" />
		
		<exclusiveGateway id="managerExclusiveGw" name="Exclusive Gateway approval" />
		
		<sequenceFlow id="managerApprove" sourceRef="managerExclusiveGw" targetRef="managerServiceTask">
			<conditionExpression xsi:type="tFormalExpression">${userRequest.approve}
			</conditionExpression>
		</sequenceFlow>
		
		<sequenceFlow id="managerReject" sourceRef="managerExclusiveGw" targetRef="theEnd">
			<conditionExpression xsi:type="tFormalExpression">${!userRequest.approve}
			</conditionExpression>
		</sequenceFlow>
		
        <sequenceFlow sourceRef="managerServiceTask" targetRef="managerTask" />
		   <userTask id="managerTask" name="Manager Task">
			<documentation>
				Manager approved user request form.
			</documentation>
		</userTask>		 -->
 
		<sequenceFlow sourceRef="inspectorTask"	targetRef="notification" />

		<scriptTask id="notification" name="final Notice" scriptFormat="groovy">
			<script>
				println 'Work flow completed.'
			</script>
		</scriptTask>

		<sequenceFlow sourceRef="notification" targetRef="theEnd" />
		<endEvent id="theEnd" />
	</process>

</definitions>