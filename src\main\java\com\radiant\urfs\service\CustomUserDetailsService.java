package com.radiant.urfs.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.radiant.urfs.domain.UserSBLoginTbl;
import com.radiant.urfs.repository.UserSBLoginRepository;




@Service
public class CustomUserDetailsService implements UserDetailsService {

	protected final Log LOGGER = LogFactory.getLog(getClass());

	@Autowired
	private UserSBLoginRepository userRepository;

	@Override
	//@Transactional(readOnly = true)
	public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
		Optional<UserSBLoginTbl> userOpt = userRepository.findByUsername(username);
		if (!userOpt.isPresent()) {
			throw new UsernameNotFoundException("Username not found or User registration has been rejected");
		}
		UserSBLoginTbl users=userOpt.get();
		if(users.getActiveFlag() == 'N') {
			throw new UsernameNotFoundException("User registration has been rejected");
		}
		if(users.getActiveFlag() == ' ') {
			throw new UsernameNotFoundException("User registration is waiting for approval");
		}
		return new org.springframework.security.core.userdetails.User(users.getUsername(), users.getPassword(),
				getGrantedAuthorities(users));
	}
	private List<GrantedAuthority> getGrantedAuthorities(UserSBLoginTbl users) {
		List<GrantedAuthority> authorities = new ArrayList<GrantedAuthority>();
		//authorities.add(new SimpleGrantedAuthority(users.getRoles().getRoleName()));
		return authorities;
	}
}
