package com.radiant.urfs.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import com.radiant.urfs.domain.UserLoginTbl;

@Repository
public interface UserRepository extends CrudRepository<UserLoginTbl, Integer> {

	//public User findByUserNameforLogin(String username) throws UsernameNotFoundException;

	public Optional<UserLoginTbl> findByUsername(String username);


	public Optional<UserLoginTbl> findByUserLoginId(Integer userLoginId);

	public String existsByUsername(String username);
	
//	public User findByUserName(String username);

}
