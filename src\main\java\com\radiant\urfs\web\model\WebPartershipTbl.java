package com.radiant.urfs.web.model;

import java.util.Date;

import com.radiant.urfs.domain.PartnershipTypeTbl;

public class WebPartershipTbl {

	

	private int entityPartnershipId;

	private String partnerCompanyName;

    private Date partnershipEndDate;

	private char activeFlag;

	private Date lastModifiedDatetime;

	private WebUser lastModifiedBy;
	
    private String partnershipDescription;
    
    private WebPartnershipTypeTbl partnershipType;
    
    public WebPartnershipTypeTbl getPartnershipType() {
		return partnershipType;
	}

	public void setPartnershipType(WebPartnershipTypeTbl partnershipType) {
		this.partnershipType = partnershipType;
	}

	public int getEntityPartnershipId() {
		return entityPartnershipId;
	}

	public void setEntityPartnershipId(int entityPartnershipId) {
		this.entityPartnershipId = entityPartnershipId;
	}

	public String getPartnerCompanyName() {
		return partnerCompanyName;
	}

	public void setPartnerCompanyName(String partnerCompanyName) {
		this.partnerCompanyName = partnerCompanyName;
	}

	
	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public WebUser getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(WebUser lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}

	public Date getPartnershipEndDate() {
		return partnershipEndDate;
	}

	public void setPartnershipEndDate(Date partnershipEndDate) {
		this.partnershipEndDate = partnershipEndDate;
	}

	public String getPartnershipDescription() {
		return partnershipDescription;
	}

	public void setPartnershipDescription(String partnershipDescription) {
		this.partnershipDescription = partnershipDescription;
	}

	
	
}
