package com.radiant.urfs.exception;

public class ActivitiUserRegistrationException extends RuntimeException {
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public ActivitiUserRegistrationException(String message) {
        super(message);
    }
	
	public ActivitiUserRegistrationException() {
		super();
	}

	public ActivitiUserRegistrationException(String message, Throwable cause,
			boolean enableSuppression, boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}

	public ActivitiUserRegistrationException(String message, Throwable cause) {
		super(message, cause);
	}

	public ActivitiUserRegistrationException(Throwable cause) {
		super(cause);
	}
}
