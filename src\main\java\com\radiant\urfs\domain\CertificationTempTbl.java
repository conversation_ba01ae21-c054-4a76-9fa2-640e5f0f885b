package com.radiant.urfs.domain;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)

@Entity
@Table(name = "ENTITY_CERTIFICATION_TEMP_TBL")
public class CertificationTempTbl implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ENTITY_CERTIFICATION_TEMP_ID")
    private int entityCertificationTempId;

    @ManyToOne
    @JoinColumn(name = "ENTITY_CERTIFICATION_ID", referencedColumnName = "ENTITY_CERTIFICATION_ID")
    private CertificationTbl entityCertification;

    @ManyToOne
    @JoinColumn(name = "ENTITY_TEMP_ID", referencedColumnName = "ENTITY_TEMP_ID")
    private EntityTempTbl entityTemp;

    @Column(name = "CERTIFICATION_NAME", length = 80)  
    private String certificationName;

    @Column(name = "CERTIFICATION_DATE")
    @Temporal(TemporalType.DATE)
    private Date certificationDate;

    @Column(name = "CERTIFICATION_EXPIRY_DATE")
    @Temporal(TemporalType.DATE)
    private Date certificationExpiryDate;

    @Column(name = "CERTIFICATION_ISSUING_BODY", length = 80) 
    private String certificationIssuingBody;

    @Column(name = "ACTIVE_FLAG", length = 1)
    private char activeFlag;

    @Column(name = "LAST_MODIFIED_DATETIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastModifiedDatetime;

    @ManyToOne
    @JoinColumn(name = "LAST_MODIFIED_BY", referencedColumnName = "USER_ID")
    private UserSBTbl lastModifiedBy;

    // Getters and Setters
    public int getEntityCertificationTempId() {
        return entityCertificationTempId;
    }

    public void setEntityCertificationTempId(int entityCertificationTempId) {
        this.entityCertificationTempId = entityCertificationTempId;
    }

    public CertificationTbl getEntityCertification() {
        return entityCertification;
    }

    public void setEntityCertification(CertificationTbl entityCertification) {
        this.entityCertification = entityCertification;
    }

    public EntityTempTbl getEntityTemp() {
        return entityTemp;
    }

    public void setEntityTemp(EntityTempTbl entityTemp) {
        this.entityTemp = entityTemp;
    }

    public String getCertificationName() {
        return certificationName;
    }

    public void setCertificationName(String certificationName) {
        this.certificationName = certificationName;
    }

    public Date getCertificationDate() {
        return certificationDate;
    }

    public void setCertificationDate(Date certificationDate) {
        this.certificationDate = certificationDate;
    }

    public Date getCertificationExpiryDate() {
        return certificationExpiryDate;
    }

    public void setCertificationExpiryDate(Date certificationExpiryDate) {
        this.certificationExpiryDate = certificationExpiryDate;
    }

    public String getCertificationIssuingBody() {
        return certificationIssuingBody;
    }

    public void setCertificationIssuingBody(String certificationIssuingBody) {
        this.certificationIssuingBody = certificationIssuingBody;
    }

    public char getActiveFlag() {
        return activeFlag;
    }

    public void setActiveFlag(char activeFlag) {
        this.activeFlag = activeFlag;
    }

    public Date getLastModifiedDatetime() {
        return lastModifiedDatetime;
    }

    public void setLastModifiedDatetime(Date lastModifiedDatetime) {
        this.lastModifiedDatetime = lastModifiedDatetime;
    }

	public UserSBTbl getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(UserSBTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}

   
}
