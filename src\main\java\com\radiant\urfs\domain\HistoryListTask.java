package com.radiant.urfs.domain;

import java.util.List;

public class HistoryListTask {
	private List<HistoryTaskDetails> approvedList;
	private List<HistoryTaskDetails> rejectedList;
	private List<HistoryTaskDetails> pendingList;

	public List<HistoryTaskDetails> getApprovedList() {
		return approvedList;
	}

	public void setApprovedList(List<HistoryTaskDetails> approvedList) {
		this.approvedList = approvedList;
	}

	public List<HistoryTaskDetails> getRejectedList() {
		return rejectedList;
	}

	public void setRejectedList(List<HistoryTaskDetails> rejectedList) {
		this.rejectedList = rejectedList;
	}

	public List<HistoryTaskDetails> getPendingList() {
		return pendingList;
	}

	public void setPendingList(List<HistoryTaskDetails> pendingList) {
		this.pendingList = pendingList;
	}

	public HistoryListTask(List<HistoryTaskDetails> approvedList, List<HistoryTaskDetails> rejectedList,
			List<HistoryTaskDetails> pendingList) {
		super();
		this.approvedList = approvedList;
		this.rejectedList = rejectedList;
		this.pendingList = pendingList;
	}

}
