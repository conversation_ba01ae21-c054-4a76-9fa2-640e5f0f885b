package com.radiant.urfs.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;

import com.radiant.urfs.domain.UserTbl;

import java.util.Date;

@Entity
@Table(name = "ENTITY_TBL")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityTbl {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ENTITY_ID")
	private Integer entityId;
	
//	@Column(name = "ENTITY_NUMBER", length = 15)
//	private String entityNumber;
	
	@Column(name = "ENTITY_NAME", length = 255)
	private String entityName;
	
	@Column(name = "PHONE", length = 15)
	private String phone;
	
	@Column(name = "URL", length = 255)
	private String url;
	
//	@ManyToOne(fetch = FetchType.LAZY)
//	@JoinColumn(name = "PAYMENTS_ID", nullable = false)
//	private PaymentsTbl paymentId;
	
	   
    @Column(name = "AUTH_IND", length = 1)
    private char authInd;
	
	@Column(name = "ACTIVE_FLAG", nullable = false, length = 1)
	private char activeFlag;
	
	@Column(name = "LAST_MODIFIED_DATETIME", nullable = false)
	@Temporal(TemporalType.TIMESTAMP)
	private Date lastModifiedDatetime;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LAST_MODIFIED_BY", nullable = false)
	private UserSBTbl lastModifiedBy;
	
	@Column(name = "INCORPORATION_DATE")
	@Temporal(TemporalType.DATE)
	private Date incorporationDate;
	
	
	@Column(name = "NUM_EMPLOYEES")
	private String numEmployees;
	
	@Column(name = "ADDRESS", length = 100)
	private String address;
	
	@Column(name = "COMPANY_PROFILE", length = 300)
	private String companyProfile;
	
//	@Lob
//	@Column(name = "LOGO", nullable = false)
//	private byte[] logo;
//	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "COMPANY_TYPE_ID")
	private CompanyTypeTbl companyTypeId;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "BUSINESS_PURPOSE_ID")
	private BusinessPurposeTbl businessPurposeId;
	
//	@ManyToOne(fetch = FetchType.LAZY)
//	@JoinColumn(name = "TRANSACTION_TYPE_ID", nullable = true)
//	private TransactionTypeTbl transactionTypeId;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "BUSINESS_OPERATION_ID")
	private BusinessOperationTypeTbl businessOperationId;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "USER_ID")
	private UserSBTbl userId;
	
	@Column(name = "SBR_NUMBER")
	private String sbrNumber;
	
//	@ManyToOne(fetch = FetchType.LAZY)
//	@JoinColumn(name = "DOCUMENT_ID")
//	private DocumentTbl documentTblByDocumentAddnId;
	
	@Column(name = "EMAIL_ADDRESS", length = 255)
	private String emailAddress;
	
	@Column(name = "EIN")
	private String FederalEmployerIdentificationNumber;
	
	@Column(name = "INCORPORATION_STATE")
	private String StateOfIncorporation;
	
	@Column(name = "BUSINESS_KEYWORDS")
	private String BusinessKeywords;
	
	@Column(name = "AREAS_INTERESTS")
	private String AreasInterests;
	
	@Column(name = "REVENUE")
	private String estimatedAnnualRevenue;
	
	@Column(name = "OWNER_FIRST_NAME")
	private String OwnerFirstName;
	
	@Column(name = "OWNER_LAST_NAME")
	private String OwnerLastName;
	
	@Column(name = "OWNER_PHONE")
	private String OwnerPhone;
	
	@Column(name = "OWNER_EMAIL")
	private String OwnerEmail;
	
	@Column(name = "SIZE_COMPANY_IND", length = 1)
	private Character sizeCompanyInd;

	
	@Column(name = "LOG_IND", length = 1)
	private Character logInd;
	
	public Integer getEntityId() {
		return entityId;
	}

	public void setEntityId(Integer entityId) {
		this.entityId = entityId;
	}

//	public String getEntityNumber() {
//		return entityNumber;
//	}
//
//	public void setEntityNumber(String entityNumber) {
//		this.entityNumber = entityNumber;
//	}

	public String getEntityName() {
		return entityName;
	}

	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char active) {
		this.activeFlag = active;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public UserSBTbl getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(UserSBTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}

	public Date getIncorporationDate() {
		return incorporationDate;
	}

	public void setIncorporationDate(Date incorporationDate) {
		this.incorporationDate = incorporationDate;
	}

	
	

	public String getNumEmployees() {
		return numEmployees;
	}

	public void setNumEmployees(String numEmployees) {
		this.numEmployees = numEmployees;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getCompanyProfile() {
		return companyProfile;
	}

	public void setCompanyProfile(String companyProfile) {
		this.companyProfile = companyProfile;
	}

//	public byte[] getLogo() {
//		return logo;
//	}
//
//	public void setLogo(byte[] logo) {
//		this.logo = logo;
//	}

	public UserSBTbl getUserId() {
		return userId;
	}

	public void setUserId(UserSBTbl userId) {
		this.userId = userId;
	}

//	public PaymentsTbl getPaymentId() {
//		return paymentId;
//	}
//
//	public void setPaymentId(PaymentsTbl paymentId) {
//		this.paymentId = paymentId;
//	}

	public CompanyTypeTbl getCompanyTypeId() {
		return companyTypeId;
	}

	public void setCompanyTypeId(CompanyTypeTbl companyTypeId) {
		this.companyTypeId = companyTypeId;
	}

	public BusinessPurposeTbl getBusinessPurposeId() {
		return businessPurposeId;
	}

	public void setBusinessPurposeId(BusinessPurposeTbl businessPurposeId) {
		this.businessPurposeId = businessPurposeId;
	}

//	public TransactionTypeTbl getTransactionTypeId() {
//		return transactionTypeId;
//	}
//
//	public void setTransactionTypeId(TransactionTypeTbl transactionTypeId) {
//		this.transactionTypeId = transactionTypeId;
//	}

	public BusinessOperationTypeTbl getBusinessOperationId() {
		return businessOperationId;
	}

	public void setBusinessOperationId(BusinessOperationTypeTbl businessOperationId) {
		this.businessOperationId = businessOperationId;
	}

	public String getSbrNumber() {
		return sbrNumber;
	}

	public void setSbrNumber(String sbrNumber) {
		this.sbrNumber = sbrNumber;
	}

//	public DocumentTbl getDocumentTblByDocumentAddnId() {
//		return documentTblByDocumentAddnId;
//	}
//
//	public void setDocumentTblByDocumentAddnId(DocumentTbl documentTblByDocumentAddnId) {
//		this.documentTblByDocumentAddnId = documentTblByDocumentAddnId;
//	}

	public String getEmailAddress() {
		return emailAddress;
	}

	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}

	public String getFederalEmployerIdentificationNumber() {
		return FederalEmployerIdentificationNumber;
	}

	public void setFederalEmployerIdentificationNumber(String federalEmployerIdentificationNumber) {
		FederalEmployerIdentificationNumber = federalEmployerIdentificationNumber;
	}

	public String getStateOfIncorporation() {
		return StateOfIncorporation;
	}

	public void setStateOfIncorporation(String stateOfIncorporation) {
		StateOfIncorporation = stateOfIncorporation;
	}

	public String getBusinessKeywords() {
		return BusinessKeywords;
	}

	public void setBusinessKeywords(String businessKeywords) {
		BusinessKeywords = businessKeywords;
	}

	public String getAreasInterests() {
		return AreasInterests;
	}

	public void setAreasInterests(String areasInterests) {
		AreasInterests = areasInterests;
	}

	

	public String getEstimatedAnnualRevenue() {
		return estimatedAnnualRevenue;
	}

	public void setEstimatedAnnualRevenue(String estimatedAnnualRevenue) {
		this.estimatedAnnualRevenue = estimatedAnnualRevenue;
	}

	public String getOwnerFirstName() {
		return OwnerFirstName;
	}

	public void setOwnerFirstName(String ownerFirstName) {
		OwnerFirstName = ownerFirstName;
	}

	public String getOwnerLastName() {
		return OwnerLastName;
	}

	public void setOwnerLastName(String ownerLastName) {
		OwnerLastName = ownerLastName;
	}

	public String getOwnerPhone() {
		return OwnerPhone;
	}

	public void setOwnerPhone(String ownerPhone) {
		OwnerPhone = ownerPhone;
	}

	public String getOwnerEmail() {
		return OwnerEmail;
	}

	public void setOwnerEmail(String ownerEmail) {
		OwnerEmail = ownerEmail;
	}

	public Character getSizeCompanyInd() {
		return sizeCompanyInd;
	}

	public void setSizeCompanyInd(Character sizeCompanyInd) {
		this.sizeCompanyInd = sizeCompanyInd;
	}

	public char getAuthInd() {
		return authInd;
	}

	public void setAuthInd(char authInd) {
		this.authInd = authInd;
	}

	public Character getLogInd() {
		return logInd;
	}

	public void setLogInd(Character logInd) {
		this.logInd = logInd;
	}
	
	
}
