package com.radiant.urfs.domain;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

// Generated Feb 23, 2021 12:15:49 PM by Hibernate Tools 4.3.1.Final


/**
 * UserTbl generated by hbm2java
 */
@Entity
@Table(name = "USER_TBL", catalog = "dbo")
public class UserTbl implements java.io.Serializable {

	private int userId;
	private String firstName;
	private String lastName;
	private char activeFlag;
	private Date lastModifiedDatetime;
//	private Role roles;

	public UserTbl() {
	}

	public UserTbl(int userId, String firstName, String lastName, char activeFlag, Date lastModifiedDatetime) {
		this.userId = userId;
		this.firstName = firstName;
		this.lastName = lastName;
		this.activeFlag = activeFlag;
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	@Id
	//@GeneratedValue(strategy = IDENTITY)
	@Column(name = "USER_ID", unique = true, nullable = false)
	public int getUserId() {
		return this.userId;
	}

	public void setUserId(int userId) {
		this.userId = userId;
	}

	@Column(name = "FIRST_NAME", nullable = false, length = 60)
	public String getFirstName() {
		return this.firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	@Column(name = "LAST_NAME", nullable = false, length = 60)
	public String getLastName() {
		return this.lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	@Column(name = "ACTIVE_FLAG", nullable = false, length = 1)
	public char getActiveFlag() {
		return this.activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_MODIFIED_DATETIME", nullable = false, length = 23)
	public Date getLastModifiedDatetime() {
		return this.lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}
	
//	@ManyToOne(fetch = FetchType.LAZY)
//	@JoinColumn(name = "ROLE_ID")
//	public Role getRoles() {
//		return roles;
//	}
//
//	public void setRoles(Role roles) {
//		this.roles = roles;
//	}
}
