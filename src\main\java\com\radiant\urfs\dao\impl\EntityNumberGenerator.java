package com.radiant.urfs.dao.impl;
import java.util.Random;

public class EntityNumberGenerator {

    // Generate an entity number starting with "SBR" followed by 8 random digits
    public static String generateSBRNumber() {
        Random random = new Random();
        // Generate 8 random digits
        int randomNumber = 10000000 + random.nextInt(90000000); // 8-digit random number
        // Format the number with leading zeros if necessary
        String formattedNumber = String.format("%08d", randomNumber);
        // Combine with prefix
        return "SBR" + formattedNumber;
    }
}
