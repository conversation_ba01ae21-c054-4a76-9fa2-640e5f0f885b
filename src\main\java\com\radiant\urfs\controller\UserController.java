
package com.radiant.urfs.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.servlet.ServletContext;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.radiant.urfs.common.Constants;
import com.radiant.urfs.common.UrlConstants;
import com.radiant.urfs.config.JwtUtil;
import com.radiant.urfs.domain.ActivitiResponse;
import com.radiant.urfs.domain.EmailRequest;

import com.radiant.urfs.exception.BviReturnsException;
import com.radiant.urfs.service.UserRoleService;
import com.radiant.urfs.service.UserService;
import com.radiant.urfs.service.impl.UserServiceImpl;
import com.radiant.urfs.web.model.SBDetails;
import com.radiant.urfs.web.model.WebMessage;
import com.radiant.urfs.web.model.WebPassword;
import com.radiant.urfs.web.model.WebUser;
import com.radiant.urfs.web.model.WebUserData;
import com.radiant.urfs.domain.UserSBLoginTbl;
import com.radiant.urfs.domain.UserSBRoleTbl;

@RestController
@CrossOrigin
@RequestMapping(value = UrlConstants.User.API_BASE)
//@PropertySource(value = "classpath:/appConf.properties", ignoreResourceNotFound = true)
public class UserController {

	private static final Logger log = Logger.getLogger(UserController.class);
	String className = "UserController";
	@Autowired
	UserService userService;

	@Autowired
	UserServiceImpl userServiceImpl;

	@Autowired
	private UserRoleService userRoleService;

	@Autowired
	ResourceLoader resourceLoader;

	@Autowired
	ServletContext context;

	@Autowired
	JwtUtil jwtUtil;






	
	@GetMapping(value = UrlConstants.User.GET_DETAILS_BY_USERNAME)
	public ResponseEntity<?> getDetailsByUserName(@RequestParam(value = "userName") String userName)
			throws BviReturnsException {
		String logTag = "UserController : getDetailsByUserName()" + userName;
		log.info("Entered into : " + logTag);
		try {
			return userService.getDetailsByUserName(userName);
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while getDetailsByUserName: " + userName + " " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	

	@GetMapping(value = UrlConstants.User.GET_ALL_USERS)
	public ResponseEntity<?> getAllUsers() throws BviReturnsException {
//	log.info("request body : ");
		String logTag = "getAllUsers(): ";
		log.info("Entering into getAllUsers() : ");
		try {
			return userService.getAllUsers();
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while getAllUsers. " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	

	@PostMapping(value = UrlConstants.User.USER_REGISTRATION)
	public ResponseEntity<?> userRegistration(@RequestBody WebUser userSignupTbl) throws BviReturnsException {
		log.info("request body : " + userSignupTbl);
		String logTag = "userRegistration(): ";
		log.info("Entering into userRegistration() : ");
		try {
			/*
			 * ResponseEntity<?> accessResponse = userService.userAccessValidation();
			 * if(accessResponse.getStatusCode() == HttpStatus.BAD_REQUEST) return
			 * accessResponse;
			 */
			return userService.registerUser(userSignupTbl);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while  when trying  adding User to database." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	
	
//***=================================SSB_APIS====================================****//

	@PostMapping(value = UrlConstants.User.LOGIN)
	public ResponseEntity<?> login(@RequestParam String username, @RequestParam String password) {
		Map<String, Object> response = new HashMap<>();
		try {

			Optional<UserSBLoginTbl> optionalUser = userService.authenticate(username, password);

			if (optionalUser.isPresent()) {
				UserSBLoginTbl userData = optionalUser.get();

				// Check user status
				if (userData.getActiveFlag() == Constants.PENDING) {
					return new ResponseEntity<>(new WebMessage(Constants.User.USER_PENDING_APPROVAL),
							HttpStatus.FORBIDDEN);
				}
				if (userData.getActiveFlag() == Constants.INACTIVE) {
					return new ResponseEntity<>(new WebMessage(Constants.User.USER_INACTIVE), HttpStatus.FORBIDDEN);
				}

				// Generate JWT token
				String token = jwtUtil.generateToken(userData.getUsername());

				// Fetch the role code associated with the user
				String roleCode = null;
				// Assuming you have a service method to get roles for a user
				List<UserSBRoleTbl> userRoles = userRoleService.getRolesByUserId(userData.getUserId().getUserId());
				if (!userRoles.isEmpty()) {
					roleCode = userRoles.get(0).getRole().getRoleCode(); // Get the first role code
				}
				// Fetch the sbrNumber using entityTempId
//		            Integer entityTempId = userData.getEntityTempId(); // Assuming you have a method to get entityTempId
//		            String sbrNumber = empTempService.getSbrNumberByEntityTempId(entityTempId);

				// Return token and user details
				response.put("message", "Login successful");
				response.put("token", token);
				response.put("username", userData.getUsername());
				response.put("userId", userData.getUserId().getUserId());
				response.put("firstName", userData.getUserId().getFirstName());
				response.put("lastName", userData.getUserId().getLastName());
				response.put("activeFlag", userData.getActiveFlag());
				response.put("roleCode", roleCode);

				return ResponseEntity.ok(response);
			} else {
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid username or password");
			}
		} catch (BviReturnsException e) {
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Internal server error");
		}
	}

	@PostMapping(value = UrlConstants.User.SAVE_NEW_USER, consumes = "multipart/form-data")
	public @ResponseBody ResponseEntity<?> saveNewUserRegistration(@RequestParam("sbDetails") String sbDetailsJson,
			@RequestParam(value = "addndoc", required = false) MultipartFile addndoc) throws BviReturnsException {
		String logTag = "saveNewUserRegistration";
		try {
			// Convert JSON string to SBDetails object
			ObjectMapper objectMapper = new ObjectMapper();
			SBDetails sbDetails = objectMapper.readValue(sbDetailsJson, SBDetails.class);

			return userService.saveNewUserRegistration(sbDetails, addndoc);
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while saving the UserRegistration: " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@PostMapping(value = UrlConstants.User.APPROVE)
	public ResponseEntity<?> approve(@RequestBody List<Integer> entityTempIds) throws BviReturnsException {
		String logTag = "UserController : approveUser()";
		log.info("Entered into : " + logTag);
		try {
			return userService.approve(entityTempIds);
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while getting approveUser.." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@GetMapping(value = UrlConstants.User.GET_DASHBOARD_LIST)
	public ResponseEntity<?> getDashboardList(@RequestParam char authInd) throws BviReturnsException {
		String logTag = "UserController : getDashboardList()";
		log.info("Entered into : " + logTag);
		try {
			return userService.getDashboardList(authInd);
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while getting getDashboardList.." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@PostMapping(value = UrlConstants.User.REJECT)
	public ResponseEntity<?> reject(@RequestParam(name = "comments") String comments,
			@RequestBody List<Integer> entityTempIds) throws BviReturnsException {
		String logTag = "UserController : rejectUser()";
		log.info("Entered into : " + logTag);
		try {
			return userService.reject(comments, entityTempIds);
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while getting rejectUser.." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@PostMapping(value = UrlConstants.User.AMMENDMENTS)
	public @ResponseBody ResponseEntity<?> ammendments(@RequestParam("sbDetails") String sbDetailsJson,
			@RequestParam(value = "addndoc", required = false) MultipartFile addndoc, @RequestParam Integer userId)
			throws BviReturnsException {
		String logTag = "ammendments";
		try {

			// Convert JSON string to SBDetails object
			ObjectMapper objectMapper = new ObjectMapper();
			SBDetails sbDetails = objectMapper.readValue(sbDetailsJson, SBDetails.class);

			return userService.ammendments(sbDetails, addndoc, userId);

		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while saving the ammendments : " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	

	@GetMapping(value = UrlConstants.User.GET_AMMENDMENTS_DASHBOARD_LIST)
	public ResponseEntity<?> getAmmendmentsDashboardList(@RequestParam Integer userId) throws BviReturnsException {
		String logTag = "UserController : getDashboardList()";
		log.info("Entered into : " + logTag);
		try {
			return userService.getAmmendmentsDashboardList(userId);
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while getting getAmmendmentsDashboardList.." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@GetMapping(value = UrlConstants.User.GET_REGISTRATION_DASHBOARD_LIST)
	public ResponseEntity<?> getRegistrationDashboardList(@RequestParam String sbrNumber) throws BviReturnsException {
		String logTag = "UserController : getRegistrationDashboardList()";
		log.info("Entered into : " + logTag);
		try {
			return userService.getRegistrationDashboardList(sbrNumber);
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while getting RegistrationDashboardList.." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@GetMapping(value = UrlConstants.User.GET_SEARCH_DETAILS)
	public @ResponseBody ResponseEntity<?> getSearchDetails(@RequestParam(required = false) String sbNumber,
			@RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate,
			@RequestParam(required = false) String entityName,
			@RequestParam(required = false) Integer businessOperationId,
			@RequestParam(required = false) String companyHead, @RequestParam(required = false) String url,
			@RequestParam(required = false) String status, @RequestParam(required = false) Integer companyTypeId,
			@RequestParam(required = false) String AreasInterests,
			@RequestParam(required = false) String BusinessKeywords) throws BviReturnsException {
		String logTag = "UserController : getSearchDetails()";
		log.info("Entered into : " + logTag);
		try {
			return userService.getSearchDetails(sbNumber, startDate, endDate, entityName, businessOperationId,
					companyHead, url, status, companyTypeId, AreasInterests, BusinessKeywords);
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while getting getSearchDetails.." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@GetMapping(value = UrlConstants.User.GET_BUSINESS_PURPOSE_DETAILS)
	public @ResponseBody ResponseEntity<?> getBussinessPurposeDetails() throws BviReturnsException {
		String logTag = "UserController : getBussinessPurposeDetails()";
		log.info("Entered into : " + logTag);
		try {
			return userService.getBussinessPurposeDetails();
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while getting BussinessPurposeDetails.." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@GetMapping(value = UrlConstants.User.GET_PARTNERSHIP_DETAILS)
	public @ResponseBody ResponseEntity<?> getPartnershipDetails() throws BviReturnsException {
		String logTag = "UserController : getPartnershipDetails()";
		log.info("Entered into : " + logTag);
		try {
			return userService.getPartnershipDetails();
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while getting PartnershipDetails.." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@GetMapping(value = UrlConstants.User.GET_BUSSINESS_OPERATION_DETAILS)
	public @ResponseBody ResponseEntity<?> getBussinessOperationDetails() throws BviReturnsException {
		String logTag = "UserController : getBussinessOperationDetails()";
		log.info("Entered into : " + logTag);
		try {
			return userService.getBussinessOperationDetails();
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while get BussinessOperationDetails.." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@GetMapping(value = UrlConstants.User.GET_COMPANY_TYPE_DETAILS)
	public @ResponseBody ResponseEntity<?> getCompanyTypeDetails() throws BviReturnsException {
		String logTag = "UserController : getCompanyTypeDetails()";
		log.info("Entered into : " + logTag);
		try {
			return userService.getCompanyTypeDetails();
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while getCompanyTypeDetails.." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@GetMapping(value = UrlConstants.User.CHECK_USERNAME)
	public ResponseEntity<?> checkUsername(@PathVariable("userName") String userName) throws BviReturnsException {
		String logTag = "checkUsername()";
		boolean userNmaeExist;
		ActivitiResponse activitiResponse = new ActivitiResponse();
		try {
			userNmaeExist = userService.checkUsername(userName);
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while checking username exiat or not : " + userName + ": "
					+ e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
		if (userNmaeExist) {
			activitiResponse.setStatus(Constants.FAIL);
			activitiResponse.setMessage(Constants.User.USER_NAME_EXISTS);
		} else {
			activitiResponse.setStatus(Constants.SUCCESS);
			activitiResponse.setMessage(Constants.User.USER_NAME_NOT_EXSISTS);
		}
		return ResponseEntity.ok(activitiResponse);
	}

	@GetMapping(value = UrlConstants.User.GET_PAYMENT_SUMMARY_DETAILS)
	public ResponseEntity<?> getPaymentSummaryDetails(@RequestParam String paymentCode) throws BviReturnsException {

		log.info("UserController" + "getPaymentSummaryDetails" + "Start");

		try {
			return userService.getPaymentSummaryDetails(paymentCode);

		} catch (Exception e) {
			String errMessage = " Exception occurred while verifying gp number ." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@GetMapping(value = UrlConstants.User.GET_TRANSACTIONS_DETAILS)
	public ResponseEntity<?> getTransactionsDetails() throws BviReturnsException {

		log.info("UserController" + "getTransactionsDetails" + "Start");

		try {
			return userService.getTransactionsDetails();

		} catch (Exception e) {
			String errMessage = " Exception occurred while verifying transaction id ." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@GetMapping(value = UrlConstants.User.GET_TRANSACTIONS_DETAILS_USER)
	public ResponseEntity<?> getTransactionsDetailsByUser(@RequestParam Integer lastModifiedBy)
			throws BviReturnsException {

		log.info("UserController" + "getTransactionsDetailsByUser" + "Start");

		try {
			return userService.getTransactionsDetailsByUser(lastModifiedBy);

		} catch (Exception e) {
			String errMessage = " Exception occurred while verifying transaction id ." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@GetMapping(value = UrlConstants.User.GET_USER_DETAILS)
	public ResponseEntity<?> getDetailsByUser(@RequestParam Integer lastModifiedBy) throws BviReturnsException {

		log.info("UserController" + "getDetailsByUser" + "Start");

		try {
			return userService.getDetailsByUser(lastModifiedBy);

		} catch (Exception e) {
			String errMessage = " Exception occurred while verifying username ." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@GetMapping(value = UrlConstants.User.GET_APPROVED_DASHBOARD_LIST)
	public ResponseEntity<?> getApprovedDashboardList() throws BviReturnsException {
		String logTag = "UserController : getApprovedDashboardList()";
		log.info("Entered into : " + logTag);
		try {
			return userService.getApprovedDashboardList();
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while getting getApprovedDashboardList.." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@PostMapping(value = UrlConstants.User.SEND_EMAIL_DETAILS)
    public ResponseEntity<?> sendEmailDetails(@RequestBody EmailRequest emailRequest) {
        try {
            return userService.sendEmailDetails(emailRequest.getComments(), emailRequest.getEntityName(),emailRequest.getSubject());
        } catch (BviReturnsException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }
	@GetMapping(value = UrlConstants.User.ACTIVE_INACTIVE)
	public ResponseEntity<?> activeInactiveUsers() throws BviReturnsException {
		String logTag = "UserController : getApprovedDashboardList()";
		log.info("Entered into : " + logTag);
		try {
			return userService.activeInactiveUsers();
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while getting activeInactiveUsers.." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@RequestMapping(value = UrlConstants.User.ENABLE_DISABLE, method = RequestMethod.GET)
	public ResponseEntity<?> enableDisableUser(@RequestParam Integer userId, @RequestParam String activeFlag,
			@RequestParam String entityName) {
		try {

			return userService.enableDisableUser(activeFlag, userId, entityName);

		} catch (BviReturnsException e) {
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
		}
	}

	@PostMapping(value = UrlConstants.User.FORGOT_PASSWORD)
	public ResponseEntity<?> forgotPassword(@RequestParam("username") String username,
			@RequestParam("emailAddress") String emailAddress) throws BviReturnsException {
		String logTag = "forgotPassword(): ";
		log.info("Entering into forgotPassword() : ");
		log.info("username : " + username);
		log.info("email : " + emailAddress);
		try {
			return userService.forgotPassword(username, emailAddress);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while forgotPassword. " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@PostMapping(value = UrlConstants.User.CHANGE_PASSWORD)
	public ResponseEntity<?> changePassword(@RequestBody WebPassword webPassword) throws BviReturnsException {
		log.info("request body : " + webPassword);
		String logTag = "changePassword(): ";
		log.info("user id: " + webPassword.getUserId());
		log.info("email: " + webPassword.getEmail());
		try {
			return userService.changePassword(webPassword);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while  when trying to changePassword. userId"
					+ webPassword.getUserId() + " " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@PostMapping(value = UrlConstants.User.RESET_PASSWORD)
	public ResponseEntity<?> resetPassword(@RequestParam(value = "token") String token,
			@RequestParam(value = "password") String newPassword) throws BviReturnsException {
		String logTag = "resetPassword(): ";
		log.info("Entering into resetPassword() : ");
		try {
			return userService.resetPassword(token, newPassword);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while resetPassword. " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@PostMapping(value = UrlConstants.User.VALIDATE_OLD_PASSWORD)
	public ResponseEntity<?> validateOldPassword(@RequestBody WebUserData webUserData) throws BviReturnsException {
		// log.info("request body : "+ username);
		String logTag = "validateOldPassword(): ";
		log.info("User Id : " + webUserData.getUserId());
//			log.info("Email : "+webUserData.getEmail());
		log.info("Password : " + webUserData.getPassword());

		try {
			return userService.validateOldPassword(webUserData);
		} catch (Exception e) {
			String errMessage = logTag + "Exception occurred while validateOldPassword. " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}
	// ========================**********=========================//

	@PostMapping(value = UrlConstants.User.REGISTER_NEW_USER)
	public @ResponseBody ResponseEntity<?> registerNewUser(@RequestParam("sbDetails") String sbDetailsJson)
			throws BviReturnsException {
		String logTag = "registerNewUser";
		try {
			// Convert JSON string to SBDetails object
			ObjectMapper objectMapper = new ObjectMapper();
			SBDetails sbDetails = objectMapper.readValue(sbDetailsJson, SBDetails.class);

			return userService.registerNewUser(sbDetails);
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while saving the UserRegistration: " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@PostMapping(value = UrlConstants.User.UPDATE_USER)
	public @ResponseBody ResponseEntity<?> updateUser(@RequestParam("sbDetails") String sbDetailsJson,
			@RequestParam Integer userId) throws BviReturnsException {
		String logTag = "registerNewUser";
		try {
			// Convert JSON string to SBDetails object
			ObjectMapper objectMapper = new ObjectMapper();
			SBDetails sbDetails = objectMapper.readValue(sbDetailsJson, SBDetails.class);

			return userService.updateUser(sbDetails, userId);
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while saving the UserRegistration: " + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@GetMapping(value = UrlConstants.User.GET_REGISTRATION_PENDING_LIST)
	public ResponseEntity<?> getRegistrationPendingList(@RequestParam String sbrNumber) throws BviReturnsException {
		String logTag = "UserController : getRegistrationDashboardList()";
		log.info("Entered into : " + logTag);
		try {
			return userService.getRegistrationPendingList(sbrNumber);
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while getting RegistrationDashboardList.." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}

	@GetMapping(value = UrlConstants.User.GET_APPROVED_REGISTER_LIST)
	public ResponseEntity<?> getApprovedRegisterList() throws BviReturnsException {
		String logTag = "UserController : getApprovedDashboardList()";
		log.info("Entered into : " + logTag);
		try {
			return userService.getApprovedRegisterList();
		} catch (Exception e) {
			String errMessage = logTag + " Exception occurred while getting getApprovedDashboardList.." + e;
			log.error(errMessage, e);
			throw new BviReturnsException(errMessage, e);
		}
	}


	

}
