package com.radiant.urfs.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "PAYPAL_TRANSACTIONS_TBL")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaypalTransactionsTbl {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PAYPAL_TRANSACTIONS_ID")
    private Integer payPalTransactionsId;

    @Column(name = "ENTITY_ID", nullable = false)
    private Integer entityId;

    @Column(name = "PAYPAL_CONFIRMATION_NUMBER", nullable = false, length = 50)
    private String payPalConfirmationNumber;

    @Column(name = "PAYPAL_TRANSACTION_AMT", nullable = false, precision = 18, scale = 0)
    private BigDecimal payPalTransactionAmt;

    @Column(name = "PAYPAL_TRANSACTION_DATETIME", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date payPalTransactionDatetime;

    @Column(name = "PAYPAL_MESSAGE", length = 255)
    private String payPalMessage;

    @Column(name = "LAST_MODIFIED_DATETIME", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastModifiedDatetime;

    @ManyToOne
    @JoinColumn(name = "LAST_MODIFIED_BY", nullable = false)
    private UserSBTbl lastModifiedByUser;

	public Integer getPayPalTransactionsId() {
		return payPalTransactionsId;
	}

	public void setPayPalTransactionsId(Integer payPalTransactionsId) {
		this.payPalTransactionsId = payPalTransactionsId;
	}

	public Integer getEntityId() {
		return entityId;
	}

	public void setEntityId(Integer entityId) {
		this.entityId = entityId;
	}

	public String getPayPalConfirmationNumber() {
		return payPalConfirmationNumber;
	}

	public void setPayPalConfirmationNumber(String payPalConfirmationNumber) {
		this.payPalConfirmationNumber = payPalConfirmationNumber;
	}

	public BigDecimal getPayPalTransactionAmt() {
		return payPalTransactionAmt;
	}

	public void setPayPalTransactionAmt(BigDecimal payPalTransactionAmt) {
		this.payPalTransactionAmt = payPalTransactionAmt;
	}

	public Date getPayPalTransactionDatetime() {
		return payPalTransactionDatetime;
	}

	public void setPayPalTransactionDatetime(Date payPalTransactionDatetime) {
		this.payPalTransactionDatetime = payPalTransactionDatetime;
	}

	public String getPayPalMessage() {
		return payPalMessage;
	}

	public void setPayPalMessage(String payPalMessage) {
		this.payPalMessage = payPalMessage;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public UserSBTbl getLastModifiedByUser() {
		return lastModifiedByUser;
	}

	public void setLastModifiedByUser(UserSBTbl lastModifiedByUser) {
		this.lastModifiedByUser = lastModifiedByUser;
	}

}