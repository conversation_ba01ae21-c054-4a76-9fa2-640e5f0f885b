package com.radiant.urfs.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;


@Entity
@Table(name = "ROLE_TBL", catalog = "dbo")
public class Role implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	@Column(name="ROLE_ID")
	private Integer roleId;
	@Column(name="ROLE_NAME")
	private String roleName;
	@Column(name="ROLE_DESC")
	private String roleDesc;
	//private String active_flag;
	private char activeFlag;
	private Date lastModifiedDatetime;
	
	private Set<UserLoginTbl> users = new HashSet<UserLoginTbl>();
	
	public Role() {}

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	public Integer getRoleId() {
		return roleId;
	}

	public void setRoleId(Integer roleId) {
		this.roleId = roleId;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}
	@Column(name = "ROLE_DESCRIPTION", nullable = false)
	public String getRoleDesc() {
		return roleDesc;
	}

	public void setRoleDesc(String roleDesc) {
		this.roleDesc = roleDesc;
	}
	
	@Column(name = "ACTIVE_FLAG",  length = 1)
	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "roles")
	public Set<UserLoginTbl> getUsers() {
		return users;
	}

	public void setUsers(Set<UserLoginTbl> users) {
		this.users = users;
	}
	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

}
