package com.radiant.urfs.web.model;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

public class EmailDetails {
	private String[] toRecipients;
	private String[] ccRecipients;
	private String subject;
	private String messageContent;
	private String attachmentFilePath;
	private byte[] attachmentData;
	
	public String[] getToRecipients() {
		return toRecipients;
	}
	public void setToRecipients(String[] toRecipients) {
		this.toRecipients = toRecipients;
	}
	
	public String[] getCcRecipients() {
		return ccRecipients;
	}
	public void setCcRecipients(String[] ccRecipients) {
		this.ccRecipients = ccRecipients;
	}
	public String getSubject() {
		return subject;
	}
	public void setSubject(String subject) {
		this.subject = subject;
	}
	public String getMessageContent() {
		return messageContent;
	}
	public void setMessageContent(String messageContent) {
		this.messageContent = messageContent;
	}
	public String getAttachmentFilePath() {
		return attachmentFilePath;
	}
	public void setAttachmentFilePath(String attachmentFilePath) {
		this.attachmentFilePath = attachmentFilePath;
	}
	public byte[] getAttachmentData() {
		return attachmentData;
	}
	 public void setAttachmentData(byte[] attachmentData, String attachmentFilePath) {
	        this.attachmentData = attachmentData;
	        this.attachmentFilePath = attachmentFilePath;
	    }

	 public void setAttachmentData(InputStream inputStream, String attachmentFilePath) {
		    try {
		        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		        byte[] buffer = new byte[1024];
		        int bytesRead;
		        while ((bytesRead = inputStream.read(buffer)) != -1) {
		            outputStream.write(buffer, 0, bytesRead);
		        }
		        attachmentData = outputStream.toByteArray();
		        this.attachmentFilePath = attachmentFilePath;
		    } catch (IOException e) {
		        e.printStackTrace();
		        // Handle IOException here (e.g., log, display error message, or throw a custom exception)
		    }
		}
}