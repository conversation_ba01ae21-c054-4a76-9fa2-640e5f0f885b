package com.radiant.urfs.exception;

public class BviReturnsException  extends Exception {

	private static final long serialVersionUID = 6057638889793569346L;

	public BviReturnsException() {
		super();
	}

	public BviReturnsException(String message, Throwable cause,
			boolean enableSuppression, boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}

	public BviReturnsException(String message, Throwable cause) {
		super(message, cause);
	}

	public BviReturnsException(String message) {
		super(message);
	}

	public BviReturnsException(Throwable cause) {
		super(cause);
	}
	


}
