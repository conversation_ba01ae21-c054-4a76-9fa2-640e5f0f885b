package com.radiant.urfs.dao.impl;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

public class LogoUtil {

    public static byte[] getLogoAsBytes(String filePath) throws IOException {
        File file = new File(filePath);
        FileInputStream fis = new FileInputStream(file);
        byte[] data = new byte[(int) file.length()];
        fis.read(data);
        fis.close();
        return data;
    }
}
