package com.radiant.urfs.domain;


import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "ENTITY_CERTIFICATION_TBL")
public class CertificationTbl implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ENTITY_CERTIFICATION_ID", nullable = false)
    private int entityCertificationId;

    @ManyToOne
    @JoinColumn(name = "ENTITY_ID", referencedColumnName = "ENTITY_ID", nullable = false)
    private EntityTbl entity;

    @Column(name = "CERTIFICATION_NAME", length = 80)  
    private String certificationName;

    @Column(name = "CERTIFICATION_DATE")
    @Temporal(TemporalType.DATE)
    private Date certificationDate;

    @Column(name = "CERTIFICATION_EXPIRY_DATE")
    @Temporal(TemporalType.DATE)
    private Date certificationExpiryDate;

    @Column(name = "CERTIFICATION_ISSUING_BODY", length = 80)  
    private String certificationIssuingBody;

    @Column(name = "ACTIVE_FLAG", length = 1, nullable = false)
    private char activeFlag;

    @Column(name = "LAST_MODIFIED_DATETIME", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastModifiedDatetime;

    @ManyToOne
    @JoinColumn(name = "LAST_MODIFIED_BY", referencedColumnName = "USER_ID", nullable = false)
    private UserSBTbl lastModifiedBy;

    // Getters and Setters
    public int getEntityCertificationId() {
        return entityCertificationId;
    }

    public void setEntityCertificationId(int entityCertificationId) {
        this.entityCertificationId = entityCertificationId;
    }

    public EntityTbl getEntity() {
        return entity;
    }

    public void setEntity(EntityTbl entity) {
        this.entity = entity;
    }

    public String getCertificationName() {
        return certificationName;
    }

    public void setCertificationName(String certificationName) {
        this.certificationName = certificationName;
    }

    public Date getCertificationDate() {
        return certificationDate;
    }

    public void setCertificationDate(Date certificationDate) {
        this.certificationDate = certificationDate;
    }

    public Date getCertificationExpiryDate() {
        return certificationExpiryDate;
    }

    public void setCertificationExpiryDate(Date certificationExpiryDate) {
        this.certificationExpiryDate = certificationExpiryDate;
    }

    public String getCertificationIssuingBody() {
        return certificationIssuingBody;
    }

    public void setCertificationIssuingBody(String certificationIssuingBody) {
        this.certificationIssuingBody = certificationIssuingBody;
    }

    public char getActiveFlag() {
        return activeFlag;
    }

    public void setActiveFlag(char activeFlag) {
        this.activeFlag = activeFlag;
    }

    public Date getLastModifiedDatetime() {
        return lastModifiedDatetime;
    }

    public void setLastModifiedDatetime(Date lastModifiedDatetime) {
        this.lastModifiedDatetime = lastModifiedDatetime;
    }

	public UserSBTbl getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(UserSBTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}

    
}
