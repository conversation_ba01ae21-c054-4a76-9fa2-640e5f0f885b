package com.radiant.urfs.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "PAYMENT_STATUS_TBL")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentStatusTbl {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PAYMENT_STATUS_ID")
    private Integer paymentStatusId;

    @Column(name = "PAYMENT_STATUS_CODE", nullable = false, length = 20, unique = true)
    private String paymentStatusCode;

    @Column(name = "PAYMENT_STATUS_DESC", nullable = false, length = 120)
    private String paymentStatusDesc;

    @Column(name = "ACTIVE_FLAG", nullable = false, length = 1)
    private String activeFlag;

    @Column(name = "LAST_MODIFIED_DATETIME", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastModifiedDatetime;

    @Column(name = "LAST_MODIFIED_BY", nullable = false)
    private Integer lastModifiedBy;

    @ManyToOne
    @JoinColumn(name = "LAST_MODIFIED_BY", nullable = false, insertable = false, updatable = false)
    private UserSBTbl lastModifiedByUser;

	public Integer getPaymentStatusId() {
		return paymentStatusId;
	}

	public void setPaymentStatusId(Integer paymentStatusId) {
		this.paymentStatusId = paymentStatusId;
	}

	public String getPaymentStatusCode() {
		return paymentStatusCode;
	}

	public void setPaymentStatusCode(String paymentStatusCode) {
		this.paymentStatusCode = paymentStatusCode;
	}

	public String getPaymentStatusDesc() {
		return paymentStatusDesc;
	}

	public void setPaymentStatusDesc(String paymentStatusDesc) {
		this.paymentStatusDesc = paymentStatusDesc;
	}

	public String getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(String activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public Integer getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(Integer lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}

	public UserSBTbl getLastModifiedByUser() {
		return lastModifiedByUser;
	}

	public void setLastModifiedByUser(UserSBTbl lastModifiedByUser) {
		this.lastModifiedByUser = lastModifiedByUser;
	}
    
    
    
}
