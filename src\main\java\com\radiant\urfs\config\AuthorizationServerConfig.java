package com.radiant.urfs.config;

import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenEnhancerChain;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;

//@Configuration
//@EnableAuthorizationServer
//public class AuthorizationServerConfig extends AuthorizationServerConfigurerAdapter {
//
//
//	@Value("${jwt.client_id}")
//	private String CLIEN_ID;
//	
//	@Value("${jwt.client_secret}")
//	private String CLIENT_SECRET; 
//	
//	@Value("${jwt.grant_type}")
//	private String GRANT_TYPE;
//
//	@Value("${jwt.refresh_token}")
//	private String REFRESH_TOKEN ;
//	
//	@Value("${jwt.score_read}")
//	private String SCOPE_READ ;
//	
//	@Value("${jwt.score_write}")
//	private String SCOPE_WRITE ;
//
//
//	@Autowired
//	private TokenStore tokenStore;
//
//	@Autowired
//	private JwtAccessTokenConverter accessTokenConverter;
//
//	@Autowired
//	@Qualifier("authenticationManagerBean")
//	private AuthenticationManager authenticationManager;
//
//	@Autowired
//	private CustomAuthenticationProvider customAuthenticationProvider;
//
//
//	private int ACCESS_TOKEN_VALIDITY_SECONDS =2 * 60 * 60;
//	private static final  int REFRESH_TOKEN_VALIDITY_SECONDS =6 * 60 * 60 ;
//	
//	@Override
//	public void configure(ClientDetailsServiceConfigurer configurer) throws Exception {
//		configurer.inMemory().withClient(CLIEN_ID).secret(CLIENT_SECRET)
//				.authorizedGrantTypes(GRANT_TYPE, REFRESH_TOKEN).scopes(SCOPE_READ, SCOPE_WRITE)
//				.accessTokenValiditySeconds(ACCESS_TOKEN_VALIDITY_SECONDS)
//				.refreshTokenValiditySeconds(REFRESH_TOKEN_VALIDITY_SECONDS);
//	}
//
//	@Override
//	public void configure(AuthorizationServerEndpointsConfigurer configurer) throws Exception {
//		TokenEnhancerChain enhancerChain = new TokenEnhancerChain();
//		configurer.pathMapping("/oauth/token", "/api/v1/user/login");
//		List<TokenEnhancer> list = Arrays.asList(accessTokenConverter);
//		enhancerChain.setTokenEnhancers(list);
//		configurer.tokenStore(tokenStore).accessTokenConverter(accessTokenConverter).tokenEnhancer(enhancerChain);
//		configurer.authenticationManager(authenticationManager);
//	}
//	
//	protected void configure(AuthenticationManagerBuilder auth) throws Exception {
//		auth.authenticationProvider(customAuthenticationProvider);
//	}
//	
//	

//}
