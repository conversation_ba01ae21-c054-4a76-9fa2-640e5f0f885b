package com.radiant.urfs.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "BUSINESS_PURPOSE_TBL")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessPurposeTbl {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "BUSINESS_PURPOSE_ID")
    private Integer businessPurposeId;

    @Column(name = "BUSINESS_PURPOSE_DESC", nullable = false, length = 255)
    private String businessPurposeDesc;

    @Column(name = "ACTIVE_FLAG", nullable = false, length = 1)
    private String activeFlag;

    @Column(name = "LAST_MODIFIED_DATETIME", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastModifiedDatetime;

    @ManyToOne
    @JoinColumn(name = "LAST_MODIFIED_BY", nullable = false)
    private UserSBTbl lastModifiedBy;

	public Integer getBusinessPurposeId() {
		return businessPurposeId;
	}

	public void setBusinessPurposeId(Integer businessPurposeId) {
		this.businessPurposeId = businessPurposeId;
	}

	public String getBusinessPurposeDesc() {
		return businessPurposeDesc;
	}

	public void setBusinessPurposeDesc(String businessPurposeDesc) {
		this.businessPurposeDesc = businessPurposeDesc;
	}

	public String getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(String activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public UserSBTbl getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(UserSBTbl lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}


}