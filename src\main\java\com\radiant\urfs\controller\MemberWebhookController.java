package com.radiant.urfs.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.radiant.urfs.service.BusinessRegistryService;
import com.radiant.urfs.service.UserService;
import com.radiant.urfs.web.model.WebMessage;
import com.radiant.urfs.web.model.WebUser;

@RestController
@CrossOrigin
@RequestMapping("/api/member")
public class MemberWebhookController {

	private static final Logger log = LoggerFactory.getLogger(MemberWebhookController.class);

	@Autowired
	private BusinessRegistryService businessRegistryService;

	@Autowired
	private UserService userService;

	@GetMapping("/ping")
	public ResponseEntity<?> ping() {
		return new ResponseEntity<>(new WebMessage("OK", "member endpoint is reachable"), HttpStatus.OK);
	}

	@PostMapping("/register")
	public ResponseEntity<?> registerMember(@RequestBody WebUser payload) {
		try {
			// Use existing registration flow to create user and sync company
			return userService.registerUser(payload);
		} catch (Exception e) {
			log.error("Error handling member register webhook", e);
			return new ResponseEntity<>(new WebMessage("ERROR", "Failed to process webhook"), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}


