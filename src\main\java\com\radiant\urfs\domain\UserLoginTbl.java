
package com.radiant.urfs.domain;

import static javax.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table /* (name = "USER_LOGIN_TBL", catalog = "dbo") */ 
public class UserLoginTbl implements Serializable {
	/**
	* 
	*/
	private static final long serialVersionUID = 1L;
	private Integer userLoginId;
	private String username;
	private String password;
	private String admin;
	private String manager;
	private UserTbl userTblByUserId;
	private Date lastLoginDate;
	private Date lastPwdChangeDate;
	private Date lastModifiedDatetime;
	private String telephone;

	@Column(name = "EMAIL")
	private String email;
	private char activeFlag;
	private Role roles;
	private UserTbl userTblByLastModifiedBy;
	private String typeOfReturn;
	private String resetPasswordToken;

	@Id

	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "USER_LOGIN_ID", unique = true, nullable = true)
	public Integer getUserLoginId() {
		return userLoginId;
	}

	public void setUserLoginId(Integer userLoginId) {
		this.userLoginId = userLoginId;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	@ManyToOne(fetch = FetchType.LAZY)

	@JoinColumn(name = "ROLE_ID")
	public Role getRoles() {
		return roles;
	}

	public void setRoles(Role roles) {
		this.roles = roles;
	}

	@Column(name = "ACTIVE_FLAG", length = 1)
	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	@ManyToOne(fetch = FetchType.LAZY)

	@JoinColumn(name = "LAST_MODIFIED_BY")
	public UserTbl getUserTblByLastModifiedBy() {
		return userTblByLastModifiedBy;
	}

	public void setUserTblByLastModifiedBy(UserTbl userTblByLastModifiedBy) {
		this.userTblByLastModifiedBy = userTblByLastModifiedBy;
	}

	@ManyToOne(fetch = FetchType.LAZY)

	@JoinColumn(name = "USER_ID")
	public UserTbl getUserTblByUserId() {
		return userTblByUserId;
	}

	public void setUserTblByUserId(UserTbl userTblByUserId) {
		this.userTblByUserId = userTblByUserId;
	}

	@Temporal(TemporalType.TIMESTAMP)

	@Column(name = "LAST_LOGIN_DATE", length = 23)
	public Date getLastLoginDate() {
		return lastLoginDate;
	}

	public void setLastLoginDate(Date lastLoginDate) {
		this.lastLoginDate = lastLoginDate;
	}

	@Temporal(TemporalType.TIMESTAMP)

	@Column(name = "LAST_PWD_CHANGE_DATE", length = 23)
	public Date getLastPwdChangeDate() {
		return lastPwdChangeDate;
	}

	public void setLastPwdChangeDate(Date lastPwdChangeDate) {
		this.lastPwdChangeDate = lastPwdChangeDate;
	}

	@Temporal(TemporalType.TIMESTAMP)

	@Column(name = "LAST_MODIFIED_DATETIME", nullable = false, length = 23)
	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public String getAdmin() {
		return admin;
	}

	public void setAdmin(String admin) {
		this.admin = admin;
	}

	public String getManager() {
		return manager;
	}

	public void setManager(String manager) {
		this.manager = manager;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	@Column(name = "TELEPHONE", nullable = false, length = 20)
	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	@Column(name = "TYPE_OF_RETURN", nullable = true, length = 25)
	public String getTypeOfReturn() {
		return typeOfReturn;
	}

	public void setTypeOfReturn(String typeOfReturn) {
		this.typeOfReturn = typeOfReturn;
	}

//	@Column(name = "RESET_PASSWORD_TOKEN", nullable = true, length = 30)
//	public String getResetPasswordToken() {
//		return resetPasswordToken;
//	}
//
//	public void setResetPasswordToken(String resetPasswordToken) {
//		this.resetPasswordToken = resetPasswordToken;
//	}
}
