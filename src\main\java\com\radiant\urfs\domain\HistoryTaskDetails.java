package com.radiant.urfs.domain;

public class HistoryTaskDetails {
	private String taskId;
	private String name;
	private String executionId;
	private String procInstId;
	private String startDate;
	private String endDate;
	private String deleteReason;
	private String assignee;
	private char isApprovedBy;
	private String status;
	private String inspectorComments;
	private String managerComments;
	private UserDTO userDTO;

	public HistoryTaskDetails(String taskId, String name, String executionId, String procInstId, String startDate, String endDate, String deleteReason, String assignee, String inspectorComments, String managerComments, char isApprovedBy, String status, UserDTO userDTO) {
		super();
		this.taskId = taskId;
		this.name = name;
		this.executionId = executionId;
		this.procInstId = procInstId;
		this.startDate = startDate;
		this.endDate = endDate;
		this.deleteReason = deleteReason;
		this.assignee = assignee;
		this.isApprovedBy = isApprovedBy;
		this.status = status;
		this.inspectorComments = inspectorComments;
		this.managerComments = managerComments;
		this.userDTO = userDTO;
		
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getExecutionId() {
		return executionId;
	}

	public void setExecutionId(String executionId) {
		this.executionId = executionId;
	}

	public String getProcInstId() {
		return procInstId;
	}

	public void setProcInstId(String procInstId) {
		this.procInstId = procInstId;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getDeleteReason() {
		return deleteReason;
	}

	public void setDeleteReason(String deleteReason) {
		this.deleteReason = deleteReason;
	}

	public String getAssignee() {
		return assignee;
	}

	public void setAssignee(String assignee) {
		this.assignee = assignee;
	}

	public char getIsApprovedBy() {
		return isApprovedBy;
	}

	public void setIsApprovedBy(char isApprovedBy) {
		this.isApprovedBy = isApprovedBy;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public UserDTO getUserDTO() {
		return userDTO;
	}

	public void setUserDTO(UserDTO userDTO) {
		this.userDTO = userDTO;
	}

	public String getInspectorComments() {
		return inspectorComments;
	}

	public void setInspectorComments(String inspectorComments) {
		this.inspectorComments = inspectorComments;
	}

	public String getManagerComments() {
		return managerComments;
	}

	public void setManagerComments(String managerComments) {
		this.managerComments = managerComments;
	}

}
