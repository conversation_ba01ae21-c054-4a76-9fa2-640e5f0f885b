package com.radiant.urfs.domain;

import java.util.List;

public class LicenceTaskDetails {
	
	private String taskId;
	private String taskName;
	private String createDate;
	private String executionId;
	private String procInstId;
	private LicenceDTO licenceDTO;
	private List<LicenceDTO> licenceDTOList;
	private String status;
	private int amlcftRecords;
	public LicenceTaskDetails() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	
	
	public LicenceTaskDetails(String taskId, String taskName, String createDate, String executionId, String procInstId,
			LicenceDTO licenceDTO) {
		super();
		this.taskId = taskId;
		this.taskName = taskName;
		this.createDate = createDate;
		this.executionId = executionId;
		this.procInstId = procInstId;
		this.licenceDTO = licenceDTO;
	}

	public LicenceTaskDetails(String taskId, String taskName, String createDate, String executionId, String procInstId,
			LicenceDTO licenceDTO, String status) {
		super();
		this.taskId = taskId;
		this.taskName = taskName;
		this.createDate = createDate;
		this.executionId = executionId;
		this.procInstId = procInstId;
		this.licenceDTO = licenceDTO;
		this.status = status;
	}


	public String getTaskId() {
		return taskId;
	}
	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}
	public String getTaskName() {
		return taskName;
	}
	public void setTaskName(String taskName) {
		this.taskName = taskName;
	}
	public String getCreateDate() {
		return createDate;
	}
	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}
	public String getExecutionId() {
		return executionId;
	}
	public void setExecutionId(String executionId) {
		this.executionId = executionId;
	}
	public String getProcInstId() {
		return procInstId;
	}
	public void setProcInstId(String procInstId) {
		this.procInstId = procInstId;
	}
	public LicenceDTO getLicenceDTO() {
		return licenceDTO;
	}
	public void setLicenceDTO(LicenceDTO licenceDTO) {
		this.licenceDTO = licenceDTO;
	}

	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}

	public List<LicenceDTO> getLicenceDTOList() {
		return licenceDTOList;
	}
	public void setLicenceDTOList(List<LicenceDTO> licenceDTOList) {
		this.licenceDTOList = licenceDTOList;
	}



	public int getAmlcftRecords() {
		return amlcftRecords;
	}
	public void setAmlcftRecords(int amlcftRecords) {
		this.amlcftRecords = amlcftRecords;
	}
	
	

}
