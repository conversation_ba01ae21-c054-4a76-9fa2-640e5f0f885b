package com.radiant.urfs.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "PRODUCT_PRICING_TBL")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductPricingTbl {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PRODUCT_PRICING_ID")
    private Integer productPricingId;

    @Column(name = "PRODUCT_NUMBER", nullable = false, length = 20, unique = true)
    private String productNumber;

    @Column(name = "PRODUCT_NAME", nullable = false, length = 500, unique = true)
    private String productName;

    @Column(name = "PRICE", nullable = false, precision = 18, scale = 2)
    private BigDecimal price;

    
    @Column(name = "ACTIVE_FLAG", nullable = false, length = 1)
    private char activeFlag;

    @Column(name = "LAST_MODIFIED_DATETIME", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastModifiedDatetime;

    @ManyToOne
    @JoinColumn(name = "LAST_MODIFIED_BY", nullable = false)
    private UserSBTbl lastModifiedByUser;

	public Integer getProductPricingId() {
		return productPricingId;
	}

	public void setProductPricingId(Integer productPricingId) {
		this.productPricingId = productPricingId;
	}

	public String getProductNumber() {
		return productNumber;
	}

	public void setProductNumber(String productNumber) {
		this.productNumber = productNumber;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	

	public char getActiveFlag() {
		return activeFlag;
	}

	public void setActiveFlag(char activeFlag) {
		this.activeFlag = activeFlag;
	}

	public Date getLastModifiedDatetime() {
		return lastModifiedDatetime;
	}

	public void setLastModifiedDatetime(Date lastModifiedDatetime) {
		this.lastModifiedDatetime = lastModifiedDatetime;
	}

	public UserSBTbl getLastModifiedByUser() {
		return lastModifiedByUser;
	}

	public void setLastModifiedByUser(UserSBTbl lastModifiedByUser) {
		this.lastModifiedByUser = lastModifiedByUser;
	}


}